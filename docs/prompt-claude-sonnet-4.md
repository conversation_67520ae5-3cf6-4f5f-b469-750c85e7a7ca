# Prompt para Claude Sonnet 4 - Implementação do Checkout Dinâmico

## Contexto do Projeto

Você está trabalhando em um sistema de checkout dinâmico para uma plataforma de pagamentos. O sistema já possui:

- **Estrutura de configurações**: `CheckoutSettingsData` interface completa
- **Sistema de upload**: API `/api/uploads/signed-upload-url`, hooks `useFileUpload`
- **Buckets configurados**: `checkoutBanners`, `testimonialAvatars`, `products`
- **Página de configuração**: `UnifiedCheckoutSettings` já implementada
- **Página de checkout**: `apps/web/app/(checkout)/checkout/[productId]/page.tsx` precisa ser atualizada

## Objetivo

Implementar um sistema de checkout completamente dinâmico onde todas as configurações feitas na página de configuração do produto sejam refletidas automaticamente na página de checkout, incluindo uploads de imagens.

## Estrutura de Dados Existente

```typescript
interface CheckoutSettingsData {
  banner: {
    enabled: boolean;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };
  header: {
    showLogo: boolean;
    logoUrl?: string;
    companyName: string;
  };
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: string;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  guaranteeCards: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}
```

## Tarefas Específicas

### 1. Criar Componentes de Renderização

Crie os seguintes componentes em `apps/web/app/(checkout)/checkout/components/`:

#### A. CheckoutBanner
- Props: `settings: { enabled: boolean; url: string | null; maxHeight: string; borderRadius: string; shadow: boolean; }`
- Renderizar banner se habilitado e URL disponível
- Aplicar estilos dinâmicos (altura, bordas, sombra)

#### B. CheckoutHeader  
- Props: `settings: { showLogo: boolean; logoUrl: string | null; companyName: string; }`
- Mostrar logo se habilitado
- Exibir nome da empresa

#### C. UrgencyBar
- Props: `settings: { enabled: boolean; message: string; endTime?: Date; backgroundColor: string; textColor: string; accentColor: string; }`
- Contador regressivo em tempo real
- Aplicar cores dinâmicas

#### D. TrustBadges
- Props: `settings: { enabled: boolean; badges: Array<...>; layout: 'horizontal' | 'vertical' | 'grid'; showDescriptions: boolean; backgroundColor: string; textColor: string; borderColor: string; }`
- Renderizar badges habilitados
- Layout responsivo baseado na configuração
- Ícones dinâmicos (shield, check, mail, etc.)

#### E. GuaranteeCards
- Props: `settings: { enabled: boolean; cards: Array<...>; layout: 'horizontal' | 'vertical' | 'grid'; backgroundColor: string; textColor: string; borderColor: string; }`
- Cards ordenados por `order`
- Suporte a ícones customizados
- Layout responsivo

#### F. ScarcityIndicator
- Props: `settings: { enabled: boolean; totalStock: number; soldCount: number; message: string; variant: 'warning' | 'danger' | 'info'; showIcon: boolean; backgroundColor: string; textColor: string; borderColor: string; }`
- Barra de progresso visual
- Cálculo automático de vagas restantes
- Variantes de cor

#### G. Testimonials
- Props: `settings: { enabled: boolean; testimonials: Array<...>; maxTestimonials: number; autoPlay: boolean; autoPlayInterval: number; showControls: boolean; showStars: boolean; showAvatars: boolean; backgroundColor: string; textColor: string; borderColor: string; }`
- Carrossel automático
- Controles de navegação
- Avatares e estrelas opcionais

#### H. CheckoutSidebar
- Props: `settings: { enabled: boolean; bannerUrl?: string; title: string; content: string; backgroundColor: string; textColor: string; borderColor: string; borderRadius: string; shadow: boolean; }`
- Banner opcional
- Conteúdo personalizado

### 2. Criar Componentes de Upload

#### A. TestimonialAvatarUpload
- Hook: `useFileUpload` com bucket `'testimonialAvatars'`
- Preview da imagem atual
- Upload de novos avatares

#### B. SidebarBannerUpload  
- Hook: `useFileUpload` com bucket `'checkoutBanners'`
- Preview do banner atual
- Upload de novos banners

### 3. Atualizar Página de Checkout

Modifique `apps/web/app/(checkout)/checkout/[productId]/page.tsx`:

```typescript
// Parsear todas as configurações com fallbacks
const checkoutSettings = product.settings as CheckoutSettingsData || {};

const bannerSettings = {
  enabled: checkoutSettings.banner?.enabled || false,
  maxHeight: checkoutSettings.banner?.maxHeight || '300px',
  borderRadius: checkoutSettings.banner?.borderRadius || 'rounded-lg',
  shadow: checkoutSettings.banner?.shadow || true,
  url: checkoutSettings.banner?.url || null,
};

// ... (parsear todas as outras configurações)

return (
  <div className='min-h-screen'>
    <CheckoutHeader settings={headerSettings} />
    <UrgencyBar settings={urgencySettings} />
    
    <div className='mx-3 md:mx-auto md:container py-5'>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <CheckoutBanner settings={bannerSettings} />
          <TrustBadges settings={trustBadgesSettings} />
          <GuaranteeCards settings={guaranteeCardsSettings} />
          <ScarcityIndicator settings={scarcitySettings} />
          <Testimonials settings={testimonialsSettings} />
          <CheckoutForm product={...} />
        </div>
        <div className="lg:col-span-1">
          <CheckoutSidebar settings={sidebarSettings} />
        </div>
      </div>
    </div>
  </div>
);
```

### 4. Atualizar UnifiedCheckoutSettings

Adicione uploads de imagens nas configurações:

- **Sidebar**: Upload de banner da sidebar
- **Testimonials**: Upload de avatar para cada depoimento
- **Header**: Upload de logo (se necessário)

### 5. Atualizar TestimonialsEditor

Integre upload de avatar:

```typescript
// Para cada depoimento, adicionar:
<TestimonialAvatarUpload
  currentAvatar={testimonial.avatar}
  onAvatarChange={(url) => updateTestimonial(index, 'avatar', url)}
/>
```

## Regras Técnicas

1. **TypeScript**: Use interfaces bem definidas
2. **Tailwind CSS**: Aplicar classes dinâmicas baseadas nas configurações
3. **Responsividade**: Layout mobile-first
4. **Performance**: Lazy loading para imagens
5. **Acessibilidade**: Labels e ARIA apropriados
6. **Fallbacks**: Valores padrão para todas as configurações

## Estrutura de Arquivos

```
apps/web/app/(checkout)/checkout/
├── components/
│   ├── checkout-banner.tsx
│   ├── checkout-header.tsx
│   ├── urgency-bar.tsx
│   ├── trust-badges.tsx
│   ├── guarantee-cards.tsx
│   ├── scarcity-indicator.tsx
│   ├── testimonials.tsx
│   ├── checkout-sidebar.tsx
│   ├── testimonial-avatar-upload.tsx
│   └── sidebar-banner-upload.tsx
└── [productId]/
    └── page.tsx (atualizar)
```

## Hooks e APIs Disponíveis

- `useFileUpload({ bucket, onSuccess, onError })` - Upload de arquivos
- `/api/uploads/signed-upload-url` - Obter URL de upload
- `db.product.findFirst()` - Buscar produto e configurações

## Resultado Esperado

Sistema de checkout completamente dinâmico onde:
- ✅ Todas as configurações são refletidas automaticamente
- ✅ Uploads de imagens funcionam para todos os elementos
- ✅ Layout responsivo e acessível
- ✅ Fallbacks para configurações não definidas
- ✅ Performance otimizada

Implemente todos os componentes e atualizações necessárias seguindo as especificações acima.
