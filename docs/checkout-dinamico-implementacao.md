# Implementação do Checkout Dinâmico - Documento Técnico

## Visão Geral

Este documento descreve a implementação completa do sistema de checkout dinâmico, onde todas as configurações feitas na página de configuração do produto são refletidas dinamicamente na página de checkout (`apps/web/app/(checkout)/checkout/[productId]/page.tsx`).

## Arquitetura Atual

### 1. Estrutura de Dados

O sistema já possui uma estrutura robusta para configurações de checkout:

```typescript
interface CheckoutSettingsData {
  // Banner (usando upload, sem URL manual)
  banner: {
    enabled: boolean;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };

  // Header
  header: {
    showLogo: boolean;
    logoUrl?: string;
    companyName: string;
  };

  // Urgência
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: string;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };

  // Confiança
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Cards de Garantia
  guaranteeCards: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Escassez
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Depoimentos
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Sidebar
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}
```

### 2. Sistema de Upload de Imagens

O sistema já possui infraestrutura completa para upload:

- **Buckets configurados**: `checkoutBanners`, `testimonialAvatars`, `products`
- **API de upload**: `/api/uploads/signed-upload-url`
- **Hooks**: `useFileUpload` para upload de arquivos
- **Componentes**: `BannerUpload` para upload de banners

## Implementação Necessária

### 1. Atualizar a Página de Checkout

O arquivo `apps/web/app/(checkout)/checkout/[productId]/page.tsx` precisa ser atualizado para:

#### A. Carregar todas as configurações do produto
```typescript
// Buscar configurações completas do produto
const product = await db.product.findFirst({
  where: {
    id: productId,
    status: {
      in: ['PUBLISHED', 'DRAFT']
    },
  },
  select: {
    id: true,
    name: true,
    description: true,
    priceCents: true,
    thumbnail: true,
    checkoutType: true,
    settings: true, // Todas as configurações
  },
});
```

#### B. Parsear configurações com fallbacks
```typescript
const checkoutSettings = product.settings as CheckoutSettingsData || {};

// Banner settings
const bannerSettings = {
  enabled: checkoutSettings.banner?.enabled || false,
  maxHeight: checkoutSettings.banner?.maxHeight || '300px',
  borderRadius: checkoutSettings.banner?.borderRadius || 'rounded-lg',
  shadow: checkoutSettings.banner?.shadow || true,
  url: checkoutSettings.banner?.url || null,
};

// Header settings
const headerSettings = {
  showLogo: checkoutSettings.header?.showLogo || false,
  logoUrl: checkoutSettings.header?.logoUrl || null,
  companyName: checkoutSettings.header?.companyName || 'SupGateway',
};

// Urgency settings
const urgencySettings = {
  enabled: checkoutSettings.urgency?.enabled || false,
  message: checkoutSettings.urgency?.message || 'Esta oferta se encerra em:',
  endTime: checkoutSettings.urgency?.endTime ? new Date(checkoutSettings.urgency.endTime) : undefined,
  backgroundColor: checkoutSettings.urgency?.backgroundColor || 'bg-red-50',
  textColor: checkoutSettings.urgency?.textColor || 'text-white',
  accentColor: checkoutSettings.urgency?.accentColor || 'bg-red-600',
};

// Trust badges settings
const trustBadgesSettings = {
  enabled: checkoutSettings.trustBadges?.enabled || true,
  badges: checkoutSettings.trustBadges?.badges || [
    {
      id: 'security',
      title: '100% Seguro',
      subtitle: 'Pagamentos protegidos',
      icon: 'shield',
      enabled: true,
    },
    // ... outros badges padrão
  ],
  layout: checkoutSettings.trustBadges?.layout || 'vertical',
  showDescriptions: checkoutSettings.trustBadges?.showDescriptions || true,
  backgroundColor: checkoutSettings.trustBadges?.backgroundColor || 'bg-blue-50',
  textColor: checkoutSettings.trustBadges?.textColor || 'text-blue-800',
  borderColor: checkoutSettings.trustBadges?.borderColor || 'border-blue-200',
};

// Guarantee cards settings
const guaranteeCardsSettings = {
  enabled: checkoutSettings.guaranteeCards?.enabled || true,
  cards: checkoutSettings.guaranteeCards?.cards || [
    {
      id: 'security',
      title: '100% Seguro',
      description: 'Pagamentos protegidos com criptografia',
      icon: 'shield',
      enabled: true,
      order: 1,
    },
    // ... outros cards padrão
  ],
  layout: checkoutSettings.guaranteeCards?.layout || 'grid',
  backgroundColor: checkoutSettings.guaranteeCards?.backgroundColor || 'bg-green-50',
  textColor: checkoutSettings.guaranteeCards?.textColor || 'text-green-800',
  borderColor: checkoutSettings.guaranteeCards?.borderColor || 'border-green-200',
};

// Scarcity settings
const scarcitySettings = {
  enabled: checkoutSettings.scarcity?.enabled || false,
  totalStock: checkoutSettings.scarcity?.totalStock || 100,
  soldCount: checkoutSettings.scarcity?.soldCount || 0,
  message: checkoutSettings.scarcity?.message || 'Apenas {remaining} vagas restantes!',
  variant: checkoutSettings.scarcity?.variant || 'warning',
  showIcon: checkoutSettings.scarcity?.showIcon || true,
  backgroundColor: checkoutSettings.scarcity?.backgroundColor || 'bg-orange-50',
  textColor: checkoutSettings.scarcity?.textColor || 'text-orange-800',
  borderColor: checkoutSettings.scarcity?.borderColor || 'border-orange-200',
};

// Testimonials settings
const testimonialsSettings = {
  enabled: checkoutSettings.testimonials?.enabled || true,
  testimonials: checkoutSettings.testimonials?.testimonials || [
    {
      id: '1',
      name: 'Carlos Silva',
      rating: 5,
      comment: 'A integração foi surpreendentemente simples.',
      location: 'São Paulo, SP',
      verified: true,
    },
    // ... outros depoimentos padrão
  ],
  maxTestimonials: checkoutSettings.testimonials?.maxTestimonials || 3,
  autoPlay: checkoutSettings.testimonials?.autoPlay || true,
  autoPlayInterval: checkoutSettings.testimonials?.autoPlayInterval || 5000,
  showControls: checkoutSettings.testimonials?.showControls || true,
  showStars: checkoutSettings.testimonials?.showStars || true,
  showAvatars: checkoutSettings.testimonials?.showAvatars || true,
  backgroundColor: checkoutSettings.testimonials?.backgroundColor || 'bg-gray-50',
  textColor: checkoutSettings.testimonials?.textColor || 'text-gray-800',
  borderColor: checkoutSettings.testimonials?.borderColor || 'border-gray-200',
};

// Sidebar settings
const sidebarSettings = {
  enabled: checkoutSettings.sidebar?.enabled || false,
  bannerUrl: checkoutSettings.sidebar?.bannerUrl || null,
  title: checkoutSettings.sidebar?.title || 'Informações Importantes',
  content: checkoutSettings.sidebar?.content || 'Adicione informações úteis para seus clientes aqui.',
  backgroundColor: checkoutSettings.sidebar?.backgroundColor || 'bg-blue-50',
  textColor: checkoutSettings.sidebar?.textColor || 'text-blue-800',
  borderColor: checkoutSettings.sidebar?.borderColor || 'border-blue-200',
  borderRadius: checkoutSettings.sidebar?.borderRadius || 'rounded-lg',
  shadow: checkoutSettings.sidebar?.shadow || true,
};
```

### 2. Componentes de Renderização

Criar componentes específicos para cada elemento:

#### A. Banner Component
```typescript
// components/checkout-banner.tsx
interface CheckoutBannerProps {
  settings: {
    enabled: boolean;
    url: string | null;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };
}

export function CheckoutBanner({ settings }: CheckoutBannerProps) {
  if (!settings.enabled || !settings.url) return null;

  return (
    <div className={`w-full ${settings.borderRadius} ${settings.shadow ? 'shadow-lg' : ''} overflow-hidden mb-6`}>
      <img
        src={settings.url}
        alt="Banner do Checkout"
        className="w-full object-cover"
        style={{ maxHeight: settings.maxHeight }}
      />
    </div>
  );
}
```

#### B. Header Component
```typescript
// components/checkout-header.tsx
interface CheckoutHeaderProps {
  settings: {
    showLogo: boolean;
    logoUrl: string | null;
    companyName: string;
  };
}

export function CheckoutHeader({ settings }: CheckoutHeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {settings.showLogo && settings.logoUrl && (
          <img
            src={settings.logoUrl}
            alt={settings.companyName}
            className="h-8 w-auto"
          />
        )}
        <h1 className="text-xl font-semibold text-gray-900">
          {settings.companyName}
        </h1>
      </div>
    </header>
  );
}
```

#### C. Urgency Bar Component
```typescript
// components/urgency-bar.tsx
interface UrgencyBarProps {
  settings: {
    enabled: boolean;
    message: string;
    endTime?: Date;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };
}

export function UrgencyBar({ settings }: UrgencyBarProps) {
  if (!settings.enabled) return null;

  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (!settings.endTime) return;

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const end = settings.endTime!.getTime();
      const difference = end - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft(`${days}d ${hours}h ${minutes}m ${seconds}s`);
      } else {
        setTimeLeft('Oferta encerrada!');
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [settings.endTime]);

  return (
    <div className={`${settings.backgroundColor} ${settings.textColor} py-3 px-4 text-center`}>
      <div className="max-w-7xl mx-auto">
        <p className="text-sm font-medium">
          {settings.message} <span className={`${settings.accentColor} text-white px-2 py-1 rounded`}>{timeLeft}</span>
        </p>
      </div>
    </div>
  );
}
```

#### D. Trust Badges Component
```typescript
// components/trust-badges.tsx
interface TrustBadgesProps {
  settings: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

export function TrustBadges({ settings }: TrustBadgesProps) {
  if (!settings.enabled) return null;

  const enabledBadges = settings.badges.filter(badge => badge.enabled);

  const getLayoutClasses = () => {
    switch (settings.layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-4';
      case 'vertical':
        return 'space-y-2';
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-3 gap-4';
      default:
        return 'space-y-2';
    }
  };

  return (
    <div className={`${settings.backgroundColor} ${settings.borderColor} border rounded-lg p-4 mb-6`}>
      <div className={getLayoutClasses()}>
        {enabledBadges.map((badge) => (
          <div key={badge.id} className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {/* Renderizar ícone baseado no tipo */}
              {badge.icon === 'shield' && <Shield className="h-6 w-6" />}
              {badge.icon === 'check' && <Check className="h-6 w-6" />}
              {badge.icon === 'mail' && <Mail className="h-6 w-6" />}
            </div>
            <div>
              <p className={`${settings.textColor} font-medium text-sm`}>
                {badge.title}
              </p>
              {settings.showDescriptions && (
                <p className={`${settings.textColor} text-xs opacity-75`}>
                  {badge.subtitle}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

#### E. Guarantee Cards Component
```typescript
// components/guarantee-cards.tsx
interface GuaranteeCardsProps {
  settings: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

export function GuaranteeCards({ settings }: GuaranteeCardsProps) {
  if (!settings.enabled) return null;

  const enabledCards = settings.cards
    .filter(card => card.enabled)
    .sort((a, b) => a.order - b.order);

  const getLayoutClasses = () => {
    switch (settings.layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-4';
      case 'vertical':
        return 'space-y-4';
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-3 gap-4';
      default:
        return 'space-y-4';
    }
  };

  return (
    <div className={`${settings.backgroundColor} ${settings.borderColor} border rounded-lg p-6 mb-6`}>
      <div className={getLayoutClasses()}>
        {enabledCards.map((card) => (
          <div key={card.id} className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {card.customIcon ? (
                <img src={card.customIcon} alt={card.title} className="h-6 w-6" />
              ) : (
                // Renderizar ícone baseado no tipo
                {card.icon === 'shield' && <Shield className="h-6 w-6" />}
                {card.icon === 'check' && <Check className="h-6 w-6" />}
                {card.icon === 'heart' && <Heart className="h-6 w-6" />}
              )}
            </div>
            <div>
              <h3 className={`${settings.textColor} font-semibold text-sm`}>
                {card.title}
              </h3>
              <p className={`${settings.textColor} text-xs opacity-75 mt-1`}>
                {card.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

#### F. Scarcity Component
```typescript
// components/scarcity-indicator.tsx
interface ScarcityIndicatorProps {
  settings: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

export function ScarcityIndicator({ settings }: ScarcityIndicatorProps) {
  if (!settings.enabled) return null;

  const remaining = settings.totalStock - settings.soldCount;
  const percentage = (settings.soldCount / settings.totalStock) * 100;

  const getVariantClasses = () => {
    switch (settings.variant) {
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'danger':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-orange-50 border-orange-200 text-orange-800';
    }
  };

  return (
    <div className={`${settings.backgroundColor} ${settings.borderColor} border rounded-lg p-4 mb-6`}>
      <div className="flex items-center space-x-3">
        {settings.showIcon && (
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5" />
          </div>
        )}
        <div className="flex-1">
          <p className={`${settings.textColor} text-sm font-medium`}>
            {settings.message.replace('{remaining}', remaining.toString())}
          </p>
          <div className="mt-2 bg-gray-200 rounded-full h-2">
            <div
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${percentage}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
```

#### G. Testimonials Component
```typescript
// components/testimonials.tsx
interface TestimonialsProps {
  settings: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

export function Testimonials({ settings }: TestimonialsProps) {
  if (!settings.enabled) return null;

  const [currentIndex, setCurrentIndex] = useState(0);
  const displayedTestimonials = settings.testimonials.slice(0, settings.maxTestimonials);

  useEffect(() => {
    if (!settings.autoPlay || displayedTestimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
    }, settings.autoPlayInterval);

    return () => clearInterval(interval);
  }, [settings.autoPlay, settings.autoPlayInterval, displayedTestimonials.length]);

  const currentTestimonial = displayedTestimonials[currentIndex];

  return (
    <div className={`${settings.backgroundColor} ${settings.borderColor} border rounded-lg p-6 mb-6`}>
      <div className="text-center">
        <div className="flex justify-center mb-4">
          {settings.showStars && (
            <div className="flex space-x-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-5 w-5 ${
                    i < currentTestimonial.rating ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                  fill={i < currentTestimonial.rating ? 'currentColor' : 'none'}
                />
              ))}
            </div>
          )}
        </div>

        <blockquote className={`${settings.textColor} text-lg italic mb-4`}>
          "{currentTestimonial.comment}"
        </blockquote>

        <div className="flex items-center justify-center space-x-3">
          {settings.showAvatars && currentTestimonial.avatar && (
            <img
              src={currentTestimonial.avatar}
              alt={currentTestimonial.name}
              className="h-10 w-10 rounded-full"
            />
          )}
          <div>
            <p className={`${settings.textColor} font-semibold`}>
              {currentTestimonial.name}
            </p>
            {currentTestimonial.location && (
              <p className={`${settings.textColor} text-sm opacity-75`}>
                {currentTestimonial.location}
              </p>
            )}
          </div>
          {currentTestimonial.verified && (
            <CheckCircle className="h-5 w-5 text-green-500" />
          )}
        </div>

        {settings.showControls && displayedTestimonials.length > 1 && (
          <div className="flex justify-center space-x-2 mt-4">
            {displayedTestimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
```

#### H. Sidebar Component
```typescript
// components/checkout-sidebar.tsx
interface CheckoutSidebarProps {
  settings: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}

export function CheckoutSidebar({ settings }: CheckoutSidebarProps) {
  if (!settings.enabled) return null;

  return (
    <div className={`${settings.backgroundColor} ${settings.borderColor} ${settings.borderRadius} border p-6 ${settings.shadow ? 'shadow-lg' : ''}`}>
      {settings.bannerUrl && (
        <div className="mb-4">
          <img
            src={settings.bannerUrl}
            alt="Sidebar Banner"
            className="w-full h-32 object-cover rounded"
          />
        </div>
      )}
      
      <h3 className={`${settings.textColor} font-semibold text-lg mb-3`}>
        {settings.title}
      </h3>
      
      <div className={`${settings.textColor} text-sm leading-relaxed`}>
        {settings.content}
      </div>
    </div>
  );
}
```

### 3. Atualizar a Página de Checkout

```typescript
// apps/web/app/(checkout)/checkout/[productId]/page.tsx
export default async function CheckoutPage({ params, searchParams }: CheckoutPageProps) {
  const { productId } = await params;
  const { offer: offerId, org: organizationSlug, quick } = await searchParams;

  if (!productId) {
    redirect('/');
  }

  try {
    const product = await db.product.findFirst({
      where: {
        id: productId,
        status: {
          in: ['PUBLISHED', 'DRAFT']
        },
      },
    });

    if (!product) {
      redirect('/');
    }

    // Parsear todas as configurações
    const checkoutSettings = product.settings as CheckoutSettingsData || {};
    
    // ... (todas as configurações parseadas como mostrado acima)

    return (
      <div className='min-h-screen'>
        {/* Header */}
        <CheckoutHeader settings={headerSettings} />

        {/* Urgency Bar */}
        <UrgencyBar settings={urgencySettings} />

        <div className='mx-3 md:mx-auto md:container py-5'>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Conteúdo Principal */}
            <div className="lg:col-span-2 space-y-6">
              {/* Banner */}
              <CheckoutBanner settings={bannerSettings} />

              {/* Trust Badges */}
              <TrustBadges settings={trustBadgesSettings} />

              {/* Guarantee Cards */}
              <GuaranteeCards settings={guaranteeCardsSettings} />

              {/* Scarcity Indicator */}
              <ScarcityIndicator settings={scarcitySettings} />

              {/* Testimonials */}
              <Testimonials settings={testimonialsSettings} />

              {/* Checkout Form */}
              <CheckoutForm
                product={{
                  id: product.id,
                  title: product.name,
                  description: product.description,
                  type: product.type === 'MENTORSHIP' ? 'MENTORING' : product.type as 'COURSE' | 'EBOOK' | 'MENTORING',
                  price: Number(product.priceCents) / 100,
                  installmentsLimit: 12,
                  enableInstallments: true,
                  thumbnail: product.thumbnail,
                  checkoutType: product.checkoutType,
                  acceptedPayments: ['CREDIT_CARD', 'PIX'],
                  checkoutSettings: product.settings,
                  customCheckoutUrl: null,
                  successUrl: null,
                  cancelUrl: null,
                  termsUrl: null,
                  offers: offers?.map((o) => ({
                    id: o.id,
                    title: o.name,
                    description: null,
                    price: Number(o.valueCents) / 100,
                    type: o.type,
                  })),
                }}
              />
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <CheckoutSidebar settings={sidebarSettings} />
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading product for checkout:', error);
    redirect('/');
  }
}
```

### 4. Sistema de Upload para Imagens

#### A. Avatar Upload para Testimonials
```typescript
// components/testimonial-avatar-upload.tsx
interface TestimonialAvatarUploadProps {
  currentAvatar?: string;
  onAvatarChange: (avatarUrl: string | null) => void;
}

export function TestimonialAvatarUpload({ currentAvatar, onAvatarChange }: TestimonialAvatarUploadProps) {
  const { uploadFile, isUploading } = useFileUpload({
    bucket: 'testimonialAvatars',
    onSuccess: (url) => onAvatarChange(url),
    onError: (error) => console.error('Upload error:', error),
  });

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    await uploadFile(file);
  };

  return (
    <div className="space-y-4">
      <Label>Avatar do Depoimento</Label>
      <div className="flex items-center space-x-4">
        {currentAvatar && (
          <img
            src={currentAvatar}
            alt="Avatar atual"
            className="h-16 w-16 rounded-full object-cover"
          />
        )}
        <div>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            disabled={isUploading}
            className="hidden"
            id="avatar-upload"
          />
          <Label htmlFor="avatar-upload" className="cursor-pointer">
            {isUploading ? 'Fazendo upload...' : 'Selecionar Avatar'}
          </Label>
        </div>
      </div>
    </div>
  );
}
```

#### B. Sidebar Banner Upload
```typescript
// components/sidebar-banner-upload.tsx
interface SidebarBannerUploadProps {
  currentBanner?: string;
  onBannerChange: (bannerUrl: string | null) => void;
}

export function SidebarBannerUpload({ currentBanner, onBannerChange }: SidebarBannerUploadProps) {
  const { uploadFile, isUploading } = useFileUpload({
    bucket: 'checkoutBanners',
    onSuccess: (url) => onBannerChange(url),
    onError: (error) => console.error('Upload error:', error),
  });

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    await uploadFile(file);
  };

  return (
    <div className="space-y-4">
      <Label>Banner da Sidebar</Label>
      <div className="space-y-2">
        {currentBanner && (
          <img
            src={currentBanner}
            alt="Banner atual"
            className="w-full h-32 object-cover rounded"
          />
        )}
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          disabled={isUploading}
          className="hidden"
          id="sidebar-banner-upload"
        />
        <Label htmlFor="sidebar-banner-upload" className="cursor-pointer">
          {isUploading ? 'Fazendo upload...' : 'Selecionar Banner'}
        </Label>
      </div>
    </div>
  );
}
```

### 5. Atualizar o UnifiedCheckoutSettings

O componente `UnifiedCheckoutSettings` precisa ser atualizado para incluir uploads de imagens:

```typescript
// Adicionar uploads para sidebar banner
<TabsContent value="sidebar">
  <Card>
    <CardHeader>
      <CardTitle>Configurações da Sidebar</CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="flex items-center justify-between">
        <Label htmlFor="sidebar-enabled">Exibir Sidebar</Label>
        <Switch
          id="sidebar-enabled"
          checked={settings.sidebar.enabled}
          onCheckedChange={(checked) => updateSetting('sidebar', 'enabled', checked)}
        />
      </div>

      {settings.sidebar.enabled && (
        <>
          {/* Upload do Banner da Sidebar */}
          <SidebarBannerUpload
            currentBanner={settings.sidebar.bannerUrl}
            onBannerChange={(url) => updateSetting('sidebar', 'bannerUrl', url)}
          />

          {/* ... resto das configurações */}
        </>
      )}
    </CardContent>
  </Card>
</TabsContent>
```

### 6. Atualizar o TestimonialsEditor

```typescript
// components/TestimonialsEditor.tsx
export function TestimonialsEditor({ testimonials, onTestimonialsChange, maxTestimonials }: TestimonialsEditorProps) {
  // ... código existente ...

  const updateTestimonial = (index: number, field: string, value: any) => {
    const updated = [...testimonials];
    updated[index] = { ...updated[index], [field]: value };
    onTestimonialsChange(updated);
  };

  return (
    <div className="space-y-4">
      {testimonials.map((testimonial, index) => (
        <Card key={testimonial.id}>
          <CardContent className="p-4">
            <div className="space-y-4">
              {/* Avatar Upload */}
              <TestimonialAvatarUpload
                currentAvatar={testimonial.avatar}
                onAvatarChange={(url) => updateTestimonial(index, 'avatar', url)}
              />

              {/* Nome */}
              <div>
                <Label>Nome</Label>
                <Input
                  value={testimonial.name}
                  onChange={(e) => updateTestimonial(index, 'name', e.target.value)}
                />
              </div>

              {/* Rating */}
              <div>
                <Label>Avaliação</Label>
                <Select
                  value={testimonial.rating.toString()}
                  onValueChange={(value) => updateTestimonial(index, 'rating', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <SelectItem key={rating} value={rating.toString()}>
                        {rating} estrela{rating > 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Comentário */}
              <div>
                <Label>Comentário</Label>
                <Textarea
                  value={testimonial.comment}
                  onChange={(e) => updateTestimonial(index, 'comment', e.target.value)}
                  rows={3}
                />
              </div>

              {/* Localização */}
              <div>
                <Label>Localização</Label>
                <Input
                  value={testimonial.location || ''}
                  onChange={(e) => updateTestimonial(index, 'location', e.target.value)}
                />
              </div>

              {/* Verificado */}
              <div className="flex items-center justify-between">
                <Label>Verificado</Label>
                <Switch
                  checked={testimonial.verified || false}
                  onCheckedChange={(checked) => updateTestimonial(index, 'verified', checked)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

## Resumo da Implementação

### Arquivos a serem criados/modificados:

1. **Componentes de Renderização**:
   - `apps/web/app/(checkout)/checkout/components/checkout-banner.tsx`
   - `apps/web/app/(checkout)/checkout/components/checkout-header.tsx`
   - `apps/web/app/(checkout)/checkout/components/urgency-bar.tsx`
   - `apps/web/app/(checkout)/checkout/components/trust-badges.tsx`
   - `apps/web/app/(checkout)/checkout/components/guarantee-cards.tsx`
   - `apps/web/app/(checkout)/checkout/components/scarcity-indicator.tsx`
   - `apps/web/app/(checkout)/checkout/components/testimonials.tsx`
   - `apps/web/app/(checkout)/checkout/components/checkout-sidebar.tsx`

2. **Componentes de Upload**:
   - `apps/web/app/(checkout)/checkout/components/testimonial-avatar-upload.tsx`
   - `apps/web/app/(checkout)/checkout/components/sidebar-banner-upload.tsx`

3. **Arquivos a serem modificados**:
   - `apps/web/app/(checkout)/checkout/[productId]/page.tsx` - Atualizar para usar todas as configurações
   - `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/[productId]/checkouts/components/UnifiedCheckoutSettings.tsx` - Adicionar uploads
   - `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/[productId]/checkouts/components/TestimonialsEditor.tsx` - Adicionar upload de avatar

### Funcionalidades implementadas:

1. ✅ **Banner do Checkout** - Upload e configuração
2. ✅ **Header** - Logo e nome da empresa
3. ✅ **Urgência** - Contador regressivo
4. ✅ **Trust Badges** - Badges de confiança
5. ✅ **Cards de Garantia** - Cards personalizáveis
6. ✅ **Escassez** - Indicador de estoque
7. ✅ **Depoimentos** - Com upload de avatar
8. ✅ **Sidebar** - Com upload de banner

### Sistema de Upload:

- **Buckets**: `checkoutBanners`, `testimonialAvatars`
- **API**: `/api/uploads/signed-upload-url`
- **Hooks**: `useFileUpload` para upload de arquivos
- **Componentes**: Upload components para cada tipo de imagem

### Configurações Dinâmicas:

Todas as configurações feitas na página de configuração do produto são automaticamente refletidas na página de checkout, incluindo:

- Cores e estilos
- Layouts e posicionamento
- Conteúdo personalizado
- Imagens e banners
- Comportamento dos elementos

Este sistema permite total personalização do checkout sem necessidade de código adicional, tudo configurável através da interface administrativa.
