# Sistema de Validação Inteligente CPF/CNPJ

## Visão Geral

Implementamos um sistema inteligente de validação de documentos que detecta automaticamente se o usuário está digitando um CPF (11 dígitos) ou CNPJ (14 dígitos), aplicando a máscara e validação apropriadas em tempo real.

## Funcionalidades Implementadas

### 1. Detecção Automática de Tipo
- **CPF**: Detectado quando o usuário digita até 11 dígitos
- **CNPJ**: Detectado quando o usuário digita mais de 11 dígitos
- **Transição suave**: O campo se adapta automaticamente conforme o usuário digita

### 2. Máscaras Dinâmicas
- **CPF**: `000.000.000-00` (formatação aplicada em tempo real)
- **CNPJ**: `00.000.000/0000-00` (formatação aplicada em tempo real)
- **Limite inteligente**: Impede digitação além do necessário

### 3. Validação Robusta
- **CPF**: Validação completa com algoritmo oficial
- **CNPJ**: Validação completa com algoritmo oficial
- **Proteção contra fraudes**: Rejeita sequências e números repetidos
- **Feedback visual**: Indicadores de validação em tempo real

### 4. Experiência do Usuário
- **Tooltip explicativo**: Explica por que pedimos o documento
- **Labels dinâmicos**: "Documento" em vez de "CPF" fixo
- **Validação não intrusiva**: Não quebra o fluxo de compra
- **Feedback imediato**: Validação em tempo real sem bloquear

## Componentes Atualizados

### 1. CPFCNPJInput
**Arquivo**: `apps/web/app/(checkout)/checkout/components/cpf-cnpj-input.tsx`

**Funcionalidades**:
- Detecção automática de tipo de documento
- Formatação dinâmica baseada no tipo
- Validação em tempo real
- Tooltip explicativo
- Indicadores visuais de validação

### 2. Schemas de Validação
**Arquivo**: `apps/web/app/(checkout)/checkout/components/types.ts`

**Melhorias**:
- Validação unificada para CPF e CNPJ
- Mensagens de erro mais claras
- Suporte a ambos os tipos de documento

### 3. Formulários de Checkout
**Arquivos atualizados**:
- `checkout-form.tsx`
- `customer-form.tsx`
- `unified-checkout-form.tsx`
- `combined-form.tsx`

**Mudanças**:
- Substituição do `CPFInput` pelo `CPFCNPJInput`
- Atualização das funções de validação
- Labels atualizados para "Documento"

## Algoritmos de Validação

### CPF
```typescript
function validateCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, '');

  if (cleanCPF.length !== 11) return false;
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false; // Todos iguais

  // Validação do primeiro dígito verificador
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

  // Validação do segundo dígito verificador
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

  return true;
}
```

### CNPJ
```typescript
function validateCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, '');

  if (cleanCNPJ.length !== 14) return false;
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false; // Todos iguais

  // Validação do primeiro dígito verificador
  let sum = 0;
  let weight = 2;
  for (let i = 11; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  let remainder = sum % 11;
  const firstDigit = remainder < 2 ? 0 : 11 - remainder;
  if (firstDigit !== parseInt(cleanCNPJ.charAt(12))) return false;

  // Validação do segundo dígito verificador
  sum = 0;
  weight = 2;
  for (let i = 12; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  remainder = sum % 11;
  const secondDigit = remainder < 2 ? 0 : 11 - remainder;
  if (secondDigit !== parseInt(cleanCNPJ.charAt(13))) return false;

  return true;
}
```

## Tooltip Explicativo

O tooltip explica de forma clara por que pedimos o documento:

**Para CPF**:
> "Por que pedimos seu CPF?
> O CPF é necessário para emissão de notas fiscais e comprovação de identidade. Isso garante que você receba os documentos fiscais corretos."

**Para CNPJ**:
> "Por que pedimos seu CNPJ?
> O CNPJ é necessário para emissão de notas fiscais e comprovação de pessoa jurídica. Isso garante que sua empresa receba os documentos fiscais corretos."

## Benefícios para o Negócio

### 1. Maior Conversão
- **Menos abandono**: Usuários não ficam confusos com campos específicos
- **Flexibilidade**: Aceita tanto pessoas físicas quanto jurídicas
- **Experiência fluida**: Validação em tempo real sem interrupções

### 2. Conformidade Fiscal
- **Documentos válidos**: Validação robusta garante dados corretos
- **Emissão de notas**: Dados corretos para documentação fiscal
- **Auditoria**: Rastreabilidade completa dos documentos

### 3. Experiência do Usuário
- **Intuitivo**: O campo se adapta automaticamente
- **Informativo**: Tooltip explica a necessidade
- **Visual**: Indicadores de validação em tempo real
- **Não intrusivo**: Não quebra o fluxo de compra

## Testes Implementados

Criamos um script de teste abrangente (`scripts/test-cpf-cnpj-validation.ts`) que valida:

- ✅ Validação de CPF (válidos e inválidos)
- ✅ Validação de CNPJ (válidos e inválidos)
- ✅ Formatação automática
- ✅ Detecção automática de tipo
- ✅ Casos extremos e edge cases

## Compatibilidade

- ✅ **Backward compatible**: Não quebra funcionalidades existentes
- ✅ **TypeScript**: Totalmente tipado
- ✅ **React**: Compatível com React Hook Form
- ✅ **Tailwind**: Estilização consistente
- ✅ **Acessibilidade**: Suporte a screen readers

## Próximos Passos

1. **Monitoramento**: Acompanhar métricas de conversão
2. **Feedback**: Coletar feedback dos usuários
3. **Otimizações**: Melhorias baseadas no uso real
4. **Documentação**: Atualizar documentação da API se necessário

## Conclusão

O sistema implementado oferece uma experiência superior para coleta de documentos, aumentando a conversão e garantindo a qualidade dos dados coletados. A detecção automática e validação robusta eliminam fricções no processo de checkout, resultando em mais vendas e melhor conformidade fiscal.
