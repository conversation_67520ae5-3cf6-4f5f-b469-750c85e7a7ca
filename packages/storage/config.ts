export const STORAGE_CONFIG = {
  buckets: {
    onboardingDocuments: "supgateway",
    userDocuments: "supgateway", 
    companyDocuments: "supgateway",
    checkoutBanners: "supgateway",
    testimonialAvatars: "supgateway",
  },
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    "image/jpeg",
    "image/png",
    "image/jpg",
    "image/webp",
    "application/pdf",
  ],
  uploadTimeout: 30000, // 30 seconds
} as const;

export type StorageBucket = keyof typeof STORAGE_CONFIG.buckets;
