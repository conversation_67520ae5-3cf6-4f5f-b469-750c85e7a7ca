"use client";

import { useState } from "react";
import { getSignedUploadUrl, getSignedUrl } from "../provider/s3";
import { STORAGE_CONFIG, type StorageBucket } from "../config";

interface UseFileUploadOptions {
  bucket: StorageBucket;
  onSuccess?: (url: string) => void;
  onError?: (error: string) => void;
}

export function useFileUpload({ bucket, onSuccess, onError }: UseFileUploadOptions) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const uploadFile = async (file: File, path?: string) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      // Validate file type
      if (!STORAGE_CONFIG.allowedTypes.includes(file.type)) {
        throw new Error(`Tipo de arquivo não suportado. Tipos permitidos: ${STORAGE_CONFIG.allowedTypes.join(", ")}`);
      }

      // Validate file size
      if (file.size > STORAGE_CONFIG.maxFileSize) {
        throw new Error(`Arquivo muito grande. Tamanho máximo: ${STORAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`);
      }

      // Generate file path if not provided
      const filePath = path || `${Date.now()}-${file.name}`;
      const bucketName = STORAGE_CONFIG.buckets[bucket];

      // Get signed upload URL
      console.log("Getting signed upload URL for:", { filePath, bucketName });
      const uploadUrl = await getSignedUploadUrl(filePath, { bucket: bucketName });
      console.log("Signed upload URL obtained:", uploadUrl);

      // Upload file to S3
      console.log("Starting file upload to R2...");
      const response = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });
      
      console.log("Upload response status:", response.status);
      console.log("Upload response headers:", Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error("Falha ao fazer upload do arquivo");
      }

      setUploadProgress(100);

      // Get signed URL for the uploaded file
      const fileUrl = await getSignedUrl(filePath, {
        bucket: bucketName,
        expiresIn: 60 * 60 * 24 * 365 // 1 year
      });

      onSuccess?.(fileUrl);
      return fileUrl;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      onError?.(errorMessage);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return {
    uploadFile,
    isUploading,
    uploadProgress,
  };
}
