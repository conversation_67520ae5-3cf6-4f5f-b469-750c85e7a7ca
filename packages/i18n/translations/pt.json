{"admin": {"menu": {"organizations": "Organizações", "users": "Usuários"}, "organizations": {"backToList": "Voltar para organizações", "confirmDelete": {"confirm": "Excluir", "message": "Tem certeza que deseja excluir esta organização? Esta ação não pode ser desfeita.", "title": "Excluir organização"}, "create": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "deleteOrganization": {"deleted": "Organização excluída com sucesso!", "deleting": "Excluindo organização...", "notDeleted": "A organização não pôde ser excluída. Tente novamente."}, "edit": "<PERSON><PERSON>", "form": {"createTitle": "Criar uma organização", "name": "Nome da organização", "notifications": {"error": "Não foi possível salvar a organização. Tente novamente mais tarde.", "success": "Organização salva com sucesso."}, "save": "<PERSON><PERSON>", "updateTitle": "Editar organização"}, "loading": "Carregando organizações...", "membersCount": "{count} {count, plural, one {membro} other {membros}}", "search": "Buscar por uma organização...", "title": "Gerenciar organizações"}, "title": "Administração", "users": {"confirmDelete": {"confirm": "Excluir", "message": "Tem certeza que deseja excluir este usuário? Esta ação não pode ser desfeita.", "title": "Excluir usuário"}, "delete": "Excluir", "deleteUser": {"deleted": "Usuário excluído com sucesso!", "deleting": "Excluindo usuário...", "notDeleted": "O usuário não pôde ser excluído. Tente novamente."}, "emailVerified": {"verified": "E-mail verificado", "waiting": "E-mail aguardando verificação"}, "impersonate": "Personificar", "impersonation": {"impersonating": "Personificando como {name}..."}, "loading": "Carregando usuários...", "resendVerificationMail": {"error": "Não foi possível reenviar o e-mail de verificação. Tente novamente.", "submitting": "Reenviando e-mail de verificação...", "success": "E-mail de verificação enviado.", "title": "Reenviar e-mail de verificação"}, "search": "Buscar por nome ou e-mail...", "title": "Gerenciar usuários", "assignAdminRole": "Atribuir função de administrador", "removeAdminRole": "Remover função de administrador"}, "description": "Gerencie seu gateway de pagamentos."}, "app": {"menu": {"accountSettings": "Configurações da conta", "admin": "Administração", "aiChatbot": "Chatbot IA", "organizationSettings": "Configurações da organização", "start": "Início"}, "userMenu": {"accountSettings": "Configurações da conta", "colorMode": "<PERSON><PERSON>", "documentation": "Documentação", "home": "Início", "logout": "<PERSON><PERSON>"}}, "auth": {"errors": {"invalidEmailOrPassword": "As credenciais inseridas são inválidas. Verifique-as e tente novamente.", "unknown": "Algo deu errado. Tente novamente.", "userNotFound": "Este usuário não existe", "failedToCreateUser": "Não foi possível criar o usuário. Tente novamente.", "failedToCreateSession": "Não foi possível criar uma sessão. Tente novamente.", "failedToUpdateUser": "Não foi possível atualizar o usuário. Tente novamente.", "failedToGetSession": "Não foi possível obter a sessão.", "invalidPassword": "A senha inserida está incorreta.", "invalidEmail": "O e-mail inserido é inválido.", "invalidToken": "O token inserido é inválido ou expirou.", "credentialAccountNotFound": "Conta não encontrada.", "emailCanNotBeUpdated": "O e-mail não pôde ser atualizado. Tente novamente.", "emailNotVerified": "Verifique seu e-mail antes de fazer login.", "failedToGetUserInfo": "Não foi possível carregar as informações do usuário.", "idTokenNotSupported": "Token ID não é suportado.", "passwordTooLong": "A senha é muito longa.", "passwordTooShort": "A senha é muito curta.", "providerNotFound": "Este provedor não é suportado.", "socialAccountAlreadyLinked": "Esta conta já está vinculada a um usuário.", "userEmailNotFound": "E-mail não encontrado.", "userAlreadyExists": "Este usuário já existe.", "invalidInvitation": "O convite é inválido ou expirou.", "sessionExpired": "A sessão expirou.", "failedToUnlinkLastAccount": "Falha ao desvincular conta", "accountNotFound": "Conta não encontrada"}, "forgotPassword": {"backToSignin": "Voltar ao login", "email": "E-mail", "hints": {"linkNotSent": {"message": "<PERSON><PERSON><PERSON><PERSON>, mas não conseguimos enviar o link para redefinir sua senha. Tente novamente mais tarde.", "title": "Link não enviado"}, "linkSent": {"message": "Enviamos um link para continuar. Verifique sua caixa de entrada.", "title": "Link enviado"}}, "message": "Digite seu endereço de e-mail e enviaremos um link para redefinir sua senha.", "submit": "Enviar link", "title": "Esqueceu sua senha?"}, "login": {"continueWith": "Ou continue com", "createAnAccount": "Criar uma conta", "dontHaveAnAccount": "Ainda não tem uma conta?", "forgotPassword": "Esque<PERSON>u a senha?", "hints": {"invalidCredentials": "O e-mail ou senha inseridos são inválidos. Tente novamente.", "linkSent": {"message": "Enviamos um link para continuar. Verifique sua caixa de entrada.", "title": "Link enviado"}, "passwordMode": {"title": "Entrar com senha", "description": "Use seu e-mail e senha para acessar sua conta"}, "magicLinkMode": {"title": "Entrar sem senha", "description": "Receba um link por e-mail para acessar instantaneamente"}, "socialLogin": {"title": "Acesso rápido e seguro", "description": "Use sua conta social favorita para entrar"}, "passkey": {"title": "Acesso biométrico", "description": "Use sua impressão digital ou reconhecimento facial"}}, "loginWithPasskey": "Entrar com passkey", "modes": {"magicLink": "<PERSON>", "password": "<PERSON><PERSON>"}, "submit": "Entrar", "subtitle": "Escolha como deseja acessar sua conta", "title": "Bem-vindo de volta", "sendMagicLink": "Enviar link mágico", "loading": {"signingIn": "Entrando...", "sendingLink": "Enviando link...", "authenticating": "Autenticando..."}}, "resetPassword": {"backToSignin": "Voltar ao login", "hints": {"error": "<PERSON><PERSON><PERSON><PERSON>, mas não conseguimos redefinir sua senha. Tente novamente.", "success": "Sua senha foi redefinida com sucesso."}, "message": "Digite uma nova senha.", "newPassword": "Nova senha", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Redefinir sua senha"}, "signup": {"alreadyHaveAccount": "Já tem uma conta?", "email": "E-mail", "hints": {"signupFailed": "<PERSON><PERSON><PERSON><PERSON>, mas não conseguimos criar sua conta. Tente novamente mais tarde.", "verifyEmail": "Enviamos um link para verificar seu e-mail. Verifique sua caixa de entrada.", "passwordMode": {"title": "Criar conta com senha", "description": "Preencha seus dados para criar uma conta segura"}, "magicLinkMode": {"title": "<PERSON><PERSON><PERSON> conta sem senha", "description": "Receba um link por e-mail para confirmar sua conta"}, "socialSignup": {"title": "Cadastro rápido e seguro", "description": "Use sua conta social para criar sua conta instantaneamente"}, "benefits": {"title": "Por que criar uma conta?", "items": {"0": "Acesso completo à plataforma", "1": "Histórico de transações", "2": "Configurações personalizadas", "3": "Suporte prioritário"}}}, "message": "Ficamos felizes que você queira se juntar a nós. Preencha o formulário abaixo para criar sua conta.", "name": "Nome completo", "password": "<PERSON><PERSON>", "signIn": "Entrar", "submit": "C<PERSON><PERSON> conta", "title": "Criar uma conta", "subtitle": "Junte-se a milhares de usuários que confiam em nossa plataforma", "loading": {"creating": "C<PERSON>do conta...", "sendingLink": "Enviando link...", "verifying": "Verificando..."}}, "verify": {"title": "Verificar sua conta", "code": "Código de uso único", "submit": "Verificar", "backToSignin": "Voltar ao login", "message": "Digite o código de uso único do seu aplicativo autenticador para continuar."}}, "blog": {"description": "<PERSON>ia as últimas novidades sobre pagamentos e tecnologia", "title": "Blog SupGateway", "back": "Voltar ao blog"}, "changelog": {"description": "Fique por dentro das últimas mudanças em nossa plataforma de pagamentos.", "title": "Novidades"}, "common": {"loading": "Carregando...", "confirmation": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar"}, "menu": {"blog": "Blog", "changelog": "Novidades", "contact": "Contato", "dashboard": "<PERSON><PERSON>", "docs": "Documentação", "faq": "Perguntas Frequentes", "login": "Entrar", "pricing": "Preços"}, "tableOfContents": {"title": "Nesta página"}, "actions": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "verify": "Verificar"}}, "contact": {"description": "Estamos aqui para ajudar você. Use o formulário abaixo para entrar em contato conosco.", "form": {"email": "E-mail", "message": "Mensagem", "name": "Nome", "notifications": {"error": "<PERSON><PERSON><PERSON><PERSON>, mas não conseguimos enviar sua mensagem. Tente novamente mais tarde.", "success": "Sua mensagem foi enviada com sucesso. Retornaremos o contato o mais breve possível."}, "submit": "Enviar mensagem"}, "title": "Entre em contato"}, "documentation": {"title": "Documentação"}, "faq": {"description": "Tem alguma dúvida? <PERSON>ós temos as respostas.", "title": "<PERSON><PERSON><PERSON> frequentes"}, "mail": {"common": {"openLinkInBrowser": "Se quiser abrir o link em um navegador diferente do padrão, copie e cole este link:", "otp": "Código de uso único", "useLink": "ou use o seguinte link:"}, "emailVerification": {"body": "<PERSON><PERSON><PERSON>,\nclique no link abaixo para verificar este novo endereço de e-mail.", "confirmEmail": "Verificar e-mail", "subject": "Verificar seu e-mail"}, "forgotPassword": {"body": "<PERSON><PERSON><PERSON>,\nvocê solicitou a redefinição de senha.\n\nClique no botão abaixo para redefinir sua senha.", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "subject": "Redefinir sua senha"}, "magicLink": {"body": "<PERSON><PERSON><PERSON>,\nvocê solicitou um e-mail de login do SupGateway.\n\nClique no link abaixo para fazer login.", "login": "Fazer login", "subject": "Entrar no SupGateway"}, "newUser": {"body": "<PERSON><PERSON><PERSON>,\nobrigado por se cadastrar no SupGateway.\n\nPara começar a usar nossa plataforma de pagamentos, confirme seu endereço de e-mail clicando no link abaixo.", "confirmEmail": "Confirmar e-mail", "subject": "Confirmar seu e-mail"}, "newsletterSignup": {"body": "Obrigado por se inscrever na newsletter do SupGateway. Manteremos você atualizado com as últimas novidades sobre pagamentos digitais.", "subject": "Bem-vindo à nossa newsletter"}, "organizationInvitation": {"body": "Você foi convidado para participar da organização {organizationName}. Clique no botão abaixo ou copie e cole o link em seu navegador para aceitar o convite e participar da organização.", "headline": "Participar da organização {organizationName}", "join": "Participar da organização", "subject": "Você foi convidado para participar de uma organização"}}, "newsletter": {"email": "E-mail", "hints": {"error": {"message": "Não foi possível inscrever-se na newsletter. Tente novamente mais tarde."}, "success": {"message": "Obrigado por se inscrever em nossa newsletter. Manteremos você informado.", "title": "Inscrito"}}, "submit": "Inscrever-se", "subtitle": "Seja um dos primeiros a ter acesso ao SupGateway.", "title": "<PERSON><PERSON> acesso antecipado"}, "onboarding": {"account": {"avatar": "Avatar", "avatarDescription": "Clique no círculo ou arraste uma imagem para fazer upload do seu avatar.", "name": "Nome"}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "message": "Apenas alguns passos rápidos para começar.", "notifications": {"accountSetupFailed": "<PERSON><PERSON><PERSON><PERSON>, mas não conseguimos configurar sua conta. Tente novamente mais tarde."}, "step": "Passo {step} / {total}", "title": "Configure sua conta"}, "organizations": {"createForm": {"name": "Nome da organização", "notifications": {"error": "<PERSON><PERSON><PERSON><PERSON>, mas não conseguimos criar sua organização. Tente novamente mais tarde.", "success": "Sua organização foi criada. Agora você pode convidar membros."}, "submit": "Criar organização", "subtitle": "Digite um nome para sua organização para começar. Você pode alterar o nome posteriormente nas configurações da organização.", "title": "Criar uma organização"}, "members": {"title": "Me<PERSON><PERSON>", "description": "Gerencie os membros e convites da sua organização", "stats": {"activeMembers": "Membros ativos na sua organização", "pendingInvitations": "Convi<PERSON> a<PERSON>ando resposta", "totalInvitations": "Total de convites enviados", "allTime": "Todos os tempos", "acceptanceRate": "Taxa de Aceitação", "invitationsAccepted": "Dos convites aceitos"}, "badge": {"members": "Me<PERSON><PERSON>", "pending": "Pendentes"}}, "invitationAlert": {"description": "Você precisa fazer login ou criar uma conta para participar da organização.", "title": "Você foi convidado para participar de uma organização."}, "invitationModal": {"accept": "Aceitar", "decline": "Recusar", "description": "Você foi convidado para participar da organização {organizationName}. Deseja aceitar o convite e participar da organização?", "title": "Participar da organização"}, "organizationSelect": {"createNewOrganization": "Criar nova organização", "organizations": "Organizações", "personalAccount": "<PERSON>ta pessoal"}, "organizationsGrid": {"createNewOrganization": "Criar nova organização", "title": "Suas organizações"}, "roles": {"admin": "Administrador", "member": "Membro", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"changeName": {"title": "Nome da organização"}, "deleteOrganization": {"confirmation": "Tem certeza que deseja excluir sua organização?", "description": "Exclua permanentemente sua organização. Uma vez excluída, não há volta. Para confirmar, digite sua senha abaixo:", "submit": "Excluir organização", "title": "Excluir organização"}, "logo": {"description": "Faça upload de um logo para sua organização.", "title": "Logo da organização"}, "members": {"activeMembers": "Membros ativos", "description": "Veja todos os membros ativos e os convites pendentes de sua organização.", "invitations": {"empty": "Você ainda não convidou nenhum membro.", "expiresAt": "Expira em {date}", "invitationStatus": {"accepted": "<PERSON><PERSON>", "canceled": "Cancelado", "pending": "Pendente", "rejected": "<PERSON><PERSON><PERSON><PERSON>"}, "revoke": "<PERSON><PERSON><PERSON> convite", "resend": "Reenviar convite"}, "inviteMember": {"description": "Para convidar um novo membro, envie um convite.", "email": "E-mail", "notifications": {"error": {"description": "Não conseguimos convidar o membro. Tente novamente mais tarde.", "title": "Não foi possível convidar o membro"}, "success": {"description": "O membro foi convidado.", "title": "Membro convidado"}}, "role": "Função", "submit": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> membro"}, "leaveOrganization": "Sair da organização", "notifications": {"removeMember": {"error": {"description": "Não foi possível remover o membro de sua organização. Tente novamente."}, "loading": {"description": "Removendo membro da organização..."}, "success": {"description": "O membro foi removido com sucesso de sua organização."}}, "revokeInvitation": {"error": {"description": "O convite não pôde ser revogado. Tente novamente mais tarde."}, "loading": {"description": "Revogando convite..."}, "success": {"description": "O convite foi revogado."}}, "updateMembership": {"error": {"description": "Não foi possível atualizar a associação à organização. Tente novamente."}, "loading": {"description": "Atualizando associação..."}, "success": {"description": "Associação atualizada com sucesso"}}, "resendInvitation": {"loading": {"description": "<PERSON><PERSON><PERSON><PERSON> convite..."}, "success": {"description": "Convite enviado"}, "error": {"description": "Não foi possível enviar o convite. Tente novamente."}}}, "pendingInvitations": "Convites pendentes", "removeMember": "Remover membro", "title": "Me<PERSON><PERSON>"}, "notifications": {"organizationDeleted": "Sua organização foi excluída.", "organizationNameNotUpdated": "Não conseguimos atualizar o nome de sua organização. Tente novamente mais tarde.", "organizationNameUpdated": "O nome de sua organização foi atualizado.", "organizationNotDeleted": "Não conseguimos excluir sua organização. Tente novamente mais tarde."}, "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configurações da organização.", "title": "Organização", "dangerZone": {"title": "Zona de perigo"}}, "start": {"subtitle": "Bem-vindo à página inicial desta organização!"}}, "pricing": {"choosePlan": "Escolher plano", "contactSales": "Falar com vendas", "description": "Escolha o plano que funciona melhor para você.", "getStarted": "<PERSON><PERSON><PERSON>", "month": "{count, plural, one {mês} other {{count} meses}}", "monthly": "Mensal", "products": {"basic": {"description": "Perfeito para pequenas equipes.", "features": {"anotherFeature": "Outro recurso incrível", "limitedSupport": "Suporte limitado"}, "title": "Básico"}, "enterprise": {"description": "Plano personalizado adaptado às suas necessidades", "features": {"enterpriseSupport": "Suporte empresarial", "unlimitedProjects": "Projetos ilimitados"}, "title": "Empresarial"}, "free": {"description": "Comece gratuitamente", "features": {"anotherFeature": "Outro recurso incrível", "limitedSupport": "Suporte limitado"}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "lifetime": {"description": "Compre uma vez. Use para sempre.", "features": {"extendSupport": "Suporte estendido", "noRecurringCosts": "Sem custos recorrentes"}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "pro": {"description": "Melhor para equipes", "features": {"anotherFeature": "Outro recurso incrível", "fiveMembers": "Até 5 membros", "fullSupport": "Suporte completo"}, "title": "Profissional"}}, "purchase": "<PERSON><PERSON><PERSON>", "recommended": "Recomendado", "subscribe": "<PERSON><PERSON><PERSON>", "title": "Preços", "trialPeriod": "{days} {days, plural, one {dia} other {dias}} de teste gratuito", "year": "{count, plural, one {ano} other {{count} anos}}", "yearly": "<PERSON><PERSON>", "perSeat": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"account": {"avatar": {"description": "Para alterar seu avatar, clique na imagem neste bloco e selecione um arquivo do seu computador para fazer upload.", "notifications": {"error": "Não foi possível atualizar o avatar", "success": "Avatar atualizado com sucesso"}, "title": "Seu avatar"}, "changeEmail": {"description": "Para alterar seu e-mail, digite o novo e-mail e clique em salvar. Você terá que confirmar o novo e-mail antes que ele se torne ativo.", "notifications": {"error": "Não foi possível atualizar o e-mail", "success": "E-mail atualizado com sucesso"}, "title": "Seu e-mail"}, "changeName": {"notifications": {"error": "Não foi possível atualizar o nome", "success": "Nome atualizado com sucesso"}, "title": "Seu nome"}, "deleteAccount": {"confirmation": "Tem certeza que deseja excluir sua conta?", "description": "Exclua permanentemente sua conta. Uma vez excluída, não há volta. Para confirmar, digite sua senha abaixo:", "notifications": {"error": "Não foi possível excluir a conta", "success": "Conta excluída com sucesso"}, "submit": "Excluir conta", "title": "Excluir conta"}, "language": {"description": "Para alterar o idioma do aplicativo para sua conta, selecione um idioma da lista e clique em salvar.", "notifications": {"error": "Não foi possível atualizar o idioma", "success": "Idioma atualizado com sucesso"}, "title": "Seu idioma"}, "security": {"activeSessions": {"description": "<PERSON><PERSON><PERSON> s<PERSON> as sessões ativas de sua conta. Clique no X para encerrar uma sessão específica.", "title": "Sessõ<PERSON> at<PERSON>", "notifications": {"revokeSession": {"success": "Sessão revogada"}}, "currentSession": "<PERSON>ss<PERSON> atual"}, "changePassword": {"currentPassword": "<PERSON><PERSON> atual", "newPassword": "Nova senha", "notifications": {"error": "Não foi possível atualizar a senha", "success": "Senha atualizada com sucesso"}, "title": "<PERSON><PERSON> se<PERSON>a"}, "connectedAccounts": {"connect": "Conectar", "disconnect": "Desconectar", "title": "Contas conectadas"}, "passkeys": {"description": "Use passkeys como uma alternativa segura às senhas.", "notifications": {"addPasskey": {"error": {"title": "Não foi possível adicionar passkey"}, "success": {"title": "Passkey adicionada"}}, "deletePasskey": {"error": {"title": "Não foi possível excluir passkey"}, "loading": {"title": "Excluindo passkey..."}, "success": {"title": "Passkey excluída"}}}, "title": "Passkeys"}, "setPassword": {"description": "Você ainda não definiu uma senha. Para definir uma, precisa passar pelo processo de redefinição de senha. Clique no botão abaixo para enviar um e-mail para redefinir sua senha e siga as instruções no e-mail.", "notifications": {"error": "Não foi possível enviar link para definir senha. Tente novamente.", "success": "Verifique sua caixa de entrada pelo link para definir sua senha."}, "submit": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON> se<PERSON>a"}, "title": "Segurança", "twoFactor": {"title": "Autenticação de dois fatores", "description": "Adicione uma camada extra de segurança à sua conta.", "dialog": {"password": {"title": "Verificar com senha", "description": "Verifique sua conta digitando sua senha:", "label": "<PERSON><PERSON> senha:"}, "totpUrl": {"description": "Use seu aplicativo autenticador preferido e escaneie o código QR ou digite o código secreto abaixo manualmente para configurar a autenticação de dois fatores.", "code": "Digite o código de 6 dígitos para verificar a configuração:", "title": "Ativar autenticação de dois fatores"}}, "enable": "Ativar autenticação de dois fatores", "notifications": {"verify": {"success": {"title": "A autenticação de dois fatores foi ativada com sucesso."}}, "enable": {"error": {"title": "Não foi possível verificar sua conta com a senha fornecida. Tente novamente."}}, "disable": {"success": {"title": "A autenticação de dois fatores foi desativada com sucesso."}}}, "disable": "Desativar autenticação de dois fatores", "enabled": "Você tem a autenticação de dois fatores ativada para sua conta."}}, "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configurações de sua conta pessoal.", "title": "Configurações da conta"}, "billing": {"createCustomerPortal": {"label": "Gerenciar cobrança", "notifications": {"error": {"title": "Não foi possível criar uma sessão do portal do cliente. Tente novamente."}}}, "activePlan": {"status": {"active": "Ativo", "canceled": "Cancelado", "expired": "<PERSON><PERSON><PERSON>", "incomplete": "Incompleto", "past_due": "Em atraso", "paused": "<PERSON><PERSON><PERSON>", "trialing": "<PERSON><PERSON><PERSON> de teste", "unpaid": "Não pago"}, "title": "Seu plano"}, "changePlan": {"description": "Escolha um plano para assinar.", "title": "Alterar seu plano"}, "title": "Cobrança"}, "menu": {"account": {"billing": "Cobrança", "dangerZone": "Zona de perigo", "general": "G<PERSON>", "security": "Segurança", "title": "Conta"}, "organization": {"billing": "Cobrança", "general": "G<PERSON>", "members": "Me<PERSON><PERSON>", "title": "Organização", "dangerZone": "Zona de perigo"}}, "save": "<PERSON><PERSON>"}, "start": {"subtitle": "<PERSON><PERSON><PERSON> as estatísticas mais recentes de seus pagamentos.", "welcome": "<PERSON><PERSON>-vindo {name}!"}, "zod": {"errors": {"invalid_arguments": "Argumentos de função inválidos", "invalid_date": "Data inválida", "invalid_enum_value": "<PERSON>or de enum inválido. Esperado {- options}, recebido '{received}'", "invalid_intersection_types": "Os resultados da interseção não puderam ser mesclados", "invalid_literal": "Valor literal inválido, esperado {expected}", "invalid_return_type": "Tipo de retorno de função inválido", "invalid_string": {"cuid": "{validation} inv<PERSON><PERSON>o", "datetime": "{validation} inv<PERSON><PERSON>o", "email": "{validation} inv<PERSON><PERSON>o", "endsWith": "Entrada inválida: deve terminar com \"{endsWith}\"", "regex": "<PERSON>v<PERSON><PERSON><PERSON>", "startsWith": "Entrada inválida: deve come<PERSON>r com \"{startsWith}\"", "url": "{validation} inv<PERSON><PERSON>o", "uuid": "{validation} inv<PERSON><PERSON>o"}, "invalid_type": "E<PERSON><PERSON> {expected}, recebido {received}", "invalid_type_received_undefined": "Obrigatório", "invalid_union": "Entrada inválida", "invalid_union_discriminator": "Valor discriminador inválido. Esperado {- options}", "not_finite": "O número deve ser finito", "not_multiple_of": "O número deve ser um múltiplo de {multipleOf}", "too_big": {"array": {"exact": "O array deve conter exatamente {maximum} elemento(s)", "inclusive": "O array deve conter no máximo {maximum} elemento(s)", "not_inclusive": "O array deve conter menos de {maximum} elemento(s)"}, "date": {"exact": "A data deve ser exatamente {- maximum, datetime}", "inclusive": "A data deve ser menor ou igual a {- maximum, datetime}", "not_inclusive": "A data deve ser menor que {- maximum, datetime}"}, "number": {"exact": "O número deve ser exatamente {maximum}", "inclusive": "O número deve ser menor ou igual a {maximum}", "not_inclusive": "O número deve ser menor que {maximum}"}, "set": {"exact": "Entrada inválida", "inclusive": "Entrada inválida", "not_inclusive": "Entrada inválida"}, "string": {"exact": "O texto deve conter exatamente {maximum} caractere(s)", "inclusive": "O texto deve conter no máximo {maximum} caractere(s)", "not_inclusive": "O texto deve conter menos de {maximum} caractere(s)"}}, "too_small": {"array": {"exact": "O array deve conter exatamente {minimum} elemento(s)", "inclusive": "O array deve conter pelo menos {minimum} elemento(s)", "not_inclusive": "O array deve conter mais de {minimum} elemento(s)"}, "date": {"exact": "A data deve ser exatamente {- minimum, datetime}", "inclusive": "A data deve ser maior ou igual a {- minimum, datetime}", "not_inclusive": "A data deve ser maior que {- minimum, datetime}"}, "number": {"exact": "O número deve ser exatamente {minimum}", "inclusive": "O número deve ser maior ou igual a {minimum}", "not_inclusive": "O número deve ser maior que {minimum}"}, "set": {"exact": "Entrada inválida", "inclusive": "Entrada inválida", "not_inclusive": "Entrada inválida"}, "string": {"exact": "O texto deve conter exatamente {minimum} caractere(s)", "inclusive": "O texto deve conter pelo menos {minimum} caractere(s)", "not_inclusive": "O texto deve conter mais de {minimum} caractere(s)"}}, "unrecognized_keys": "Chave(s) não reconhecida(s) no objeto: {- keys}"}, "types": {"array": "array", "bigint": "bigint", "boolean": "boolean", "date": "data", "float": "float", "function": "função", "integer": "inteiro", "map": "mapa", "nan": "nan", "never": "never", "null": "nulo", "number": "número", "object": "objeto", "promise": "promise", "set": "conjunto", "string": "texto", "symbol": "sí<PERSON>lo", "undefined": "indefinido", "unknown": "desconhecido", "void": "void"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "data e hora", "email": "e-mail", "emoji": "emoji", "ip": "ip", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}, "choosePlan": {"title": "Escolha seu plano", "description": "Para continuar, selecione um plano."}}