import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/zod';
import { z } from 'zod';
import { HTTPException } from 'hono/http-exception';
import { db } from '@repo/database';
import { authMiddleware } from '../../middleware/auth';

export const testimonialsRouter = new Hono()
  .basePath("/testimonials");

// Schema de validação para testimonial
const createTestimonialSchema = z.object({
  customerName: z.string().min(1, "Nome do cliente é obrigatório"),
  customerEmail: z.string().email("E-mail inválido").optional(),
  customerPhoto: z.string().url("URL da foto inválida").optional(),
  customerRole: z.string().optional(),
  customerLocation: z.string().optional(),
  title: z.string().optional(),
  content: z.string().min(10, "Conteúdo deve ter pelo menos 10 caracteres"),
  rating: z.number().min(1).max(5, "Avaliação deve ser entre 1 e 5"),
  source: z.enum(["WHATSAPP", "FACEBOOK", "TIKTOK", "INSTAGRAM", "EMAIL", "MANUAL", "YOUTUBE", "WEBSITE"]),
  sourceUrl: z.string().url("URL da fonte inválida").optional(),
  sourceDate: z.string().datetime().optional(),
  attachments: z.array(z.string()).default([]),
  productId: z.string().optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
});

const updateTestimonialSchema = createTestimonialSchema.partial().extend({
  isApproved: z.boolean().optional(),
  isActive: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
});

// GET /testimonials/public - Buscar testimonials públicos (sem autenticação)
testimonialsRouter.get(
  "/public",
  describeRoute({
    tags: ["Testimonials"],
    summary: "Get public testimonials",
    description: "Get public testimonials without authentication",
  }),
  validator("query", z.object({
    limit: z.string().optional().default("10"),
    productId: z.string().optional(),
    featured: z.string().optional(),
  })),
  async (c) => {
    try {
      const { limit, productId, featured } = c.req.valid("query");

      const where: any = {
        isApproved: true,
        isActive: true,
      };

      if (productId) {
        where.productId = productId;
      }

      if (featured === "true") {
        where.isFeatured = true;
      }

      const testimonials = await db.testimonial.findMany({
        where,
        select: {
          id: true,
          customerName: true,
          customerPhoto: true,
          customerRole: true,
          customerLocation: true,
          title: true,
          content: true,
          rating: true,
          source: true,
          isApproved: true,
          isFeatured: true,
          createdAt: true,
        },
        orderBy: [
          { isFeatured: "desc" },
          { createdAt: "desc" },
        ],
        take: parseInt(limit),
      });

      return c.json({ testimonials });
    } catch (error) {
      console.error("Error fetching public testimonials:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);

// GET /testimonials - Listar testimonials da organização (com autenticação)
testimonialsRouter.get(
  "/",
  describeRoute({
    tags: ["Testimonials"],
    summary: "List testimonials",
    description: "Get paginated list of testimonials for the organization",
  }),
  validator("query", z.object({
    page: z.string().optional().default("1"),
    limit: z.string().optional().default("20"),
    productId: z.string().optional(),
    isApproved: z.string().optional(),
    isActive: z.string().optional(),
    isFeatured: z.string().optional(),
    search: z.string().optional(),
  })),
  async (c) => {
    try {
      const session = c.get("session") as any;
      const { page, limit, productId, isApproved, isActive, isFeatured, search } = c.req.valid("query");

      // Get user's organization
      const member = await db.member.findFirst({
        where: {
          userId: session.user.userId,
        },
        include: {
          organization: true,
        },
      });

      if (!member) {
        return c.json({ error: "Organization not found" }, 404);
      }

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      // Build where clause
      const where: any = {
        organizationId: member.organizationId,
      };

      if (productId) {
        where.productId = productId;
      }

      if (isApproved !== undefined) {
        where.isApproved = isApproved === "true";
      }

      if (isActive !== undefined) {
        where.isActive = isActive === "true";
      }

      if (isFeatured !== undefined) {
        where.isFeatured = isFeatured === "true";
      }

      if (search) {
        where.OR = [
          { customerName: { contains: search, mode: "insensitive" } },
          { content: { contains: search, mode: "insensitive" } },
          { title: { contains: search, mode: "insensitive" } },
        ];
      }

      const [testimonials, total] = await Promise.all([
        db.testimonial.findMany({
          where,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limitNum,
        }),
        db.testimonial.count({ where }),
      ]);

      return c.json({
        testimonials,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      });
    } catch (error) {
      console.error("Error fetching testimonials:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);

// Aplicar middleware de autenticação para rotas protegidas
testimonialsRouter.use("/", authMiddleware);
testimonialsRouter.use("/:id", authMiddleware);

// GET /testimonials/:id - Buscar testimonial específico
testimonialsRouter.get(
  "/:id",
  describeRoute({
    tags: ["Testimonials"],
    summary: "Get testimonial by ID",
    description: "Get a specific testimonial by its ID",
  }),
  async (c) => {
    try {
      const session = c.get("session") as any;
      const id = c.req.param("id");

      // Get user's organization
      const member = await db.member.findFirst({
        where: {
          userId: session.user.userId,
        },
      });

      if (!member) {
        return c.json({ error: "Organization not found" }, 404);
      }

      const testimonial = await db.testimonial.findFirst({
        where: {
          id,
          organizationId: member.organizationId,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      if (!testimonial) {
        return c.json({ error: "Testimonial not found" }, 404);
      }

      return c.json({ testimonial });
    } catch (error) {
      console.error("Error fetching testimonial:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);

// POST /testimonials - Criar novo testimonial
testimonialsRouter.post(
  "/",
  describeRoute({
    tags: ["Testimonials"],
    summary: "Create testimonial",
    description: "Create a new testimonial",
  }),
  validator("json", createTestimonialSchema),
  async (c) => {
    try {
      const session = c.get("session") as any;
      const data = c.req.valid("json");

      // Get user's organization
      const member = await db.member.findFirst({
        where: {
          userId: session.user.userId,
        },
      });

      if (!member) {
        return c.json({ error: "Organization not found" }, 404);
      }

      // Validate product if provided
      if (data.productId) {
        const product = await db.product.findFirst({
          where: {
            id: data.productId,
            organizationId: member.organizationId,
          },
        });

        if (!product) {
          return c.json({ error: "Product not found" }, 404);
        }
      }

      const testimonial = await db.testimonial.create({
        data: {
          ...data,
          organizationId: member.organizationId,
          sourceDate: data.sourceDate ? new Date(data.sourceDate) : null,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return c.json({ testimonial }, 201);
    } catch (error) {
      console.error("Error creating testimonial:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);

// PUT /testimonials/:id - Atualizar testimonial
testimonialsRouter.put(
  "/:id",
  describeRoute({
    tags: ["Testimonials"],
    summary: "Update testimonial",
    description: "Update an existing testimonial",
  }),
  validator("json", updateTestimonialSchema),
  async (c) => {
    try {
      const session = c.get("session") as any;
      const id = c.req.param("id");
      const data = c.req.valid("json");

      // Get user's organization
      const member = await db.member.findFirst({
        where: {
          userId: session.user.userId,
        },
      });

      if (!member) {
        return c.json({ error: "Organization not found" }, 404);
      }

      // Check if testimonial exists and belongs to organization
      const existingTestimonial = await db.testimonial.findFirst({
        where: {
          id,
          organizationId: member.organizationId,
        },
      });

      if (!existingTestimonial) {
        return c.json({ error: "Testimonial not found" }, 404);
      }

      // Validate product if provided
      if (data.productId) {
        const product = await db.product.findFirst({
          where: {
            id: data.productId,
            organizationId: member.organizationId,
          },
        });

        if (!product) {
          return c.json({ error: "Product not found" }, 404);
        }
      }

      const testimonial = await db.testimonial.update({
        where: { id },
        data: {
          ...data,
          sourceDate: data.sourceDate ? new Date(data.sourceDate) : undefined,
          approvedBy: data.isApproved ? session.user.userId : undefined,
          approvedAt: data.isApproved ? new Date() : undefined,
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return c.json({ testimonial });
    } catch (error) {
      console.error("Error updating testimonial:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);

// DELETE /testimonials/:id - Deletar testimonial
testimonialsRouter.delete(
  "/:id",
  describeRoute({
    tags: ["Testimonials"],
    summary: "Delete testimonial",
    description: "Delete a testimonial by its ID",
  }),
  async (c) => {
    try {
      const session = c.get("session") as any;
      const id = c.req.param("id");

      // Get user's organization
      const member = await db.member.findFirst({
        where: {
          userId: session.user.userId,
        },
      });

      if (!member) {
        return c.json({ error: "Organization not found" }, 404);
      }

      // Check if testimonial exists and belongs to organization
      const existingTestimonial = await db.testimonial.findFirst({
        where: {
          id,
          organizationId: member.organizationId,
        },
      });

      if (!existingTestimonial) {
        return c.json({ error: "Testimonial not found" }, 404);
      }

      await db.testimonial.delete({
        where: { id },
      });

      return c.json({ message: "Testimonial deleted successfully" });
    } catch (error) {
      console.error("Error deleting testimonial:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);

// GET /testimonials/public/:productId - Buscar testimonials públicos para um produto
testimonialsRouter.get(
  "/public/:productId",
  describeRoute({
    tags: ["Testimonials"],
    summary: "Get public testimonials",
    description: "Get public testimonials for a specific product",
  }),
  async (c) => {
    try {
      const productId = c.req.param("productId");

      const testimonials = await db.testimonial.findMany({
        where: {
          productId,
          isApproved: true,
          isActive: true,
        },
        select: {
          id: true,
          customerName: true,
          customerPhoto: true,
          customerRole: true,
          customerLocation: true,
          title: true,
          content: true,
          rating: true,
          source: true,
          createdAt: true,
        },
        orderBy: [
          { isFeatured: "desc" },
          { createdAt: "desc" },
        ],
        take: 10,
      });

      return c.json({ testimonials });
    } catch (error) {
      console.error("Error fetching public testimonials:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }
);
