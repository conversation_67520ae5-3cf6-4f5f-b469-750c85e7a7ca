import { Hono } from "hono";
import { z } from "zod";
import { db } from "@repo/database";
import { authMiddleware } from "../middleware/auth";

const settingsRouter = new Hono()
  .basePath("/:productId/settings")
  .use("*", authMiddleware);

const updateSettingsSchema = z.object({
  banner: z.string().nullable().optional(), // Removido .url() para permitir strings não-URL
  header: z.object({
    showLogo: z.boolean().optional(),
    logoUrl: z.string().optional(),
    companyName: z.string().optional(),
  }).optional(),
  urgency: z.object({
    enabled: z.boolean().optional(),
    message: z.string().optional(),
    endTime: z.string().optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    accentColor: z.string().optional(),
  }).optional(),
  guaranteeCards: z.object({
    enabled: z.boolean().optional(),
    cards: z.array(z.object({
      id: z.string(),
      title: z.string(),
      description: z.string(),
      icon: z.string(),
      customIcon: z.string().optional(),
      enabled: z.boolean(),
      order: z.number(),
    })).optional(),
    layout: z.enum(['horizontal', 'vertical', 'grid']).optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    borderColor: z.string().optional(),
  }).optional(),
  scarcity: z.object({
    enabled: z.boolean().optional(),
    totalStock: z.number().optional(),
    soldCount: z.number().optional(),
    message: z.string().optional(),
    variant: z.enum(['warning', 'danger', 'info']).optional(),
    showIcon: z.boolean().optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    borderColor: z.string().optional(),
  }).optional(),
  testimonials: z.object({
    enabled: z.boolean().optional(),
    testimonials: z.array(z.object({
      id: z.string(),
      name: z.string(),
      rating: z.number(),
      comment: z.string(),
      avatar: z.string().optional(),
      location: z.string().optional(),
      verified: z.boolean().optional(),
    })).optional(),
    maxTestimonials: z.number().optional(),
    autoPlay: z.boolean().optional(),
    autoPlayInterval: z.number().optional(),
    showControls: z.boolean().optional(),
    showStars: z.boolean().optional(),
    showAvatars: z.boolean().optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    borderColor: z.string().optional(),
  }).optional(),
  sidebar: z.object({
    enabled: z.boolean().optional(),
    bannerUrl: z.string().nullable().optional(),
    title: z.string().optional(),
    content: z.string().optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    borderColor: z.string().optional(),
    borderRadius: z.string().optional(),
    shadow: z.boolean().optional(),
  }).optional(),
});

settingsRouter.patch("/", async (c) => {
  try {
    const productId = c.req.param("productId");
    const body = await c.req.json();

    console.log('Recebendo configurações para produto:', productId);
    console.log('Dados recebidos:', JSON.stringify(body, null, 2));

    const validatedData = updateSettingsSchema.parse(body);

    // Verificar se o produto existe e o usuário tem permissão
    const product = await db.product.findFirst({
      where: {
        id: productId,
        organization: {
          members: {
            some: {
              userId: c.get("userId"),
            },
          },
        },
      },
    });

    if (!product) {
      return c.json({ error: "Produto não encontrado" }, 404);
    }

    // Atualizar as configurações do produto
    const updatedProduct = await db.product.update({
      where: { id: productId },
      data: {
        settings: {
          ...(product.settings as Record<string, unknown> || {}),
          ...validatedData,
        },
      },
    });

    return c.json({
      success: true,
      product: updatedProduct,
    });
  } catch (error) {
    console.error("Erro ao atualizar configurações do produto:", error);

    if (error instanceof z.ZodError) {
      return c.json({ error: "Dados inválidos", details: error.errors }, 400);
    }

    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

export { settingsRouter };
