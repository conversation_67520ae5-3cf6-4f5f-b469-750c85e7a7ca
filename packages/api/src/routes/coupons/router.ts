import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";

export const couponsRouter = new Hono()
  .basePath("/coupons")
  .use(authMiddleware)

  // GET /api/coupons - Listar cupons da organização
  .get(
    "/",
    validator(
      "query",
      z.object({
        organizationId: z.string(),
        page: z.string().optional().default("1"),
        limit: z.string().optional().default("10"),
        isActive: z.string().optional(),
        search: z.string().optional(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "List coupons",
      description: "Get list of coupons for the organization",
      responses: {
        200: {
          description: "List of coupons",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { organizationId, page, limit, isActive, search } = c.req.valid("query");

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      const where: any = {
        organizationId,
      };

      if (isActive !== undefined) {
        where.isActive = isActive === "true";
      }

      if (search) {
        where.OR = [
          { code: { contains: search, mode: "insensitive" } },
        ];
      }

      const [coupons, total] = await Promise.all([
        db.coupon.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip,
          take: limitNum,
        }),
        db.coupon.count({ where }),
      ]);

      return c.json({
        coupons,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum),
        },
      });
    },
  )

  // GET /api/coupons/:id - Buscar cupom específico
  .get(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "Get coupon by ID",
      description: "Get a specific coupon by ID",
      responses: {
        200: {
          description: "Coupon details",
        },
        404: {
          description: "Coupon not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      const coupon = await db.coupon.findFirst({
        where: {
          id,
          organizationId: {
            in: await db.organizationMembership
              .findMany({
                where: { userId: user.id },
                select: { organizationId: true },
              })
              .then(memberships => memberships.map(m => m.organizationId)),
          },
        },
      });

      if (!coupon) {
        throw new HTTPException(404, { message: "Cupom não encontrado" });
      }

      return c.json({ coupon });
    },
  )

  // POST /api/coupons - Criar novo cupom
  .post(
    "/",
    validator(
      "json",
      z.object({
        organizationId: z.string(),
        code: z.string().min(1, "Código é obrigatório"),
        type: z.enum(["PERCENTAGE", "FIXED"]),
        valueCents: z.number().min(0, "Valor deve ser maior ou igual a 0"),
        minAmountCents: z.number().min(0).optional(),
        currency: z.string().default("BRL"),
        maxUses: z.number().min(1).optional(),
        isActive: z.boolean().default(true),
        startsAt: z.string().datetime().optional(),
        expiresAt: z.string().datetime().optional(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "Create coupon",
      description: "Create a new coupon",
      responses: {
        201: {
          description: "Coupon created successfully",
        },
        400: {
          description: "Invalid input data",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const data = c.req.valid("json");

      // Verificar se o código já existe na organização
      const existingCoupon = await db.coupon.findFirst({
        where: {
          organizationId: data.organizationId,
          code: data.code,
        },
      });

      if (existingCoupon) {
        throw new HTTPException(400, { message: "Código já existe nesta organização" });
      }

      const coupon = await db.coupon.create({
        data: {
          ...data,
          creatorId: user.id,
          startsAt: data.startsAt ? new Date(data.startsAt) : null,
          expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        },
      });

      return c.json({ coupon }, 201);
    },
  )

  // PUT /api/coupons/:id - Atualizar cupom
  .put(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    validator(
      "json",
      z.object({
        code: z.string().min(1).optional(),
        type: z.enum(["PERCENTAGE", "FIXED"]).optional(),
        valueCents: z.number().min(0).optional(),
        minAmountCents: z.number().min(0).optional(),
        currency: z.string().optional(),
        maxUses: z.number().min(1).optional(),
        isActive: z.boolean().optional(),
        startsAt: z.string().datetime().optional(),
        expiresAt: z.string().datetime().optional(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "Update coupon",
      description: "Update an existing coupon",
      responses: {
        200: {
          description: "Coupon updated successfully",
        },
        404: {
          description: "Coupon not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");
      const data = c.req.valid("json");

      // Verificar se o cupom existe e pertence à organização do usuário
      const existingCoupon = await db.coupon.findFirst({
        where: {
          id,
          organizationId: {
            in: await db.organizationMembership
              .findMany({
                where: { userId: user.id },
                select: { organizationId: true },
              })
              .then(memberships => memberships.map(m => m.organizationId)),
          },
        },
      });

      if (!existingCoupon) {
        throw new HTTPException(404, { message: "Cupom não encontrado" });
      }

      // Se está atualizando o código, verificar se não existe outro cupom com o mesmo código
      if (data.code && data.code !== existingCoupon.code) {
        const codeExists = await db.coupon.findFirst({
          where: {
            organizationId: existingCoupon.organizationId,
            code: data.code,
            id: { not: id },
          },
        });

        if (codeExists) {
          throw new HTTPException(400, { message: "Código já existe nesta organização" });
        }
      }

      const updateData: any = { ...data };
      if (data.startsAt) updateData.startsAt = new Date(data.startsAt);
      if (data.expiresAt) updateData.expiresAt = new Date(data.expiresAt);

      const coupon = await db.coupon.update({
        where: { id },
        data: updateData,
      });

      return c.json({ coupon });
    },
  )

  // DELETE /api/coupons/:id - Deletar cupom
  .delete(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "Delete coupon",
      description: "Delete a coupon",
      responses: {
        200: {
          description: "Coupon deleted successfully",
        },
        404: {
          description: "Coupon not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      // Verificar se o cupom existe e pertence à organização do usuário
      const existingCoupon = await db.coupon.findFirst({
        where: {
          id,
          organizationId: {
            in: await db.organizationMembership
              .findMany({
                where: { userId: user.id },
                select: { organizationId: true },
              })
              .then(memberships => memberships.map(m => m.organizationId)),
          },
        },
      });

      if (!existingCoupon) {
        throw new HTTPException(404, { message: "Cupom não encontrado" });
      }

      await db.coupon.delete({
        where: { id },
      });

      return c.json({ message: "Cupom deletado com sucesso" });
    },
  )

  // PATCH /api/coupons/:id/toggle - Ativar/desativar cupom
  .patch(
    "/:id/toggle",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "Toggle coupon status",
      description: "Toggle the active status of a coupon",
      responses: {
        200: {
          description: "Coupon status updated successfully",
        },
        404: {
          description: "Coupon not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      const coupon = await db.coupon.findFirst({
        where: {
          id,
          organizationId: {
            in: await db.organizationMembership
              .findMany({
                where: { userId: user.id },
                select: { organizationId: true },
              })
              .then(memberships => memberships.map(m => m.organizationId)),
          },
        },
      });

      if (!coupon) {
        throw new HTTPException(404, { message: "Cupom não encontrado" });
      }

      const updatedCoupon = await db.coupon.update({
        where: { id },
        data: { isActive: !coupon.isActive },
      });

      return c.json({ coupon: updatedCoupon });
    },
  )

  // GET /api/coupons/validate/:code - Validar cupom
  .get(
    "/validate/:code",
    validator(
      "param",
      z.object({
        code: z.string(),
      }),
    ),
    validator(
      "query",
      z.object({
        organizationId: z.string(),
        amountCents: z.string().optional(),
      }),
    ),
    describeRoute({
      tags: ["Coupons"],
      summary: "Validate coupon",
      description: "Validate a coupon code",
      responses: {
        200: {
          description: "Coupon validation result",
        },
        404: {
          description: "Coupon not found",
        },
      },
    }),
    async (c) => {
      const { code } = c.req.valid("param");
      const { organizationId, amountCents } = c.req.valid("query");

      const coupon = await db.coupon.findFirst({
        where: {
          code,
          organizationId,
          isActive: true,
          OR: [
            { startsAt: null },
            { startsAt: { lte: new Date() } },
          ],
          OR: [
            { expiresAt: null },
            { expiresAt: { gte: new Date() } },
          ],
        },
      });

      if (!coupon) {
        throw new HTTPException(404, { message: "Cupom não encontrado ou inválido" });
      }

      // Verificar se ainda pode ser usado
      if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
        throw new HTTPException(400, { message: "Cupom esgotado" });
      }

      // Verificar valor mínimo
      if (amountCents && coupon.minAmountCents && parseInt(amountCents) < coupon.minAmountCents) {
        throw new HTTPException(400, {
          message: `Valor mínimo para este cupom é ${coupon.minAmountCents / 100}`
        });
      }

      // Calcular desconto
      let discountCents = 0;
      if (coupon.type === "PERCENTAGE") {
        discountCents = Math.round((parseInt(amountCents || "0") * coupon.valueCents) / 10000);
      } else {
        discountCents = coupon.valueCents;
      }

      return c.json({
        valid: true,
        coupon: {
          id: coupon.id,
          code: coupon.code,
          type: coupon.type,
          valueCents: coupon.valueCents,
          discountCents,
          currency: coupon.currency,
        },
      });
    },
  );
