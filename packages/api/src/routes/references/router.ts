import { Hono } from "hono";
import { describeRoute } from "hono-openapi";

export const referencesRouter = new Hono()
  .basePath("/references")

  // GET /api/references/currencies - Listar moedas suportadas
  .get(
    "/currencies",
    describeRoute({
      tags: ["References"],
      summary: "List supported currencies",
      description: "Get list of supported currencies",
      responses: {
        200: {
          description: "List of currencies",
        },
      },
    }),
    async (c) => {
      const currencies = [
        { code: "BRL", name: "Real Brasileiro", symbol: "R$" },
        { code: "USD", name: "Dólar Americano", symbol: "$" },
        { code: "EUR", name: "Euro", symbol: "€" },
        { code: "GBP", name: "Libra Esterlina", symbol: "£" },
        { code: "CAD", name: "Dólar Canadense", symbol: "C$" },
        { code: "AUD", name: "Dólar Australiano", symbol: "A$" },
      ];

      return c.j<PERSON>({ currencies });
    },
  )

  // GET /api/references/languages - Listar idiomas suportados
  .get(
    "/languages",
    describeRoute({
      tags: ["References"],
      summary: "List supported languages",
      description: "Get list of supported languages",
      responses: {
        200: {
          description: "List of languages",
        },
      },
    }),
    async (c) => {
      const languages = [
        { code: "pt-BR", name: "Português (Brasil)" },
        { code: "en-US", name: "Inglês (Estados Unidos)" },
        { code: "es-ES", name: "Espanhol (Espanha)" },
        { code: "fr-FR", name: "Francês (França)" },
        { code: "de-DE", name: "Alemão (Alemanha)" },
        { code: "it-IT", name: "Italiano (Itália)" },
      ];

      return c.json({ languages });
    },
  )

  // GET /api/references/product-formats - Listar formatos de produto
  .get(
    "/product-formats",
    describeRoute({
      tags: ["References"],
      summary: "List product formats",
      description: "Get list of supported product formats",
      responses: {
        200: {
          description: "List of product formats",
        },
      },
    }),
    async (c) => {
      const formats = [
        { id: "COURSE", name: "Curso Online" },
        { id: "EBOOK", name: "E-book" },
        { id: "MENTORSHIP", name: "Mentoria" },
        { id: "SUBSCRIPTION", name: "Assinatura" },
        { id: "BUNDLE", name: "Pacote de Produtos" },
      ];

      return c.json({ formats });
    },
  );