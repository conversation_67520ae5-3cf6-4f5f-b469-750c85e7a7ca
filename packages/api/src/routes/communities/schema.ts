import { z } from "zod";

export const createCommunitySchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  slug: z.string().min(1, "Slug is required").max(50, "Slug too long"),
  description: z.string().optional(),
  shortDescription: z.string().max(200, "Short description too long").optional(),
  thumbnail: z.string().url().optional(),
  banner: z.string().url().optional(),
  isPublic: z.boolean().default(false),
  isActive: z.boolean().default(true),
  maxMembers: z.number().int().positive().optional(),
  priceCents: z.number().int().min(0).optional(),
  currency: z.string().default("BRL"),
  billingType: z.enum(["ONE_TIME", "MONTHLY", "YEARLY"]).default("ONE_TIME"),
  features: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  language: z.string().default("pt-BR"),
  accessType: z.enum(["FREE", "PAID", "INVITE_ONLY"]).default("PAID"),
  inviteCode: z.string().optional(),
  platformType: z.enum(["DISCORD", "TELEGRAM", "WHATSAPP", "CUSTOM"]).optional(),
  platformUrl: z.string().url().optional(),
  platformId: z.string().optional(),
  settings: z.record(z.any()).default({}),
});

export const updateCommunitySchema = createCommunitySchema.partial();

export const joinCommunitySchema = z.object({
  inviteCode: z.string().optional(),
});

export type CreateCommunityData = z.infer<typeof createCommunitySchema>;
export type UpdateCommunityData = z.infer<typeof updateCommunitySchema>;
export type JoinCommunityData = z.infer<typeof joinCommunitySchema>;
