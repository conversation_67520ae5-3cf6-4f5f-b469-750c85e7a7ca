import { Hono } from "hono";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { describeRoute } from "hono-openapi";
import { createCommunitySchema, updateCommunitySchema, joinCommunitySchema } from "./schema";
import { validator } from "hono/validator";

export const communitiesRouter = new Hono()
  .basePath("/communities")
  .use(authMiddleware)

  // GET /api/communities - Listar comunidades
  .get(
    "/",
    describeRoute({
      tags: ["Communities"],
      summary: "List communities",
      description: "Get a list of communities with optional filtering",
      responses: {
        200: {
          description: "Communities retrieved successfully",
        },
      },
    }),
    async (c) => {
      const { organizationId, isPublic, type, search } = c.req.query();

      const where: any = {};
      
      if (organizationId) {
        where.organizationId = organizationId;
      }
      
      if (isPublic !== undefined) {
        where.isPublic = isPublic === "true";
      }
      
      if (type) {
        where.accessType = type;
      }
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { tags: { has: search } },
        ];
      }

      const communities = await db.community.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              logo: true,
            },
          },
          _count: {
            select: {
              members: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({ communities });
    },
  )

  // GET /api/communities/:id - Obter comunidade específica
  .get(
    "/:id",
    describeRoute({
      tags: ["Communities"],
      summary: "Get community",
      description: "Get a specific community by ID",
      responses: {
        200: {
          description: "Community retrieved successfully",
        },
        404: {
          description: "Community not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.param();

      const community = await db.community.findUnique({
        where: { id },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              logo: true,
            },
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
            take: 10,
            orderBy: {
              joinedAt: "desc",
            },
          },
          categories: {
            where: {
              isActive: true,
            },
            orderBy: {
              order: "asc",
            },
          },
          _count: {
            select: {
              members: true,
              posts: true,
            },
          },
        },
      });

      if (!community) {
        throw new HTTPException(404, { message: "Community not found" });
      }

      return c.json({ community });
    },
  )

  // POST /api/communities - Criar comunidade
  .post(
    "/",
    validator("json", createCommunitySchema),
    describeRoute({
      tags: ["Communities"],
      summary: "Create community",
      description: "Create a new community",
      responses: {
        201: {
          description: "Community created successfully",
        },
        400: {
          description: "Invalid input data",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const data = c.req.valid("json");

      // Verificar se o slug já existe na organização
      const existingCommunity = await db.community.findFirst({
        where: {
          organizationId: data.organizationId,
          slug: data.slug,
        },
      });

      if (existingCommunity) {
        throw new HTTPException(400, { message: "Slug já existe nesta organização" });
      }

      const community = await db.community.create({
        data: {
          ...data,
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              logo: true,
            },
          },
        },
      });

      return c.json({ community }, 201);
    },
  )

  // PUT /api/communities/:id - Atualizar comunidade
  .put(
    "/:id",
    validator("json", updateCommunitySchema),
    describeRoute({
      tags: ["Communities"],
      summary: "Update community",
      description: "Update an existing community",
      responses: {
        200: {
          description: "Community updated successfully",
        },
        404: {
          description: "Community not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.param();
      const data = c.req.valid("json");

      const community = await db.community.update({
        where: { id },
        data,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              logo: true,
            },
          },
        },
      });

      return c.json({ community });
    },
  )

  // DELETE /api/communities/:id - Deletar comunidade
  .delete(
    "/:id",
    describeRoute({
      tags: ["Communities"],
      summary: "Delete community",
      description: "Delete a community",
      responses: {
        200: {
          description: "Community deleted successfully",
        },
        404: {
          description: "Community not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.param();

      await db.community.delete({
        where: { id },
      });

      return c.json({ message: "Community deleted successfully" });
    },
  )

  // POST /api/communities/:id/join - Entrar na comunidade
  .post(
    "/:id/join",
    describeRoute({
      tags: ["Communities"],
      summary: "Join community",
      description: "Join a community",
      responses: {
        200: {
          description: "Successfully joined community",
        },
        400: {
          description: "Already a member or community is full",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.param();
      const user = c.get("user");

      const community = await db.community.findUnique({
        where: { id },
      });

      if (!community) {
        throw new HTTPException(404, { message: "Community not found" });
      }

      // Verificar se já é membro
      const existingMember = await db.communityMember.findUnique({
        where: {
          communityId_userId: {
            communityId: id,
            userId: user.id,
          },
        },
      });

      if (existingMember) {
        throw new HTTPException(400, { message: "Already a member of this community" });
      }

      // Verificar limite de membros
      if (community.maxMembers && community.memberCount >= community.maxMembers) {
        throw new HTTPException(400, { message: "Community is full" });
      }

      // Criar membro
      const member = await db.communityMember.create({
        data: {
          communityId: id,
          userId: user.id,
          role: "MEMBER",
          status: "ACTIVE",
        },
      });

      // Atualizar contador de membros
      await db.community.update({
        where: { id },
        data: {
          memberCount: {
            increment: 1,
          },
        },
      });

      return c.json({ member });
    },
  )

  // DELETE /api/communities/:id/leave - Sair da comunidade
  .delete(
    "/:id/leave",
    describeRoute({
      tags: ["Communities"],
      summary: "Leave community",
      description: "Leave a community",
      responses: {
        200: {
          description: "Successfully left community",
        },
        404: {
          description: "Not a member of this community",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.param();
      const user = c.get("user");

      const member = await db.communityMember.findUnique({
        where: {
          communityId_userId: {
            communityId: id,
            userId: user.id,
          },
        },
      });

      if (!member) {
        throw new HTTPException(404, { message: "Not a member of this community" });
      }

      // Deletar membro
      await db.communityMember.delete({
        where: {
          communityId_userId: {
            communityId: id,
            userId: user.id,
          },
        },
      });

      // Atualizar contador de membros
      await db.community.update({
        where: { id },
        data: {
          memberCount: {
            decrement: 1,
          },
        },
      });

      return c.json({ message: "Successfully left community" });
    },
  );
