import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";

// Schemas para planos de agentes de IA
const AgentPlanSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  price: z.number(), // em centavos
  currency: z.string().default("BRL"),
  features: z.array(z.string()),
  limits: z.object({
    maxAgents: z.number(),
    maxInteractions: z.number(), // por mês
    maxIntegrations: z.number(),
    maxTemplates: z.number(),
    maxTrainingDocuments: z.number(),
    maxConversations: z.number(), // por mês
  }),
  addOns: z.array(z.object({
    id: z.string(),
    name: z.string(),
    price: z.number(), // em centavos
    description: z.string(),
  })),
  isPopular: z.boolean().default(false),
  isEnterprise: z.boolean().default(false),
});

const UsageMetricsSchema = z.object({
  organizationId: z.string(),
  period: z.string(), // YYYY-MM
  totalInteractions: z.number(),
  totalAgents: z.number(),
  totalIntegrations: z.number(),
  totalTemplates: z.number(),
  totalTrainingDocuments: z.number(),
  totalConversations: z.number(),
  overageInteractions: z.number().default(0),
  overageConversations: z.number().default(0),
});

// Planos pré-definidos
const AGENT_PLANS = [
  {
    id: "basic",
    name: "Básico",
    description: "Perfeito para começar com automação básica",
    price: 9700, // R$ 97,00
    currency: "BRL",
    features: [
      "1 Agente de Vendas",
      "500 interações/mês",
      "WhatsApp + Chat",
      "Relatórios básicos",
      "Suporte por email",
      "Templates básicos",
    ],
    limits: {
      maxAgents: 1,
      maxInteractions: 500,
      maxIntegrations: 2,
      maxTemplates: 5,
      maxTrainingDocuments: 10,
      maxConversations: 1000,
    },
    addOns: [
      {
        id: "extra_interactions",
        name: "Interações Extras",
        price: 10, // R$ 0,10 por interação
        description: "Interações adicionais além do limite do plano",
      },
    ],
    isPopular: false,
    isEnterprise: false,
  },
  {
    id: "professional",
    name: "Profissional",
    description: "Ideal para negócios em crescimento",
    price: 19700, // R$ 197,00
    currency: "BRL",
    features: [
      "3 Agentes (Vendas + Suporte + Onboarding)",
      "2.000 interações/mês",
      "Todas as integrações",
      "Analytics avançados",
      "Suporte prioritário",
      "Templates premium",
      "Treinamento personalizado",
    ],
    limits: {
      maxAgents: 3,
      maxInteractions: 2000,
      maxIntegrations: 5,
      maxTemplates: 20,
      maxTrainingDocuments: 50,
      maxConversations: 5000,
    },
    addOns: [
      {
        id: "extra_interactions",
        name: "Interações Extras",
        price: 8, // R$ 0,08 por interação
        description: "Interações adicionais além do limite do plano",
      },
      {
        id: "premium_integrations",
        name: "Integrações Premium",
        price: 5000, // R$ 50,00 por integração
        description: "Integrações avançadas (CRM, ERP, etc.)",
      },
    ],
    isPopular: true,
    isEnterprise: false,
  },
  {
    id: "enterprise",
    name: "Empresarial",
    description: "Para empresas que precisam de escala",
    price: 39700, // R$ 397,00
    currency: "BRL",
    features: [
      "Agentes ilimitados",
      "10.000 interações/mês",
      "API completa",
      "Treinamento personalizado",
      "Suporte dedicado",
      "White-label",
      "Analytics customizados",
      "Integração com sistemas existentes",
    ],
    limits: {
      maxAgents: -1, // ilimitado
      maxInteractions: 10000,
      maxIntegrations: -1, // ilimitado
      maxTemplates: -1, // ilimitado
      maxTrainingDocuments: -1, // ilimitado
      maxConversations: 25000,
    },
    addOns: [
      {
        id: "extra_interactions",
        name: "Interações Extras",
        price: 5, // R$ 0,05 por interação
        description: "Interações adicionais além do limite do plano",
      },
      {
        id: "white_label",
        name: "White-label",
        price: 100000, // R$ 1.000,00
        description: "Personalização completa da marca",
      },
      {
        id: "custom_training",
        name: "Treinamento Personalizado",
        price: 50000, // R$ 500,00
        description: "Sessão de treinamento personalizada",
      },
    ],
    isPopular: false,
    isEnterprise: true,
  },
];

export const aiAgentsPricingRouter = createTRPCRouter({
  // Listar planos disponíveis
  getPlans: protectedProcedure
    .query(async () => {
      return AGENT_PLANS;
    }),

  // Obter plano específico
  getPlan: protectedProcedure
    .input(z.object({ planId: z.string() }))
    .query(async ({ input }) => {
      const plan = AGENT_PLANS.find(p => p.id === input.planId);
      
      if (!plan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Plano não encontrado",
        });
      }

      return plan;
    }),

  // Calcular preço com add-ons
  calculatePrice: protectedProcedure
    .input(z.object({
      planId: z.string(),
      addOns: z.array(z.object({
        id: z.string(),
        quantity: z.number().min(1),
      })).default([]),
      customUsage: z.object({
        interactions: z.number().min(0).optional(),
        conversations: z.number().min(0).optional(),
      }).optional(),
    }))
    .query(async ({ input }) => {
      const plan = AGENT_PLANS.find(p => p.id === input.planId);
      
      if (!plan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Plano não encontrado",
        });
      }

      let totalPrice = plan.price;
      const breakdown = {
        plan: {
          name: plan.name,
          price: plan.price,
        },
        addOns: [] as Array<{
          name: string;
          price: number;
          quantity: number;
          total: number;
        }>,
        overage: {
          interactions: 0,
          conversations: 0,
          total: 0,
        },
        total: plan.price,
      };

      // Calcular add-ons
      for (const addOn of input.addOns) {
        const addOnConfig = plan.addOns.find(a => a.id === addOn.id);
        if (addOnConfig) {
          const addOnTotal = addOnConfig.price * addOn.quantity;
          breakdown.addOns.push({
            name: addOnConfig.name,
            price: addOnConfig.price,
            quantity: addOn.quantity,
            total: addOnTotal,
          });
          totalPrice += addOnTotal;
        }
      }

      // Calcular overage se especificado
      if (input.customUsage) {
        const { interactions, conversations } = input.customUsage;
        
        if (interactions && interactions > plan.limits.maxInteractions) {
          const overageInteractions = interactions - plan.limits.maxInteractions;
          const overagePrice = overageInteractions * (plan.addOns.find(a => a.id === "extra_interactions")?.price || 10);
          breakdown.overage.interactions = overagePrice;
          totalPrice += overagePrice;
        }

        if (conversations && conversations > plan.limits.maxConversations) {
          const overageConversations = conversations - plan.limits.maxConversations;
          const overagePrice = overageConversations * 5; // R$ 0,05 por conversa extra
          breakdown.overage.conversations = overagePrice;
          totalPrice += overagePrice;
        }
      }

      breakdown.total = totalPrice;

      return {
        plan,
        breakdown,
        totalPrice,
        currency: plan.currency,
      };
    }),

  // Obter métricas de uso da organização
  getUsageMetrics: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      period: z.string(), // YYYY-MM
    }))
    .query(async ({ input, ctx }) => {
      // Verificar se o usuário tem acesso à organização
      // Em produção, implementar verificação real
      
      // Mock data para demonstração
      const mockUsage: z.infer<typeof UsageMetricsSchema> = {
        organizationId: input.organizationId,
        period: input.period,
        totalInteractions: 1250,
        totalAgents: 2,
        totalIntegrations: 3,
        totalTemplates: 8,
        totalTrainingDocuments: 15,
        totalConversations: 2500,
        overageInteractions: 250, // 1250 - 1000 (limite do plano)
        overageConversations: 0,
      };

      return mockUsage;
    }),

  // Verificar se organização pode usar um plano
  canUsePlan: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      planId: z.string(),
    }))
    .query(async ({ input, ctx }) => {
      const plan = AGENT_PLANS.find(p => p.id === input.planId);
      
      if (!plan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Plano não encontrado",
        });
      }

      // Verificar limites da organização
      // Em produção, implementar verificação real
      const canUse = {
        canUpgrade: true,
        canDowngrade: true,
        currentPlan: "basic",
        restrictions: [] as string[],
      };

      return canUse;
    }),

  // Simular upgrade/downgrade
  simulatePlanChange: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      fromPlanId: z.string(),
      toPlanId: z.string(),
      currentUsage: z.object({
        interactions: z.number(),
        conversations: z.number(),
        agents: z.number(),
      }),
    }))
    .query(async ({ input }) => {
      const fromPlan = AGENT_PLANS.find(p => p.id === input.fromPlanId);
      const toPlan = AGENT_PLANS.find(p => p.id === input.toPlanId);
      
      if (!fromPlan || !toPlan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Plano não encontrado",
        });
      }

      const analysis = {
        fromPlan: fromPlan.name,
        toPlan: toPlan.name,
        priceDifference: toPlan.price - fromPlan.price,
        usageAnalysis: {
          interactions: {
            current: input.currentUsage.interactions,
            fromLimit: fromPlan.limits.maxInteractions,
            toLimit: toPlan.limits.maxInteractions,
            willFit: input.currentUsage.interactions <= toPlan.limits.maxInteractions,
          },
          conversations: {
            current: input.currentUsage.conversations,
            fromLimit: fromPlan.limits.maxConversations,
            toLimit: toPlan.limits.maxConversations,
            willFit: input.currentUsage.conversations <= toPlan.limits.maxConversations,
          },
          agents: {
            current: input.currentUsage.agents,
            fromLimit: fromPlan.limits.maxAgents,
            toLimit: toPlan.limits.maxAgents,
            willFit: toPlan.limits.maxAgents === -1 || input.currentUsage.agents <= toPlan.limits.maxAgents,
          },
        },
        recommendations: [] as string[],
        warnings: [] as string[],
      };

      // Gerar recomendações
      if (toPlan.price > fromPlan.price) {
        analysis.recommendations.push("Upgrade recomendado para melhor performance");
      } else {
        analysis.recommendations.push("Downgrade pode resultar em limitações");
      }

      // Verificar se o uso atual cabe no novo plano
      if (!analysis.usageAnalysis.interactions.willFit) {
        analysis.warnings.push("Uso de interações excede o limite do novo plano");
      }

      if (!analysis.usageAnalysis.conversations.willFit) {
        analysis.warnings.push("Uso de conversas excede o limite do novo plano");
      }

      if (!analysis.usageAnalysis.agents.willFit) {
        analysis.warnings.push("Número de agentes excede o limite do novo plano");
      }

      return analysis;
    }),

  // Obter histórico de preços
  getPricingHistory: protectedProcedure
    .input(z.object({
      planId: z.string(),
      months: z.number().min(1).max(12).default(6),
    }))
    .query(async ({ input }) => {
      // Mock data para histórico de preços
      const history = Array.from({ length: input.months }, (_, index) => {
        const date = new Date();
        date.setMonth(date.getMonth() - index);
        
        return {
          month: date.toISOString().substring(0, 7), // YYYY-MM
          price: 19700 + (index * 100), // Variação simulada
          currency: "BRL",
        };
      }).reverse();

      return history;
    }),

  // Obter comparação de planos
  comparePlans: protectedProcedure
    .input(z.object({
      planIds: z.array(z.string()).min(2).max(3),
    }))
    .query(async ({ input }) => {
      const plans = input.planIds
        .map(id => AGENT_PLANS.find(p => p.id === id))
        .filter(Boolean);

      if (plans.length !== input.planIds.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Um ou mais planos não encontrados",
        });
      }

      const comparison = {
        plans: plans.map(plan => ({
          id: plan!.id,
          name: plan!.name,
          price: plan!.price,
          features: plan!.features,
          limits: plan!.limits,
        })),
        differences: {
          price: {
            min: Math.min(...plans.map(p => p!.price)),
            max: Math.max(...plans.map(p => p!.price)),
            range: Math.max(...plans.map(p => p!.price)) - Math.min(...plans.map(p => p!.price)),
          },
          features: {
            common: plans[0]!.features.filter(feature => 
              plans.every(plan => plan!.features.includes(feature))
            ),
            unique: plans.map(plan => ({
              planId: plan!.id,
              planName: plan!.name,
              features: plan!.features.filter(feature => 
                !plans[0]!.features.includes(feature)
              ),
            })),
          },
        },
      };

      return comparison;
    }),
});
