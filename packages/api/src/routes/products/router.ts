import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { checkoutsRouter } from "./checkouts";
import { settingsRouter } from "../product-settings";

export const productsRouter = new Hono()
  .basePath("/products")
  .use(authMiddleware)

  // GET /api/products - Listar produtos do usuário
  .get(
    "/",
    validator(
      "query",
      z.object({
        organizationId: z.string(),
        page: z.string().optional().default("1"),
        limit: z.string().optional().default("10"),
        status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).optional(),
        type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]).optional(),
        search: z.string().optional(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "List products",
      description: "Get list of products for the authenticated user",
      responses: {
        200: {
          description: "List of products",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { organizationId, page, limit, status, type, search } = c.req.valid("query");


      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      const where: any = {
        creatorId: user.id,
        organizationId,
      };

      if (status) where.status = status;
      if (type) where.type = type;
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ];
      }

      const [products, total] = await Promise.all([
        db.product.findMany({
          where,
          include: {
            category: true,
            enrollments: true,
            orders: true,
            _count: {
              select: {
                enrollments: true,
                orders: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limitNum,
        }),
        db.product.count({ where }),
      ]);

      return c.json({
        products,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum),
        },
      });
    },
  )

  // GET /api/products/:id - Buscar produto específico
  .get(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Get product by ID",
      description: "Get a specific product by ID",
      responses: {
        200: {
          description: "Product details",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      const product = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
        include: {
          category: true,
          modules: {
            include: {
              lessons: true,
            },
            orderBy: { order: "asc" },
          },
          enrollments: true,
          orders: true,
          offers: true,
          assets: true,
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      return c.json({ product });
    },
  )

  // POST /api/products - Criar novo produto
  .post(
    "/",
    validator(
      "json",
      z.object({
        organizationId: z.string(),
        name: z.string().min(1, "Nome é obrigatório"),
        slug: z.string().min(1, "Slug é obrigatório"),
        description: z.string().optional(),
        shortDescription: z.string().optional(),
        priceCents: z.number().min(0, "Preço deve ser maior ou igual a 0"),
        comparePriceCents: z.number().min(0).optional(),
        currency: z.string().default("BRL"),
        type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
        status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).default("DRAFT"),
        visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).default("PRIVATE"),
        categoryId: z.string().optional(),
        thumbnail: z.string().optional(),
        gallery: z.array(z.string()).default([]),
        tags: z.array(z.string()).default([]),
        features: z.array(z.string()).default([]),
        requirements: z.array(z.string()).default([]),
        duration: z.number().min(0).optional(),
        level: z.string().optional(),
        language: z.string().default("pt-BR"),
        certificate: z.boolean().default(false),
        downloadable: z.boolean().default(false),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
  settings: z.record(z.any()).default({}),

  // Checkout & Conversion Settings
  guaranteeType: z.enum(["NONE", "7_DAYS", "30_DAYS", "60_DAYS", "90_DAYS"]).default("30_DAYS"),
  guaranteeText: z.string().optional(),
  showTestimonials: z.boolean().default(true),
  showUrgency: z.boolean().default(true),
  showScarcity: z.boolean().default(true),
  showTrustBadges: z.boolean().default(true),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Create product",
      description: "Create a new product",
      responses: {
        201: {
          description: "Product created successfully",
        },
        400: {
          description: "Invalid input data",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const data = c.req.valid("json");

      // Verificar se o slug já existe na organização
      const existingProduct = await db.product.findFirst({
        where: {
          organizationId: data.organizationId,
          slug: data.slug,
        },
      });

      if (existingProduct) {
        throw new HTTPException(400, { message: "Slug já existe nesta organização" });
      }

      const product = await db.product.create({
        data: {
          ...data,
          creatorId: user.id,
        },
        include: {
          category: true,
        },
      });

      return c.json({ product }, 201);
    },
  )

  // PUT /api/products/:id - Atualizar produto
  .put(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    validator(
      "json",
      z.object({
        name: z.string().min(1).optional(),
        slug: z.string().min(1).optional(),
        description: z.string().optional(),
        shortDescription: z.string().optional(),
        priceCents: z.number().min(0).optional(),
        comparePriceCents: z.number().min(0).optional(),
        currency: z.string().optional(),
        type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]).optional(),
        status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).optional(),
        visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).optional(),
        categoryId: z.string().optional(),
        thumbnail: z.string().optional(),
        gallery: z.array(z.string()).optional(),
        tags: z.array(z.string()).optional(),
        features: z.array(z.string()).optional(),
        requirements: z.array(z.string()).optional(),
        duration: z.number().min(0).optional(),
        level: z.string().optional(),
        language: z.string().optional(),
        certificate: z.boolean().optional(),
        downloadable: z.boolean().optional(),
        checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).optional(),
        settings: z.record(z.any()).optional(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Update product",
      description: "Update an existing product",
      responses: {
        200: {
          description: "Product updated successfully",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");
      const data = c.req.valid("json");

      // Verificar se o produto existe e pertence ao usuário
      const existingProduct = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
      });

      if (!existingProduct) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      // Se está atualizando o slug, verificar se não existe outro produto com o mesmo slug
      if (data.slug && data.slug !== existingProduct.slug) {
        const slugExists = await db.product.findFirst({
          where: {
            organizationId: existingProduct.organizationId,
            slug: data.slug,
            id: { not: id },
          },
        });

        if (slugExists) {
          throw new HTTPException(400, { message: "Slug já existe nesta organização" });
        }
      }

      const product = await db.product.update({
        where: { id },
        data,
        include: {
          category: true,
        },
      });

      return c.json({ product });
    },
  )

  // DELETE /api/products/:id - Deletar produto
  .delete(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Delete product",
      description: "Delete a product",
      responses: {
        200: {
          description: "Product deleted successfully",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      // Verificar se o produto existe e pertence ao usuário
      const existingProduct = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
      });

      if (!existingProduct) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      await db.product.delete({
        where: { id },
      });

      return c.json({ message: "Produto deletado com sucesso" });
    },
  )

  // PATCH /api/products/:id/status - Atualizar status do produto
  .patch(
    "/:id/status",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    validator(
      "json",
      z.object({
        status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Update product status",
      description: "Update the status of a product",
      responses: {
        200: {
          description: "Product status updated successfully",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");
      const { status } = c.req.valid("json");

      const product = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      const updatedProduct = await db.product.update({
        where: { id },
        data: { status },
        include: {
          category: true,
        },
      });

      return c.json({ product: updatedProduct });
    },
  )

  // GET /api/products/:id/analytics - Analytics do produto
  .get(
    "/:id/analytics",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Get product analytics",
      description: "Get analytics data for a specific product",
      responses: {
        200: {
          description: "Product analytics data",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      const product = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      // Buscar dados de analytics
      const [
        totalSales,
        totalRevenue,
        enrollments,
        recentOrders,
        monthlyRevenue,
      ] = await Promise.all([
        db.order.count({
          where: {
            productId: id,
            status: "COMPLETED",
          },
        }),
        db.order.aggregate({
          where: {
            productId: id,
            status: "COMPLETED",
          },
          _sum: {
            totalCents: true,
          },
        }),
        db.courseEnrollment.count({
          where: {
            productId: id,
          },
        }),
        db.order.findMany({
          where: {
            productId: id,
          },
          include: {
            buyer: {
              select: {
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        }),
        // Revenue dos últimos 6 meses
        db.order.groupBy({
          by: ["createdAt"],
          where: {
            productId: id,
            status: "COMPLETED",
            createdAt: {
              gte: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000), // 6 meses atrás
            },
          },
          _sum: {
            totalCents: true,
          },
          _count: {
            id: true,
          },
        }),
      ]);

      return c.json({
        analytics: {
          totalSales,
          totalRevenue: totalRevenue._sum.totalCents || 0,
          enrollments,
          recentOrders,
          monthlyRevenue,
        },
      });
    },
  )

  // PUT /api/products/:id/configuration - Atualizar configurações do produto
  .put(
    "/:id/configuration",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    validator(
      "json",
      z.object({
        // Informações Básicas
        name: z.string().min(1).max(60).optional(),
        description: z.string().max(200).optional(),
        thumbnail: z.string().optional(),

        // Suporte ao Comprador
        sellerName: z.string().optional(),
        sellerEmail: z.string().email().optional(),
        sellerPhone: z.string().optional(),

        // Preferências
        categoryId: z.string().optional(),
        format: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]).optional(),
        language: z.string().optional(),
        currency: z.string().optional(),
        salesPageUrl: z.string().url().optional().or(z.literal("")),

        // Status
        isActive: z.boolean().optional(),

        // Recuperação Ativa
        activeRecoveryEnabled: z.boolean().optional(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Update product configuration",
      description: "Update comprehensive product configuration settings",
      responses: {
        200: {
          description: "Product configuration updated successfully",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");
      const data = c.req.valid("json");

      // Verificar se o produto existe e pertence ao usuário
      const existingProduct = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
      });

      if (!existingProduct) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      // Preparar dados para atualização
      const updateData: any = {};
      const settingsUpdate: any = existingProduct.settings ? { ...existingProduct.settings } : {};

      // Informações Básicas
      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.thumbnail !== undefined) updateData.thumbnail = data.thumbnail;

      // Suporte ao Comprador (armazenar em settings)
      if (data.sellerName !== undefined) settingsUpdate.sellerName = data.sellerName;
      if (data.sellerEmail !== undefined) settingsUpdate.sellerEmail = data.sellerEmail;
      if (data.sellerPhone !== undefined) settingsUpdate.sellerPhone = data.sellerPhone;

      // Preferências
      if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
      if (data.format !== undefined) updateData.type = data.format;
      if (data.language !== undefined) updateData.language = data.language;
      if (data.currency !== undefined) updateData.currency = data.currency;
      if (data.salesPageUrl !== undefined) settingsUpdate.salesPageUrl = data.salesPageUrl;

      // Status
      if (data.isActive !== undefined) {
        updateData.status = data.isActive ? "PUBLISHED" : "DRAFT";
      }

      // Recuperação Ativa (armazenar em settings)
      if (data.activeRecoveryEnabled !== undefined) {
        settingsUpdate.activeRecoveryEnabled = data.activeRecoveryEnabled;
      }

      // Atualizar settings
      updateData.settings = settingsUpdate;

      const updatedProduct = await db.product.update({
        where: { id },
        data: updateData,
        include: {
          category: true,
          organization: true,
        },
      });

      return c.json({ product: updatedProduct });
    },
  )

  // GET /api/products/:id/configuration - Obter configurações do produto
  .get(
    "/:id/configuration",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Products"],
      summary: "Get product configuration",
      description: "Get comprehensive product configuration settings",
      responses: {
        200: {
          description: "Product configuration data",
        },
        404: {
          description: "Product not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const { id } = c.req.valid("param");

      const product = await db.product.findFirst({
        where: {
          id,
          creatorId: user.id,
        },
        include: {
          category: true,
          organization: true,
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      // Formatar dados de configuração
      const configuration = {
        // Informações Básicas
        name: product.name,
        description: product.description,
        thumbnail: product.thumbnail,

        // Suporte ao Comprador
        sellerName: (product.settings as any)?.sellerName || "",
        sellerEmail: (product.settings as any)?.sellerEmail || "",
        sellerPhone: (product.settings as any)?.sellerPhone || "",

        // Preferências
        categoryId: product.categoryId,
        format: product.type,
        language: product.language,
        currency: product.currency,
        salesPageUrl: (product.settings as any)?.salesPageUrl || "",

        // Status
        isActive: product.status === "PUBLISHED",

        // Recuperação Ativa
        activeRecoveryEnabled: (product.settings as any)?.activeRecoveryEnabled || false,
      };

      return c.json({ configuration, product });
    },
  )

  // Mount checkouts router
  .route("/", checkoutsRouter)

  // Mount settings router
  .route("/", settingsRouter);
