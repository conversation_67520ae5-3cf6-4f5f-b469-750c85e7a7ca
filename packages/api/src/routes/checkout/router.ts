import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { checkoutFormSchema } from "./schema";
import { processPayment } from "./lib/process-payment";
import { getProduct } from "./lib/get-product";

export const checkoutRouter = new Hono()
  .basePath("/checkout")
  .get(
    "/product/:productId",
    validator(
      "param",
      z.object({
        productId: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Checkout"],
      summary: "Get product for checkout",
      description: "Get product information for checkout page",
      responses: {
        200: {
          description: "Product information",
        },
      },
    }),
    async (c) => {
      const { productId } = c.req.valid("param");
      return getProduct(c, productId);
    },
  )
  .post(
    "/process-payment",
    validator(
      "json",
      checkoutFormSchema,
    ),
    describeRoute({
      tags: ["Checkout"],
      summary: "Process payment",
      description: "Process payment for a product",
      responses: {
        200: {
          description: "Payment processed successfully",
        },
      },
    }),
    async (c) => {
      const data = c.req.valid("json");
      return processPayment(c, data);
    },
  );
