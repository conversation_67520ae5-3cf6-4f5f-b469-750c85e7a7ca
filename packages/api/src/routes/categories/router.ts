import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { z } from "zod";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";

export const categoriesRouter = new Hono()
  .basePath("/categories")
  .use(authMiddleware)

  // GET /api/categories - Listar categorias
  .get(
    "/",
    describeRoute({
      tags: ["Categories"],
      summary: "List categories",
      description: "Get list of available categories",
      responses: {
        200: {
          description: "List of categories",
        },
      },
    }),
    async (c) => {
      const categories = await db.category.findMany({
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      return c.json({ categories });
    },
  );