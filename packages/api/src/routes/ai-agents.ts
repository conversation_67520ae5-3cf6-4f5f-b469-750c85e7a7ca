import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { db } from "@repo/database";
import { TRPCError } from "@trpc/server";

const createAgentSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional(),
  type: z.enum(["SALES", "SUPPORT", "ONBOARDING", "COMMUNITY"]),
  personality: z.enum(["FRIENDLY", "PROFESSIONAL", "ENTHUSIASTIC", "TECHNICAL", "CASUAL"]),
  language: z.string().default("pt-BR"),
  systemPrompt: z.string().optional(),
  welcomeMessage: z.string().optional(),
  fallbackMessage: z.string().optional(),
  conversionGoal: z.number().min(0).max(1).optional(),
  leadGoal: z.number().min(0).optional(),
  responseTimeGoal: z.number().min(0).optional(),
  allowEscalation: z.boolean().default(true),
  maxInteractions: z.number().min(1).optional(),
});

const updateAgentSchema = createAgentSchema.partial();

const createIntegrationSchema = z.object({
  agentId: z.string(),
  type: z.enum(["WHATSAPP", "TELEGRAM", "CHAT_WIDGET", "EMAIL", "CRM"]),
  name: z.string(),
  settings: z.record(z.any()),
});

const createConversationSchema = z.object({
  agentId: z.string(),
  customerName: z.string().optional(),
  customerEmail: z.string().optional(),
  customerPhone: z.string().optional(),
  channel: z.string(),
  initialMessage: z.string().optional(),
});

const sendMessageSchema = z.object({
  conversationId: z.string(),
  message: z.string(),
  role: z.enum(["user", "assistant", "system"]).default("user"),
});

export const aiAgentsRouter = createTRPCRouter({
  // Listar agentes da organização
  list: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      status: z.enum(["ACTIVE", "INACTIVE", "PAUSED", "TRAINING"]).optional(),
      type: z.enum(["SALES", "SUPPORT", "ONBOARDING", "COMMUNITY"]).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { organizationId, status, type } = input;
      
      // Verificar se o usuário tem acesso à organização
      const member = await db.member.findFirst({
        where: {
          organizationId,
          userId: ctx.user.id,
        },
      });

      if (!member) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Você não tem acesso a esta organização",
        });
      }

      const agents = await db.aiAgent.findMany({
        where: {
          organizationId,
          ...(status && { status }),
          ...(type && { type }),
        },
        include: {
          integrations: true,
          conversations: {
            take: 5,
            orderBy: { lastMessageAt: "desc" },
          },
          _count: {
            select: {
              conversations: true,
              integrations: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return agents;
    }),

  // Obter agente específico
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const agent = await db.aiAgent.findFirst({
        where: {
          id: input.id,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
        include: {
          integrations: true,
          templates: true,
          conversations: {
            take: 10,
            orderBy: { lastMessageAt: "desc" },
          },
          _count: {
            select: {
              conversations: true,
              integrations: true,
              templates: true,
            },
          },
        },
      });

      if (!agent) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Agente não encontrado",
        });
      }

      return agent;
    }),

  // Criar novo agente
  create: protectedProcedure
    .input(createAgentSchema.extend({
      organizationId: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { organizationId, ...agentData } = input;

      // Verificar se o usuário tem acesso à organização
      const member = await db.member.findFirst({
        where: {
          organizationId,
          userId: ctx.user.id,
        },
      });

      if (!member) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Você não tem acesso a esta organização",
        });
      }

      const agent = await db.aiAgent.create({
        data: {
          ...agentData,
          organizationId,
          status: "INACTIVE",
        },
      });

      return agent;
    }),

  // Atualizar agente
  update: protectedProcedure
    .input(updateAgentSchema.extend({
      id: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, ...updateData } = input;

      // Verificar se o agente existe e o usuário tem acesso
      const existingAgent = await db.aiAgent.findFirst({
        where: {
          id,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
      });

      if (!existingAgent) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Agente não encontrado",
        });
      }

      const agent = await db.aiAgent.update({
        where: { id },
        data: updateData,
      });

      return agent;
    }),

  // Deletar agente
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Verificar se o agente existe e o usuário tem acesso
      const existingAgent = await db.aiAgent.findFirst({
        where: {
          id: input.id,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
      });

      if (!existingAgent) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Agente não encontrado",
        });
      }

      await db.aiAgent.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  // Ativar/Desativar agente
  toggleStatus: protectedProcedure
    .input(z.object({
      id: z.string(),
      status: z.enum(["ACTIVE", "INACTIVE", "PAUSED", "TRAINING"]),
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, status } = input;

      // Verificar se o agente existe e o usuário tem acesso
      const existingAgent = await db.aiAgent.findFirst({
        where: {
          id,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
      });

      if (!existingAgent) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Agente não encontrado",
        });
      }

      const agent = await db.aiAgent.update({
        where: { id },
        data: { 
          status,
          lastActiveAt: status === "ACTIVE" ? new Date() : null,
        },
      });

      return agent;
    }),

  // Criar integração
  createIntegration: protectedProcedure
    .input(createIntegrationSchema)
    .mutation(async ({ input, ctx }) => {
      const { agentId, ...integrationData } = input;

      // Verificar se o agente existe e o usuário tem acesso
      const agent = await db.aiAgent.findFirst({
        where: {
          id: agentId,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
      });

      if (!agent) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Agente não encontrado",
        });
      }

      const integration = await db.agentIntegration.create({
        data: {
          ...integrationData,
          agentId,
        },
      });

      return integration;
    }),

  // Listar conversas
  listConversations: protectedProcedure
    .input(z.object({
      agentId: z.string().optional(),
      organizationId: z.string(),
      status: z.string().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      const { agentId, organizationId, status, limit, offset } = input;

      // Verificar se o usuário tem acesso à organização
      const member = await db.member.findFirst({
        where: {
          organizationId,
          userId: ctx.user.id,
        },
      });

      if (!member) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Você não tem acesso a esta organização",
        });
      }

      const conversations = await db.agentConversation.findMany({
        where: {
          organizationId,
          ...(agentId && { agentId }),
          ...(status && { status }),
        },
        include: {
          agent: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
        orderBy: { lastMessageAt: "desc" },
        take: limit,
        skip: offset,
      });

      const total = await db.agentConversation.count({
        where: {
          organizationId,
          ...(agentId && { agentId }),
          ...(status && { status }),
        },
      });

      return {
        conversations,
        total,
        hasMore: offset + limit < total,
      };
    }),

  // Criar nova conversa
  createConversation: protectedProcedure
    .input(createConversationSchema)
    .mutation(async ({ input, ctx }) => {
      const { agentId, ...conversationData } = input;

      // Verificar se o agente existe e o usuário tem acesso
      const agent = await db.aiAgent.findFirst({
        where: {
          id: agentId,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
      });

      if (!agent) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Agente não encontrado",
        });
      }

      const conversation = await db.agentConversation.create({
        data: {
          ...conversationData,
          agentId,
          organizationId: agent.organizationId,
          messages: input.initialMessage ? [
            {
              role: "user",
              content: input.initialMessage,
              timestamp: new Date().toISOString(),
            }
          ] : [],
        },
      });

      return conversation;
    }),

  // Enviar mensagem
  sendMessage: protectedProcedure
    .input(sendMessageSchema)
    .mutation(async ({ input, ctx }) => {
      const { conversationId, message, role } = input;

      // Verificar se a conversa existe e o usuário tem acesso
      const conversation = await db.agentConversation.findFirst({
        where: {
          id: conversationId,
          organization: {
            members: {
              some: {
                userId: ctx.user.id,
              },
            },
          },
        },
        include: {
          agent: true,
        },
      });

      if (!conversation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversa não encontrada",
        });
      }

      // Adicionar mensagem à conversa
      const currentMessages = conversation.messages as any[];
      const newMessage = {
        role,
        content: message,
        timestamp: new Date().toISOString(),
      };

      const updatedMessages = [...currentMessages, newMessage];

      // Atualizar conversa
      const updatedConversation = await db.agentConversation.update({
        where: { id: conversationId },
        data: {
          messages: updatedMessages,
          lastMessageAt: new Date(),
          interactionCount: conversation.interactionCount + 1,
        },
      });

      // TODO: Aqui seria onde chamaria a API de IA para gerar a resposta
      // Por enquanto, retornamos a conversa atualizada
      
      return updatedConversation;
    }),

  // Obter analytics
  getAnalytics: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      agentId: z.string().optional(),
      period: z.enum(["7d", "30d", "90d", "1y"]).default("30d"),
    }))
    .query(async ({ input, ctx }) => {
      const { organizationId, agentId, period } = input;

      // Verificar se o usuário tem acesso à organização
      const member = await db.member.findFirst({
        where: {
          organizationId,
          userId: ctx.user.id,
        },
      });

      if (!member) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Você não tem acesso a esta organização",
        });
      }

      // Calcular período
      const now = new Date();
      const periodMap = {
        "7d": 7,
        "30d": 30,
        "90d": 90,
        "1y": 365,
      };
      const days = periodMap[period];
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // Buscar conversas no período
      const conversations = await db.agentConversation.findMany({
        where: {
          organizationId,
          ...(agentId && { agentId }),
          startedAt: {
            gte: startDate,
          },
        },
        include: {
          agent: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      // Calcular métricas
      const totalInteractions = conversations.reduce((sum, conv) => sum + conv.interactionCount, 0);
      const totalLeads = conversations.filter(conv => conv.leadGenerated).length;
      const totalConversions = conversations.filter(conv => conv.conversionGenerated).length;
      const totalEscalations = conversations.filter(conv => conv.escalatedToHuman).length;

      const avgResponseTime = conversations.length > 0 
        ? conversations.reduce((sum, conv) => sum + (conv.lastMessageAt.getTime() - conv.startedAt.getTime()) / 1000, 0) / conversations.length
        : 0;

      const satisfactionRatings = conversations
        .filter(conv => conv.satisfactionRating)
        .map(conv => conv.satisfactionRating!);
      const avgSatisfaction = satisfactionRatings.length > 0
        ? satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length
        : 0;

      // Breakdown por canal
      const channelBreakdown = conversations.reduce((acc, conv) => {
        acc[conv.channel] = (acc[conv.channel] || 0) + conv.interactionCount;
        return acc;
      }, {} as Record<string, number>);

      return {
        period,
        totalInteractions,
        totalLeads,
        totalConversions,
        totalEscalations,
        avgResponseTime,
        avgSatisfaction,
        channelBreakdown,
        conversations: conversations.length,
      };
    }),
});
