import { z } from "zod";

// Schemas para validação
const WhatsAppMessageSchema = z.object({
  to: z.string(),
  type: z.enum(["text", "template", "interactive", "image", "document"]),
  text: z.object({
    body: z.string(),
  }).optional(),
  template: z.object({
    name: z.string(),
    language: z.object({
      code: z.string(),
    }),
    components: z.array(z.any()).optional(),
  }).optional(),
  interactive: z.object({
    type: z.enum(["button", "list", "product", "product_list"]),
    header: z.object({
      type: z.string(),
      text: z.string().optional(),
    }).optional(),
    body: z.object({
      text: z.string(),
    }),
    footer: z.object({
      text: z.string(),
    }).optional(),
    action: z.object({
      buttons: z.array(z.object({
        type: z.string(),
        reply: z.object({
          id: z.string(),
          title: z.string(),
        }),
      })).optional(),
      sections: z.array(z.any()).optional(),
    }),
  }).optional(),
  image: z.object({
    link: z.string().optional(),
    id: z.string().optional(),
    caption: z.string().optional(),
  }).optional(),
  document: z.object({
    link: z.string().optional(),
    id: z.string().optional(),
    filename: z.string().optional(),
    caption: z.string().optional(),
  }).optional(),
});

const WhatsAppWebhookSchema = z.object({
  object: z.string(),
  entry: z.array(z.object({
    id: z.string(),
    changes: z.array(z.object({
      value: z.object({
        messaging_product: z.string(),
        metadata: z.object({
          display_phone_number: z.string(),
          phone_number_id: z.string(),
        }),
        contacts: z.array(z.object({
          profile: z.object({
            name: z.string(),
          }),
          wa_id: z.string(),
        })).optional(),
        messages: z.array(z.object({
          from: z.string(),
          id: z.string(),
          timestamp: z.string(),
          type: z.string(),
          text: z.object({
            body: z.string(),
          }).optional(),
          interactive: z.object({
            type: z.string(),
            button_reply: z.object({
              id: z.string(),
              title: z.string(),
            }).optional(),
            list_reply: z.object({
              id: z.string(),
              title: z.string(),
            }).optional(),
          }).optional(),
        })).optional(),
        statuses: z.array(z.object({
          id: z.string(),
          status: z.enum(["sent", "delivered", "read", "failed"]),
          timestamp: z.string(),
          recipient_id: z.string(),
        })).optional(),
      }),
      field: z.string(),
    })),
  })),
});

export interface WhatsAppConfig {
  accessToken: string;
  phoneNumberId: string;
  webhookVerifyToken: string;
  apiVersion: string;
}

export interface WhatsAppMessage {
  to: string;
  type: "text" | "template" | "interactive" | "image" | "document";
  text?: {
    body: string;
  };
  template?: {
    name: string;
    language: {
      code: string;
    };
    components?: any[];
  };
  interactive?: {
    type: "button" | "list" | "product" | "product_list";
    header?: {
      type: string;
      text?: string;
    };
    body: {
      text: string;
    };
    footer?: {
      text: string;
    };
    action: {
      buttons?: Array<{
        type: string;
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: any[];
    };
  };
  image?: {
    link?: string;
    id?: string;
    caption?: string;
  };
  document?: {
    link?: string;
    id?: string;
    filename?: string;
    caption?: string;
  };
}

export interface WhatsAppWebhook {
  object: string;
  entry: Array<{
    id: string;
    changes: Array<{
      value: {
        messaging_product: string;
        metadata: {
          display_phone_number: string;
          phone_number_id: string;
        };
        contacts?: Array<{
          profile: {
            name: string;
          };
          wa_id: string;
        }>;
        messages?: Array<{
          from: string;
          id: string;
          timestamp: string;
          type: string;
          text?: {
            body: string;
          };
          interactive?: {
            type: string;
            button_reply?: {
              id: string;
              title: string;
            };
            list_reply?: {
              id: string;
              title: string;
            };
          };
        }>;
        statuses?: Array<{
          id: string;
          status: "sent" | "delivered" | "read" | "failed";
          timestamp: string;
          recipient_id: string;
        }>;
      };
      field: string;
    }>;
  }>;
}

export class WhatsAppService {
  private config: WhatsAppConfig;
  private baseUrl: string;

  constructor(config: WhatsAppConfig) {
    this.config = config;
    this.baseUrl = `https://graph.facebook.com/v${config.apiVersion}`;
  }

  /**
   * Enviar mensagem de texto
   */
  async sendTextMessage(to: string, message: string): Promise<any> {
    const payload: WhatsAppMessage = {
      to,
      type: "text",
      text: {
        body: message,
      },
    };

    return this.sendMessage(payload);
  }

  /**
   * Enviar mensagem com template
   */
  async sendTemplateMessage(
    to: string,
    templateName: string,
    language: string = "pt_BR",
    components?: any[]
  ): Promise<any> {
    const payload: WhatsAppMessage = {
      to,
      type: "template",
      template: {
        name: templateName,
        language: {
          code: language,
        },
        components,
      },
    };

    return this.sendMessage(payload);
  }

  /**
   * Enviar mensagem interativa com botões
   */
  async sendInteractiveMessage(
    to: string,
    body: string,
    buttons: Array<{ id: string; title: string }>,
    header?: string,
    footer?: string
  ): Promise<any> {
    const payload: WhatsAppMessage = {
      to,
      type: "interactive",
      interactive: {
        type: "button",
        header: header ? { type: "text", text: header } : undefined,
        body: { text: body },
        footer: footer ? { text: footer } : undefined,
        action: {
          buttons: buttons.map(button => ({
            type: "reply",
            reply: {
              id: button.id,
              title: button.title,
            },
          })),
        },
      },
    };

    return this.sendMessage(payload);
  }

  /**
   * Enviar mensagem interativa com lista
   */
  async sendListMessage(
    to: string,
    body: string,
    buttonText: string,
    sections: Array<{
      title: string;
      rows: Array<{ id: string; title: string; description?: string }>;
    }>,
    header?: string,
    footer?: string
  ): Promise<any> {
    const payload: WhatsAppMessage = {
      to,
      type: "interactive",
      interactive: {
        type: "list",
        header: header ? { type: "text", text: header } : undefined,
        body: { text: body },
        footer: footer ? { text: footer } : undefined,
        action: {
          sections,
        },
      },
    };

    return this.sendMessage(payload);
  }

  /**
   * Enviar imagem
   */
  async sendImage(
    to: string,
    imageUrl: string,
    caption?: string
  ): Promise<any> {
    const payload: WhatsAppMessage = {
      to,
      type: "image",
      image: {
        link: imageUrl,
        caption,
      },
    };

    return this.sendMessage(payload);
  }

  /**
   * Enviar documento
   */
  async sendDocument(
    to: string,
    documentUrl: string,
    filename?: string,
    caption?: string
  ): Promise<any> {
    const payload: WhatsAppMessage = {
      to,
      type: "document",
      document: {
        link: documentUrl,
        filename,
        caption,
      },
    };

    return this.sendMessage(payload);
  }

  /**
   * Método genérico para enviar mensagens
   */
  private async sendMessage(message: WhatsAppMessage): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/${this.config.phoneNumberId}/messages`,
        {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.config.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(message),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`WhatsApp API Error: ${JSON.stringify(errorData)}`);
      }

      return await response.json();
    } catch (error) {
      console.error("WhatsApp API Error:", error);
      throw error;
    }
  }

  /**
   * Verificar webhook
   */
  verifyWebhook(mode: string, token: string, challenge: string): string | null {
    if (mode === "subscribe" && token === this.config.webhookVerifyToken) {
      return challenge;
    }
    return null;
  }

  /**
   * Processar webhook
   */
  processWebhook(webhookData: any): WhatsAppWebhook | null {
    try {
      const validated = WhatsAppWebhookSchema.parse(webhookData);
      return validated;
    } catch (error) {
      console.error("Invalid webhook data:", error);
      return null;
    }
  }

  /**
   * Extrair mensagens do webhook
   */
  extractMessages(webhook: WhatsAppWebhook): Array<{
    from: string;
    messageId: string;
    timestamp: string;
    type: string;
    text?: string;
    interactive?: {
      type: string;
      buttonId?: string;
      listId?: string;
    };
  }> {
    const messages: Array<{
      from: string;
      messageId: string;
      timestamp: string;
      type: string;
      text?: string;
      interactive?: {
        type: string;
        buttonId?: string;
        listId?: string;
      };
    }> = [];

    for (const entry of webhook.entry) {
      for (const change of entry.changes) {
        if (change.field === "messages" && change.value.messages) {
          for (const message of change.value.messages) {
            const extractedMessage: any = {
              from: message.from,
              messageId: message.id,
              timestamp: message.timestamp,
              type: message.type,
            };

            if (message.text) {
              extractedMessage.text = message.text.body;
            }

            if (message.interactive) {
              extractedMessage.interactive = {
                type: message.interactive.type,
                buttonId: message.interactive.button_reply?.id,
                listId: message.interactive.list_reply?.id,
              };
            }

            messages.push(extractedMessage);
          }
        }
      }
    }

    return messages;
  }

  /**
   * Extrair status de entrega do webhook
   */
  extractStatuses(webhook: WhatsAppWebhook): Array<{
    messageId: string;
    status: string;
    timestamp: string;
    recipientId: string;
  }> {
    const statuses: Array<{
      messageId: string;
      status: string;
      timestamp: string;
      recipientId: string;
    }> = [];

    for (const entry of webhook.entry) {
      for (const change of entry.changes) {
        if (change.field === "messages" && change.value.statuses) {
          for (const status of change.value.statuses) {
            statuses.push({
              messageId: status.id,
              status: status.status,
              timestamp: status.timestamp,
              recipientId: status.recipient_id,
            });
          }
        }
      }
    }

    return statuses;
  }

  /**
   * Obter informações do perfil do usuário
   */
  async getUserProfile(phoneNumber: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/${phoneNumber}`,
        {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${this.config.accessToken}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to get user profile: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting user profile:", error);
      throw error;
    }
  }

  /**
   * Marcar mensagem como lida
   */
  async markAsRead(messageId: string): Promise<any> {
    const payload = {
      messaging_product: "whatsapp",
      status: "read",
      message_id: messageId,
    };

    try {
      const response = await fetch(
        `${this.baseUrl}/${this.config.phoneNumberId}/messages`,
        {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.config.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to mark as read: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error marking as read:", error);
      throw error;
    }
  }
}

// Factory function para criar instância do serviço
export function createWhatsAppService(config: WhatsAppConfig): WhatsAppService {
  return new WhatsAppService(config);
}

// Templates pré-definidos
export const WhatsAppTemplates = {
  WELCOME: {
    name: "welcome_message",
    language: "pt_BR",
    components: [
      {
        type: "body",
        parameters: [
          {
            type: "text",
            text: "{{customer_name}}",
          },
        ],
      },
    ],
  },
  SALES_QUALIFICATION: {
    name: "sales_qualification",
    language: "pt_BR",
    components: [
      {
        type: "body",
        parameters: [
          {
            type: "text",
            text: "{{product_name}}",
          },
        ],
      },
    ],
  },
  SUPPORT_TICKET: {
    name: "support_ticket",
    language: "pt_BR",
    components: [
      {
        type: "body",
        parameters: [
          {
            type: "text",
            text: "{{ticket_id}}",
          },
        ],
      },
    ],
  },
};
