import { auth } from "@repo/auth";
import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";
import { Scalar } from "@scalar/hono-api-reference";
import { Hono } from "hono";
import { openAPISpecs } from "hono-openapi";
import {} from "openapi-merge";
import { mergeOpenApiSchemas } from "./lib/openapi-schema";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";
import { adminRouter } from "./routes/admin/router";
import { aiRouter } from "./routes/ai";
import { analyticsRouter } from "./routes/analytics/router";
import { authRouter } from "./routes/auth";
import { bannerImageRouter } from "./routes/banner-image";
import { categoriesRouter } from "./routes/categories/router";
import { checkoutRouter } from "./routes/checkout/router";
import { communitiesRouter } from "./routes/communities/router";
import { contactRouter } from "./routes/contact/router";
import { customersRouter } from "./routes/customers/router";
import { couponsRouter } from "./routes/coupons/router";
import { dashboardRouter } from "./routes/dashboard/router";
import { healthRouter } from "./routes/health";
import { newsletterRouter } from "./routes/newsletter";
import { offersRouter } from "./routes/offers/router";
import { organizationsRouter } from "./routes/organizations/router";
import { paymentsRouter } from "./routes/payments/router";
import { pixelsRouter } from "./routes/pixels/router";
import { productsRouter } from "./routes/products/router";
import { referencesRouter } from "./routes/references/router";
import { testimonialsRouter } from "./routes/testimonials/router";
import { uploadsRouter } from "./routes/uploads";
import { webhooksRouter } from "./routes/webhooks";

export const app = new Hono().basePath("/api");

app.use(loggerMiddleware);
app.use(corsMiddleware);

// Registrar rotas do Better Auth diretamente
app.all("/auth/*", (c) => {
	return auth.handler(c.req.raw);
});

const appRouter = app
	.route("/", authRouter)
	.route("/", webhooksRouter)
	.route("/", aiRouter)
	.route("/", bannerImageRouter)
	.route("/", analyticsRouter)
	.route("/", dashboardRouter)
	.route("/", uploadsRouter)
	.route("/", paymentsRouter)
	.route("/", checkoutRouter)
	.route("/", communitiesRouter)
	.route("/", productsRouter)
	.route("/", offersRouter)
	.route("/", customersRouter)
	.route("/", pixelsRouter)
	.route("/", categoriesRouter)
	.route("/", referencesRouter)
	.route("/", testimonialsRouter)
	.route("/", couponsRouter)
	.route("/", contactRouter)
	.route("/", newsletterRouter)
	.route("/", organizationsRouter)
	.route("/", adminRouter)
	.route("/", healthRouter);

app.get(
	"/app-openapi",
	openAPISpecs(app, {
		documentation: {
			info: {
				title: `${config.appName} API`,
				version: "1.0.0",
			},
			servers: [
				{
					url: getBaseUrl(),
					description: "API server",
				},
			],
		},
	}),
);

app.get("/openapi", async (c) => {
	const authSchema = await auth.api.generateOpenAPISchema();
	const appSchema = await (
		app.request("/api/app-openapi") as Promise<Response>
	).then((res) => res.json());

	const mergedSchema = mergeOpenApiSchemas({
		appSchema,
		authSchema: authSchema as any,
	});

	return c.json(mergedSchema);
});

app.get(
	"/docs",
	Scalar({
		theme: "saturn",
		url: "/api/openapi",
	}),
);

export type AppRouter = typeof appRouter;
