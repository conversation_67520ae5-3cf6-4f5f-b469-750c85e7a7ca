import { getBaseUrl } from "@repo/utils";
import { cors } from "hono/cors";

export const corsMiddleware = cors({
	origin: [
		getBaseUrl(),
		"https://sup.nextrusti.com",
		"https://cdn.nextrusti.com",
		"https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com"
	],
	allowHeaders: ["Content-Type", "Authorization", "x-amz-content-sha256", "x-amz-date"],
	allowMethods: ["POST", "GET", "OPTIONS", "PUT"],
	exposeHeaders: ["Content-Length", "ETag"],
	maxAge: 600,
	credentials: true,
});
