import type { ActiveOrganization } from "../auth";

export function isOrganizationAdmin(
	organization?: ActiveOrganization | null,
	user?: {
		id: string;
		role?: string | null;
	} | null,
) {
	const userOrganizationRole = organization?.members.find(
		(member) => member.userId === user?.id,
	)?.role;

	return (
		["owner", "admin"].includes(userOrganizationRole ?? "") ||
		isAdmin(user)
	);
}

// Função para verificar se o usuário tem permissões de admin (incluindo SUPER_ADMIN)
export function isAdmin(
	user?: {
		role?: string | null;
	} | null,
) {
	return user?.role === "ADMIN" || user?.role === "SUPER_ADMIN";
}

// Função para verificar se o usuário é super admin
export function isSuperAdmin(
	user?: {
		role?: string | null;
	} | null,
) {
	return user?.role === "SUPER_ADMIN";
}

// Função para verificar se o usuário é vendedor
export function isSeller(
	user?: {
		role?: string | null;
	} | null,
) {
	return user?.role === "SELLER";
}

// Função para verificar se o usuário é cliente
export function isCustomer(
	user?: {
		role?: string | null;
	} | null,
) {
	return user?.role === "CUSTOMER";
}
