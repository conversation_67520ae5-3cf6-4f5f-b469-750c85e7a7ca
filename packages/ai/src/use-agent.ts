"use client";

import { useState, useCallback } from "react";
import { agentService, type AgentConfig, type ConversationContext, type AgentResponse } from "./agent-service";

export interface UseAgentOptions {
  agentConfig: AgentConfig;
  context: ConversationContext;
  onResponse?: (response: AgentResponse) => void;
  onError?: (error: Error) => void;
}

export interface UseAgentReturn {
  sendMessage: (message: string) => Promise<AgentResponse>;
  isProcessing: boolean;
  lastResponse: AgentResponse | null;
  error: Error | null;
  clearError: () => void;
}

export function useAgent(options: UseAgentOptions): UseAgentReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResponse, setLastResponse] = useState<AgentResponse | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const sendMessage = useCallback(async (message: string): Promise<AgentResponse> => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await agentService.processMessage(
        message,
        options.agentConfig,
        options.context
      );

      setLastResponse(response);
      options.onResponse?.(response);
      
      return response;
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Erro desconhecido");
      setError(error);
      options.onError?.(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [options.agentConfig, options.context, options.onResponse, options.onError]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    sendMessage,
    isProcessing,
    lastResponse,
    error,
    clearError,
  };
}

export interface UseAgentStreamOptions {
  agentConfig: AgentConfig;
  context: ConversationContext;
  onChunk?: (chunk: string) => void;
  onComplete?: (fullText: string) => void;
  onError?: (error: Error) => void;
}

export interface UseAgentStreamReturn {
  sendMessage: (message: string) => Promise<void>;
  isStreaming: boolean;
  currentText: string;
  error: Error | null;
  clearError: () => void;
}

export function useAgentStream(options: UseAgentStreamOptions): UseAgentStreamReturn {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentText, setCurrentText] = useState("");
  const [error, setError] = useState<Error | null>(null);

  const sendMessage = useCallback(async (message: string): Promise<void> => {
    setIsStreaming(true);
    setCurrentText("");
    setError(null);

    try {
      const result = await agentService.streamResponse(
        message,
        options.agentConfig,
        options.context
      );

      for await (const chunk of result.textStream) {
        setCurrentText(prev => prev + chunk);
        options.onChunk?.(chunk);
      }

      options.onComplete?.(currentText);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Erro desconhecido");
      setError(error);
      options.onError?.(error);
    } finally {
      setIsStreaming(false);
    }
  }, [options.agentConfig, options.context, options.onChunk, options.onComplete, options.onError, currentText]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    sendMessage,
    isStreaming,
    currentText,
    error,
    clearError,
  };
}
