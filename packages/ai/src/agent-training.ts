import { z } from "zod";

// Schemas para validação
const TrainingDocumentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(["pdf", "doc", "docx", "txt", "md"]),
  content: z.string(),
  metadata: z.object({
    size: z.number(),
    uploadedAt: z.string(),
    processedAt: z.string().optional(),
  }),
});

const TrainingDataSchema = z.object({
  id: z.string(),
  agentId: z.string(),
  documents: z.array(TrainingDocumentSchema),
  knowledgeBase: z.array(z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    category: z.string(),
    tags: z.array(z.string()),
    createdAt: z.string(),
  })),
  qaPairs: z.array(z.object({
    id: z.string(),
    question: z.string(),
    answer: z.string(),
    category: z.string(),
    confidence: z.number().min(0).max(1),
    createdAt: z.string(),
  })),
  conversations: z.array(z.object({
    id: z.string(),
    messages: z.array(z.object({
      role: z.enum(["user", "assistant"]),
      content: z.string(),
      timestamp: z.string(),
    })),
    satisfaction: z.number().min(1).max(5).optional(),
    createdAt: z.string(),
  })),
});

const TrainingResultSchema = z.object({
  success: z.boolean(),
  processedDocuments: z.number(),
  extractedKnowledge: z.number(),
  qaPairsGenerated: z.number(),
  confidence: z.number().min(0).max(1),
  errors: z.array(z.string()),
  recommendations: z.array(z.string()),
});

export interface TrainingDocument {
  id: string;
  name: string;
  type: "pdf" | "doc" | "docx" | "txt" | "md";
  content: string;
  metadata: {
    size: number;
    uploadedAt: string;
    processedAt?: string;
  };
}

export interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  createdAt: string;
}

export interface QAPair {
  id: string;
  question: string;
  answer: string;
  category: string;
  confidence: number;
  createdAt: string;
}

export interface ConversationExample {
  id: string;
  messages: Array<{
    role: "user" | "assistant";
    content: string;
    timestamp: string;
  }>;
  satisfaction?: number;
  createdAt: string;
}

export interface TrainingData {
  id: string;
  agentId: string;
  documents: TrainingDocument[];
  knowledgeBase: KnowledgeItem[];
  qaPairs: QAPair[];
  conversations: ConversationExample[];
}

export interface TrainingResult {
  success: boolean;
  processedDocuments: number;
  extractedKnowledge: number;
  qaPairsGenerated: number;
  confidence: number;
  errors: string[];
  recommendations: string[];
}

export class AgentTrainingService {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = "https://api.openai.com/v1") {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  /**
   * Processar documentos para treinamento
   */
  async processDocuments(documents: TrainingDocument[]): Promise<{
    knowledgeItems: KnowledgeItem[];
    qaPairs: QAPair[];
    errors: string[];
  }> {
    const knowledgeItems: KnowledgeItem[] = [];
    const qaPairs: QAPair[] = [];
    const errors: string[] = [];

    for (const document of documents) {
      try {
        // Extrair texto do documento
        const extractedText = await this.extractTextFromDocument(document);
        
        // Dividir em chunks para processamento
        const chunks = this.splitTextIntoChunks(extractedText, 1000);
        
        for (const chunk of chunks) {
          // Extrair conhecimento do chunk
          const knowledge = await this.extractKnowledgeFromText(chunk, document.name);
          knowledgeItems.push(...knowledge);
          
          // Gerar pares Q&A do chunk
          const qa = await this.generateQAPairsFromText(chunk, document.name);
          qaPairs.push(...qa);
        }
        
        // Marcar documento como processado
        document.metadata.processedAt = new Date().toISOString();
        
      } catch (error) {
        errors.push(`Erro ao processar ${document.name}: ${error}`);
      }
    }

    return { knowledgeItems, qaPairs, errors };
  }

  /**
   * Extrair texto de diferentes tipos de documentos
   */
  private async extractTextFromDocument(document: TrainingDocument): Promise<string> {
    switch (document.type) {
      case "txt":
      case "md":
        return document.content;
      
      case "pdf":
        // Em produção, usar uma biblioteca como pdf-parse
        return document.content;
      
      case "doc":
      case "docx":
        // Em produção, usar uma biblioteca como mammoth
        return document.content;
      
      default:
        throw new Error(`Tipo de documento não suportado: ${document.type}`);
    }
  }

  /**
   * Dividir texto em chunks para processamento
   */
  private splitTextIntoChunks(text: string, maxChunkSize: number): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/[.!?]+/);
    let currentChunk = "";

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        currentChunk += (currentChunk ? ". " : "") + sentence;
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  /**
   * Extrair conhecimento de um texto usando IA
   */
  private async extractKnowledgeFromText(text: string, source: string): Promise<KnowledgeItem[]> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: `Você é um especialista em extrair conhecimento de textos. 
              Analise o texto fornecido e extraia os pontos-chave de conhecimento.
              Retorne um JSON com um array de objetos contendo:
              - title: título do conhecimento
              - content: descrição detalhada
              - category: categoria do conhecimento
              - tags: array de tags relevantes
              
              Foque em informações úteis para um assistente de IA responder perguntas.`
            },
            {
              role: "user",
              content: `Extraia conhecimento do seguinte texto:\n\n${text}`
            }
          ],
          temperature: 0.3,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API Error: ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error("Resposta vazia da API");
      }

      // Tentar fazer parse do JSON retornado
      try {
        const knowledge = JSON.parse(content);
        return knowledge.map((item: any, index: number) => ({
          id: `knowledge_${Date.now()}_${index}`,
          title: item.title || "Conhecimento extraído",
          content: item.content || "",
          category: item.category || "geral",
          tags: item.tags || [],
          createdAt: new Date().toISOString(),
        }));
      } catch (parseError) {
        // Se não conseguir fazer parse, criar um item genérico
        return [{
          id: `knowledge_${Date.now()}`,
          title: `Conhecimento de ${source}`,
          content: text.substring(0, 500) + "...",
          category: "geral",
          tags: ["extraído", "documento"],
          createdAt: new Date().toISOString(),
        }];
      }
    } catch (error) {
      console.error("Erro ao extrair conhecimento:", error);
      return [];
    }
  }

  /**
   * Gerar pares Q&A de um texto usando IA
   */
  private async generateQAPairsFromText(text: string, source: string): Promise<QAPair[]> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: `Você é um especialista em criar pares de pergunta-resposta para treinar assistentes de IA.
              Analise o texto fornecido e gere perguntas que um cliente poderia fazer e suas respectivas respostas.
              Retorne um JSON com um array de objetos contendo:
              - question: pergunta que um cliente faria
              - answer: resposta baseada no texto
              - category: categoria da pergunta
              - confidence: nível de confiança (0-1)
              
              Gere entre 3-5 pares Q&A por texto.`
            },
            {
              role: "user",
              content: `Gere pares Q&A do seguinte texto:\n\n${text}`
            }
          ],
          temperature: 0.4,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API Error: ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error("Resposta vazia da API");
      }

      try {
        const qaPairs = JSON.parse(content);
        return qaPairs.map((pair: any, index: number) => ({
          id: `qa_${Date.now()}_${index}`,
          question: pair.question || "Pergunta gerada",
          answer: pair.answer || "Resposta baseada no texto",
          category: pair.category || "geral",
          confidence: pair.confidence || 0.8,
          createdAt: new Date().toISOString(),
        }));
      } catch (parseError) {
        return [];
      }
    } catch (error) {
      console.error("Erro ao gerar Q&A:", error);
      return [];
    }
  }

  /**
   * Treinar agente com dados fornecidos
   */
  async trainAgent(trainingData: TrainingData): Promise<TrainingResult> {
    const result: TrainingResult = {
      success: false,
      processedDocuments: 0,
      extractedKnowledge: 0,
      qaPairsGenerated: 0,
      confidence: 0,
      errors: [],
      recommendations: [],
    };

    try {
      // Processar documentos
      if (trainingData.documents.length > 0) {
        const documentResult = await this.processDocuments(trainingData.documents);
        result.processedDocuments = trainingData.documents.length;
        result.extractedKnowledge = documentResult.knowledgeItems.length;
        result.qaPairsGenerated = documentResult.qaPairs.length;
        result.errors.push(...documentResult.errors);
      }

      // Analisar conversas existentes
      if (trainingData.conversations.length > 0) {
        const conversationInsights = await this.analyzeConversations(trainingData.conversations);
        result.recommendations.push(...conversationInsights);
      }

      // Calcular confiança geral
      result.confidence = this.calculateConfidence(trainingData);
      
      // Gerar recomendações
      result.recommendations.push(...this.generateRecommendations(trainingData));

      result.success = result.errors.length === 0;
      
    } catch (error) {
      result.errors.push(`Erro geral no treinamento: ${error}`);
    }

    return result;
  }

  /**
   * Analisar conversas para insights
   */
  private async analyzeConversations(conversations: ConversationExample[]): Promise<string[]> {
    const insights: string[] = [];
    
    // Analisar padrões de perguntas
    const allQuestions = conversations.flatMap(conv => 
      conv.messages.filter(msg => msg.role === "user").map(msg => msg.content)
    );
    
    if (allQuestions.length > 0) {
      insights.push(`Identificadas ${allQuestions.length} perguntas dos usuários`);
    }

    // Analisar satisfação
    const satisfactionScores = conversations
      .filter(conv => conv.satisfaction)
      .map(conv => conv.satisfaction!);
    
    if (satisfactionScores.length > 0) {
      const avgSatisfaction = satisfactionScores.reduce((sum, score) => sum + score, 0) / satisfactionScores.length;
      insights.push(`Satisfação média: ${avgSatisfaction.toFixed(1)}/5`);
    }

    return insights;
  }

  /**
   * Calcular confiança do treinamento
   */
  private calculateConfidence(trainingData: TrainingData): number {
    let confidence = 0;
    let factors = 0;

    // Fator: documentos processados
    if (trainingData.documents.length > 0) {
      confidence += Math.min(trainingData.documents.length / 5, 1) * 0.3;
      factors++;
    }

    // Fator: base de conhecimento
    if (trainingData.knowledgeBase.length > 0) {
      confidence += Math.min(trainingData.knowledgeBase.length / 20, 1) * 0.3;
      factors++;
    }

    // Fator: pares Q&A
    if (trainingData.qaPairs.length > 0) {
      confidence += Math.min(trainingData.qaPairs.length / 10, 1) * 0.2;
      factors++;
    }

    // Fator: conversas de exemplo
    if (trainingData.conversations.length > 0) {
      confidence += Math.min(trainingData.conversations.length / 5, 1) * 0.2;
      factors++;
    }

    return factors > 0 ? confidence / factors : 0;
  }

  /**
   * Gerar recomendações de melhoria
   */
  private generateRecommendations(trainingData: TrainingData): string[] {
    const recommendations: string[] = [];

    if (trainingData.documents.length === 0) {
      recommendations.push("Adicione documentos para melhorar o conhecimento do agente");
    }

    if (trainingData.qaPairs.length < 5) {
      recommendations.push("Crie mais pares de pergunta-resposta para treinar o agente");
    }

    if (trainingData.conversations.length === 0) {
      recommendations.push("Adicione exemplos de conversas para melhorar o comportamento");
    }

    if (trainingData.knowledgeBase.length < 10) {
      recommendations.push("Expanda a base de conhecimento com mais informações");
    }

    return recommendations;
  }

  /**
   * Validar dados de treinamento
   */
  validateTrainingData(data: any): { valid: boolean; errors: string[] } {
    try {
      TrainingDataSchema.parse(data);
      return { valid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        };
      }
      return {
        valid: false,
        errors: [`Erro de validação: ${error}`],
      };
    }
  }
}

// Factory function
export function createAgentTrainingService(apiKey: string, baseUrl?: string): AgentTrainingService {
  return new AgentTrainingService(apiKey, baseUrl);
}
