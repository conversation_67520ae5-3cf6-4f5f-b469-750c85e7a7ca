import { generateText, streamText } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

// Schema para validação de respostas do agente
const AgentResponseSchema = z.object({
  message: z.string(),
  intent: z.enum(["sales", "support", "onboarding", "community"]),
  confidence: z.number().min(0).max(1),
  nextAction: z.enum(["continue", "escalate", "complete", "schedule"]).optional(),
  metadata: z.record(z.any()).optional(),
});

export type AgentResponse = z.infer<typeof AgentResponseSchema>;

export interface AgentConfig {
  id: string;
  name: string;
  type: "SALES" | "SUPPORT" | "ONBOARDING" | "COMMUNITY";
  personality: "FRIENDLY" | "PROFESSIONAL" | "ENTHUSIASTIC" | "TECHNICAL" | "CASUAL";
  systemPrompt: string;
  knowledgeBase: string[];
  welcomeMessage: string;
  fallbackMessage: string;
  maxInteractions: number;
  allowEscalation: boolean;
}

export interface ConversationContext {
  agentId: string;
  customerId?: string;
  customerName?: string;
  customerEmail?: string;
  channel: "whatsapp" | "telegram" | "chat" | "email";
  conversationHistory: Array<{
    role: "user" | "assistant" | "system";
    content: string;
    timestamp: string;
  }>;
  metadata?: Record<string, any>;
}

export class AgentService {
  private model = openai("gpt-4o-mini");

  /**
   * Processar mensagem do usuário
   */
  async processMessage(
    message: string,
    agentConfig: AgentConfig,
    context: ConversationContext
  ): Promise<AgentResponse> {
    try {
      const systemPrompt = this.buildSystemPrompt(agentConfig, context);
      
      const result = await generateText({
        model: this.model,
        system: systemPrompt,
        prompt: message,
        tools: {
          escalate: {
            description: "Escalar conversa para atendimento humano",
            parameters: z.object({
              reason: z.string(),
              priority: z.enum(["low", "medium", "high", "urgent"]),
            }),
          },
          schedule: {
            description: "Agendar call ou reunião",
            parameters: z.object({
              type: z.enum(["call", "meeting", "demo"]),
              preferredTime: z.string(),
            }),
          },
          collectInfo: {
            description: "Coletar informações do cliente",
            parameters: z.object({
              field: z.string(),
              value: z.string(),
            }),
          },
        },
        toolChoice: "auto",
      });

      // Parse da resposta
      const response = AgentResponseSchema.parse({
        message: result.text,
        intent: this.detectIntent(message, agentConfig.type),
        confidence: 0.8, // Em produção, calcular baseado na resposta
        nextAction: this.determineNextAction(result),
        metadata: {
          tokensUsed: result.usage?.totalTokens,
          model: "gpt-4o-mini",
          timestamp: new Date().toISOString(),
        },
      });

      return response;
    } catch (error) {
      console.error("Erro ao processar mensagem:", error);
      return {
        message: agentConfig.fallbackMessage,
        intent: "support",
        confidence: 0.1,
        nextAction: "escalate",
        metadata: { error: error.message },
      };
    }
  }

  /**
   * Stream de resposta para tempo real
   */
  async streamResponse(
    message: string,
    agentConfig: AgentConfig,
    context: ConversationContext
  ) {
    const systemPrompt = this.buildSystemPrompt(agentConfig, context);
    
    return streamText({
      model: this.model,
      system: systemPrompt,
      prompt: message,
      tools: {
        escalate: {
          description: "Escalar conversa para atendimento humano",
          parameters: z.object({
            reason: z.string(),
            priority: z.enum(["low", "medium", "high", "urgent"]),
          }),
        },
        schedule: {
          description: "Agendar call ou reunião",
          parameters: z.object({
            type: z.enum(["call", "meeting", "demo"]),
            preferredTime: z.string(),
          }),
        },
      },
    });
  }

  /**
   * Construir prompt do sistema
   */
  private buildSystemPrompt(agentConfig: AgentConfig, context: ConversationContext): string {
    const personalityPrompts = {
      FRIENDLY: "Seja caloroso, acolhedor e use linguagem casual e amigável.",
      PROFESSIONAL: "Mantenha tom formal, técnico e use linguagem corporativa.",
      ENTHUSIASTIC: "Seja animado, motivador e use linguagem energética.",
      TECHNICAL: "Seja preciso, detalhado e use terminologia técnica quando apropriado.",
      CASUAL: "Seja descontraído, informal e use linguagem coloquial.",
    };

    const typePrompts = {
      SALES: `
        Você é um agente de vendas especializado. Seu objetivo é:
        - Qualificar leads e entender necessidades
        - Apresentar benefícios e soluções
        - Tratar objeções comuns
        - Agendar calls quando apropriado
        - Focar na conversão
      `,
      SUPPORT: `
        Você é um agente de suporte técnico. Seu objetivo é:
        - Resolver problemas rapidamente
        - Fornecer soluções práticas
        - Escalar quando necessário
        - Manter cliente satisfeito
        - Documentar problemas
      `,
      ONBOARDING: `
        Você é um agente de onboarding. Seu objetivo é:
        - Guiar novos usuários
        - Explicar funcionalidades
        - Resolver dúvidas iniciais
        - Motivar engajamento
        - Facilitar adoção
      `,
      COMMUNITY: `
        Você é um moderador de comunidade. Seu objetivo é:
        - Manter ambiente positivo
        - Responder perguntas
        - Moderar conteúdo
        - Incentivar participação
        - Resolver conflitos
      `,
    };

    return `
      ${typePrompts[agentConfig.type]}
      
      ${personalityPrompts[agentConfig.personality]}
      
      ${agentConfig.systemPrompt}
      
      Base de conhecimento:
      ${agentConfig.knowledgeBase.join("\n")}
      
      Contexto da conversa:
      - Canal: ${context.channel}
      - Cliente: ${context.customerName || "Anônimo"}
      - Histórico: ${context.conversationHistory.length} mensagens
      
      Instruções:
      1. Responda de forma natural e contextual
      2. Use as ferramentas quando apropriado
      3. Mantenha o foco no objetivo do agente
      4. Seja empático e útil
      5. Se não souber algo, seja honesto e ofereça alternativas
    `;
  }

  /**
   * Detectar intenção da mensagem
   */
  private detectIntent(message: string, agentType: string): "sales" | "support" | "onboarding" | "community" {
    const lowerMessage = message.toLowerCase();
    
    // Palavras-chave para vendas
    if (lowerMessage.includes("preço") || lowerMessage.includes("custo") || lowerMessage.includes("valor")) {
      return "sales";
    }
    
    // Palavras-chave para suporte
    if (lowerMessage.includes("problema") || lowerMessage.includes("erro") || lowerMessage.includes("não funciona")) {
      return "support";
    }
    
    // Palavras-chave para onboarding
    if (lowerMessage.includes("como usar") || lowerMessage.includes("primeiros passos") || lowerMessage.includes("tutorial")) {
      return "onboarding";
    }
    
    // Palavras-chave para comunidade
    if (lowerMessage.includes("grupo") || lowerMessage.includes("comunidade") || lowerMessage.includes("discussão")) {
      return "community";
    }
    
    // Default baseado no tipo do agente
    return agentType.toLowerCase() as any;
  }

  /**
   * Determinar próxima ação
   */
  private determineNextAction(result: any): "continue" | "escalate" | "complete" | "schedule" {
    if (result.toolCalls?.length > 0) {
      const toolCall = result.toolCalls[0];
      if (toolCall.toolName === "escalate") return "escalate";
      if (toolCall.toolName === "schedule") return "schedule";
    }
    
    return "continue";
  }

  /**
   * Treinar agente com dados específicos
   */
  async trainAgent(agentConfig: AgentConfig, trainingData: {
    conversations: Array<{
      messages: Array<{ role: string; content: string }>;
      outcome: string;
    }>;
    documents: string[];
  }): Promise<{ success: boolean; confidence: number }> {
    try {
      // Em produção, implementar treinamento real
      // Por enquanto, simular treinamento
      const confidence = Math.min(0.9, 0.5 + (trainingData.conversations.length * 0.1));
      
      return {
        success: true,
        confidence,
      };
    } catch (error) {
      console.error("Erro no treinamento:", error);
      return {
        success: false,
        confidence: 0,
      };
    }
  }

  /**
   * Analisar performance do agente
   */
  async analyzePerformance(agentId: string, period: "7d" | "30d" | "90d"): Promise<{
    totalInteractions: number;
    avgResponseTime: number;
    satisfactionScore: number;
    conversionRate: number;
    escalationRate: number;
  }> {
    // Em produção, buscar dados reais do banco
    return {
      totalInteractions: Math.floor(Math.random() * 1000) + 100,
      avgResponseTime: Math.random() * 20 + 5,
      satisfactionScore: Math.random() * 2 + 3,
      conversionRate: Math.random() * 0.5 + 0.1,
      escalationRate: Math.random() * 0.2 + 0.05,
    };
  }
}

// Factory function
export function createAgentService(): AgentService {
  return new AgentService();
}

// Instância singleton
export const agentService = createAgentService();
