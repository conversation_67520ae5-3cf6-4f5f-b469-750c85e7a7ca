{"dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "ai": "^4.3.16", "openai": "^5.1.1", "zod": "^3.25.55"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/react": "19.1.6", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@repo/ai", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}