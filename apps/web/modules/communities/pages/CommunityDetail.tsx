"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { Users, Calendar, Tag, ExternalLink, Lock, Star, MessageSquare, Heart } from "lucide-react";
import Image from "next/image";

interface CommunityDetailProps {
  community: {
    id: string;
    name: string;
    description?: string;
    shortDescription?: string;
    thumbnail?: string;
    banner?: string;
    memberCount: number;
    maxMembers?: number;
    priceCents?: number;
    currency: string;
    billingType: string;
    accessType: string;
    isPublic: boolean;
    isActive: boolean;
    tags: string[];
    features: string[];
    platformType?: string;
    platformUrl?: string;
    organization: {
      id: string;
      name: string;
      logo?: string;
    };
    members: Array<{
      id: string;
      user: {
        id: string;
        name: string;
        email: string;
        image?: string;
      };
      role: string;
      joinedAt: string;
    }>;
    categories: Array<{
      id: string;
      name: string;
      description?: string;
      color?: string;
    }>;
    createdAt: string;
    updatedAt: string;
  };
  isMember?: boolean;
  onJoin?: (communityId: string) => void;
  onLeave?: (communityId: string) => void;
  className?: string;
}

export function CommunityDetail({
  community,
  isMember = false,
  onJoin,
  onLeave,
  className,
}: CommunityDetailProps) {
  const formatPrice = (priceCents?: number, currency: string = "BRL", billingType: string = "ONE_TIME") => {
    if (!priceCents) return "Gratuito";
    
    const price = priceCents / 100;
    const formattedPrice = new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: currency,
    }).format(price);
    
    if (billingType === "MONTHLY") {
      return `${formattedPrice}/mês`;
    } else if (billingType === "YEARLY") {
      return `${formattedPrice}/ano`;
    }
    
    return formattedPrice;
  };

  const formatMemberCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  const isPaid = community.accessType === "PAID" && community.priceCents && community.priceCents > 0;
  const isFree = community.accessType === "FREE" || !community.priceCents;
  const isFull = community.maxMembers && community.memberCount >= community.maxMembers;

  return (
    <div className={cn("space-y-8", className)}>
      {/* Banner */}
      {community.banner && (
        <div className="relative h-64 w-full overflow-hidden rounded-lg">
          <Image
            src={community.banner}
            alt={community.name}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        </div>
      )}

      {/* Header */}
      <div className="flex flex-col gap-6 md:flex-row">
        {/* Main Info */}
        <div className="flex-1 space-y-6">
          {/* Community Info */}
          <div className="flex items-start gap-4">
            {community.thumbnail && (
              <div className="relative h-20 w-20 flex-shrink-0 overflow-hidden rounded-lg">
                <Image
                  src={community.thumbnail}
                  alt={community.name}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            
            <div className="flex-1">
              <div className="mb-2 flex items-center gap-2">
                <h1 className="text-3xl font-bold text-gray-900">{community.name}</h1>
                <Badge status={isFree ? "info" : isPaid ? "success" : "error"}>
                  {isFree ? "Gratuito" : isPaid ? "Pago" : "Privado"}
                </Badge>
                {!community.isActive && (
                  <Badge status="error">Inativo</Badge>
                )}
              </div>
              
              <div className="mb-4 flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{formatMemberCount(community.memberCount)} membros</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Criado em {new Date(community.createdAt).toLocaleDateString("pt-BR")}</span>
                </div>
              </div>
              
              <p className="text-gray-700">{community.description}</p>
            </div>
          </div>

          {/* Tags */}
          {community.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {community.tags.map((tag, index) => (
                <Badge key={index} status="info" className="flex items-center gap-1">
                  <Tag className="h-3 w-3" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Features */}
          {community.features.length > 0 && (
            <div>
              <h3 className="mb-3 text-lg font-semibold">Recursos</h3>
              <div className="grid gap-2 md:grid-cols-2">
                {community.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="w-full md:w-80">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Acesso à Comunidade
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Price */}
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">
                  {formatPrice(community.priceCents, community.currency, community.billingType)}
                </div>
                {community.maxMembers && (
                  <div className="text-sm text-gray-600">
                    Máx. {formatMemberCount(community.maxMembers)} membros
                  </div>
                )}
              </div>

              <Separator />

              {/* Action Button */}
              <div className="space-y-2">
                {isMember ? (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => onLeave?.(community.id)}
                  >
                    Sair da Comunidade
                  </Button>
                ) : (
                  <Button
                    className="w-full"
                    onClick={() => onJoin?.(community.id)}
                    disabled={isFull || community.accessType === "INVITE_ONLY"}
                  >
                    {isFull ? (
                      <>
                        <Lock className="mr-2 h-4 w-4" />
                        Comunidade Lotada
                      </>
                    ) : community.accessType === "INVITE_ONLY" ? (
                      <>
                        <Lock className="mr-2 h-4 w-4" />
                        Apenas por Convite
                      </>
                    ) : isFree ? (
                      "Entrar Gratuitamente"
                    ) : (
                      "Comprar Acesso"
                    )}
                  </Button>
                )}

                {community.platformUrl && (
                  <Button variant="outline" className="w-full" asChild>
                    <a href={community.platformUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Acessar Plataforma
                    </a>
                  </Button>
                )}
              </div>

              {/* Community Stats */}
              <div className="grid grid-cols-2 gap-4 pt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatMemberCount(community.memberCount)}
                  </div>
                  <div className="text-xs text-gray-600">Membros</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {community.categories.length}
                  </div>
                  <div className="text-xs text-gray-600">Categorias</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Organization Info */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-lg">Organização</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                {community.organization.logo && (
                  <Image
                    src={community.organization.logo}
                    alt={community.organization.name}
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                )}
                <div>
                  <div className="font-medium">{community.organization.name}</div>
                  <div className="text-sm text-gray-600">Criador da comunidade</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Categories */}
      {community.categories.length > 0 && (
        <div>
          <h3 className="mb-4 text-xl font-semibold">Categorias</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {community.categories.map((category) => (
              <Card key={category.id}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    {category.color && (
                      <div
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                    )}
                    <h4 className="font-medium">{category.name}</h4>
                  </div>
                  {category.description && (
                    <p className="mt-2 text-sm text-gray-600">{category.description}</p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Recent Members */}
      {community.members.length > 0 && (
        <div>
          <h3 className="mb-4 text-xl font-semibold">Membros Recentes</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {community.members.slice(0, 6).map((member) => (
              <Card key={member.id}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    {member.user.image ? (
                      <Image
                        src={member.user.image}
                        alt={member.user.name || "Membro"}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                        <Users className="h-5 w-5 text-gray-500" />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="font-medium">{member.user.name || "Membro"}</div>
                      <div className="text-sm text-gray-600">
                        Entrou em {new Date(member.joinedAt).toLocaleDateString("pt-BR")}
                      </div>
                    </div>
                    <Badge status="info" className="text-xs">
                      {member.role}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
