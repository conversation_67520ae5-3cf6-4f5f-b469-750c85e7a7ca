"use client";

import { CommunityGrid } from "../components/CommunityGrid";
import { CommunityFilters } from "../components/CommunityFilters";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { cn } from "@ui/lib";
import { Search, Users, Star, TrendingUp } from "lucide-react";
import { useState, useEffect } from "react";

interface Community {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  thumbnail?: string;
  memberCount: number;
  maxMembers?: number;
  priceCents?: number;
  currency: string;
  billingType: string;
  accessType: string;
  isPublic: boolean;
  tags: string[];
  createdAt?: string;
  organization: {
    id: string;
    name: string;
    logo?: string;
  };
}

interface CommunityMarketplaceProps {
  communities: Community[];
  loading?: boolean;
  onJoin?: (communityId: string) => void;
  className?: string;
}

export function CommunityMarketplace({
  communities,
  loading = false,
  onJoin,
  className,
}: CommunityMarketplaceProps) {
  const [search, setSearch] = useState("");
  const [accessType, setAccessType] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [tags, setTags] = useState<string[]>([]);
  const [filteredCommunities, setFilteredCommunities] = useState<Community[]>(communities);

  // Extract all available tags
  const availableTags = Array.from(
    new Set(communities.flatMap((c) => c.tags))
  ).sort();

  // Filter and sort communities
  useEffect(() => {
    let filtered = [...communities];

    // Search filter
    if (search) {
      filtered = filtered.filter(
        (community) =>
          community.name.toLowerCase().includes(search.toLowerCase()) ||
          community.description?.toLowerCase().includes(search.toLowerCase()) ||
          community.shortDescription?.toLowerCase().includes(search.toLowerCase()) ||
          community.tags.some((tag) =>
            tag.toLowerCase().includes(search.toLowerCase())
          )
      );
    }

    // Access type filter
    if (accessType !== "all") {
      filtered = filtered.filter((community) => community.accessType === accessType);
    }

    // Tags filter
    if (tags.length > 0) {
      filtered = filtered.filter((community) =>
        tags.some((tag) => community.tags.includes(tag))
      );
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.createdAt || new Date()).getTime() - new Date(a.createdAt || new Date()).getTime();
        case "oldest":
          return new Date(a.createdAt || new Date()).getTime() - new Date(b.createdAt || new Date()).getTime();
        case "members":
          return b.memberCount - a.memberCount;
        case "name":
          return a.name.localeCompare(b.name);
        case "price_low":
          return (a.priceCents || 0) - (b.priceCents || 0);
        case "price_high":
          return (b.priceCents || 0) - (a.priceCents || 0);
        default:
          return 0;
      }
    });

    setFilteredCommunities(filtered);
  }, [communities, search, accessType, sortBy, tags]);

  const stats = {
    total: communities.length,
    free: communities.filter((c) => c.accessType === "FREE").length,
    paid: communities.filter((c) => c.accessType === "PAID").length,
    totalMembers: communities.reduce((sum, c) => sum + c.memberCount, 0),
  };

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header */}
      <div className="text-center">
        <h1 className="mb-4 text-4xl font-bold text-gray-900">
          Marketplace de Comunidades
        </h1>
        <p className="text-lg text-gray-600">
          Descubra e participe de comunidades incríveis
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-sm text-gray-600">Comunidades</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{stats.free}</p>
                <p className="text-sm text-gray-600">Gratuitas</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{stats.paid}</p>
                <p className="text-sm text-gray-600">Pagas</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">
                  {stats.totalMembers >= 1000 
                    ? `${(stats.totalMembers / 1000).toFixed(1)}k`
                    : stats.totalMembers
                  }
                </p>
                <p className="text-sm text-gray-600">Membros</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <CommunityFilters
        search={search}
        onSearchChange={setSearch}
        accessType={accessType}
        onAccessTypeChange={setAccessType}
        sortBy={sortBy}
        onSortByChange={setSortBy}
        tags={tags}
        onTagsChange={setTags}
        availableTags={availableTags}
      />

      {/* Results */}
      <div>
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            {filteredCommunities.length} comunidade{filteredCommunities.length !== 1 ? "s" : ""} encontrada{filteredCommunities.length !== 1 ? "s" : ""}
          </h2>
          
          {filteredCommunities.length > 0 && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Mostrando</span>
              <span className="font-medium">{filteredCommunities.length}</span>
              <span>de</span>
              <span className="font-medium">{communities.length}</span>
            </div>
          )}
        </div>

        <CommunityGrid
          communities={filteredCommunities}
          loading={loading}
          showJoinButton={true}
          onJoin={onJoin}
          columns={3}
        />
      </div>

      {/* Empty state */}
      {!loading && filteredCommunities.length === 0 && communities.length > 0 && (
        <div className="text-center py-12">
          <div className="mb-4 rounded-full bg-gray-100 p-4 mx-auto w-fit">
            <Search className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900">
            Nenhuma comunidade encontrada
          </h3>
          <p className="text-gray-500 mb-4">
            Tente ajustar os filtros para encontrar o que você está procurando.
          </p>
          <Button variant="outline" onClick={() => {
            setSearch("");
            setAccessType("all");
            setSortBy("newest");
            setTags([]);
          }}>
            Limpar filtros
          </Button>
        </div>
      )}
    </div>
  );
}
