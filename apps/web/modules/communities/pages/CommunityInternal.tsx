"use client";

import { CommunityGrid } from "../components/CommunityGrid";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { Plus, Users, Settings, ExternalLink } from "lucide-react";
import { useState } from "react";

interface Community {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  thumbnail?: string;
  memberCount: number;
  maxMembers?: number;
  priceCents?: number;
  currency: string;
  billingType: string;
  accessType: string;
  isPublic: boolean;
  isActive: boolean;
  tags: string[];
  organization: {
    id: string;
    name: string;
    logo?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface CommunityInternalProps {
  communities: Community[];
  loading?: boolean;
  onCreateCommunity?: () => void;
  onEditCommunity?: (communityId: string) => void;
  onViewCommunity?: (communityId: string) => void;
  className?: string;
}

export function CommunityInternal({
  communities,
  loading = false,
  onCreateCommunity,
  onEditCommunity,
  onViewCommunity,
  className,
}: CommunityInternalProps) {
  const [filter, setFilter] = useState<"all" | "active" | "inactive" | "public" | "private">("all");

  const filteredCommunities = communities.filter((community) => {
    switch (filter) {
      case "active":
        return community.isActive;
      case "inactive":
        return !community.isActive;
      case "public":
        return community.isPublic;
      case "private":
        return !community.isPublic;
      default:
        return true;
    }
  });

  const stats = {
    total: communities.length,
    active: communities.filter((c) => c.isActive).length,
    public: communities.filter((c) => c.isPublic).length,
    totalMembers: communities.reduce((sum, c) => sum + c.memberCount, 0),
    totalRevenue: communities
      .filter((c) => c.priceCents && c.priceCents > 0)
      .reduce((sum, c) => sum + (c.priceCents || 0) * c.memberCount, 0),
  };

  const formatRevenue = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Minhas Comunidades</h1>
          <p className="text-gray-600">Gerencie suas comunidades e acompanhe o desempenho</p>
        </div>
        
        <Button onClick={onCreateCommunity} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Nova Comunidade
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-sm text-gray-600">Total</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-5 w-5 rounded-full bg-green-500" />
              <div>
                <p className="text-2xl font-bold">{stats.active}</p>
                <p className="text-sm text-gray-600">Ativas</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ExternalLink className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{stats.public}</p>
                <p className="text-sm text-gray-600">Públicas</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">
                  {stats.totalMembers >= 1000 
                    ? `${(stats.totalMembers / 1000).toFixed(1)}k`
                    : stats.totalMembers
                  }
                </p>
                <p className="text-sm text-gray-600">Membros</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-5 w-5 rounded-full bg-green-500" />
              <div>
                <p className="text-2xl font-bold">{formatRevenue(stats.totalRevenue)}</p>
                <p className="text-sm text-gray-600">Receita</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={filter === "all" ? "primary" : "outline"}
          size="sm"
          onClick={() => setFilter("all")}
        >
          Todas ({communities.length})
        </Button>
        <Button
          variant={filter === "active" ? "primary" : "outline"}
          size="sm"
          onClick={() => setFilter("active")}
        >
          Ativas ({stats.active})
        </Button>
        <Button
          variant={filter === "inactive" ? "primary" : "outline"}
          size="sm"
          onClick={() => setFilter("inactive")}
        >
          Inativas ({communities.length - stats.active})
        </Button>
        <Button
          variant={filter === "public" ? "primary" : "outline"}
          size="sm"
          onClick={() => setFilter("public")}
        >
          Públicas ({stats.public})
        </Button>
        <Button
          variant={filter === "private" ? "primary" : "outline"}
          size="sm"
          onClick={() => setFilter("private")}
        >
          Privadas ({communities.length - stats.public})
        </Button>
      </div>

      {/* Communities Grid */}
      <div>
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            {filteredCommunities.length} comunidade{filteredCommunities.length !== 1 ? "s" : ""}
          </h2>
        </div>

        <CommunityGrid
          communities={filteredCommunities.map((community) => ({
            ...community,
            // Add action buttons for internal view
            actions: (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewCommunity?.(community.id)}
                >
                  <ExternalLink className="mr-1 h-3 w-3" />
                  Ver
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditCommunity?.(community.id)}
                >
                  <Settings className="mr-1 h-3 w-3" />
                  Editar
                </Button>
              </div>
            ),
          }))}
          loading={loading}
          showJoinButton={false}
          columns={3}
        />
      </div>

      {/* Empty state */}
      {!loading && communities.length === 0 && (
        <div className="text-center py-12">
          <div className="mb-4 rounded-full bg-gray-100 p-4 mx-auto w-fit">
            <Users className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900">
            Nenhuma comunidade criada
          </h3>
          <p className="text-gray-500 mb-4">
            Comece criando sua primeira comunidade para conectar pessoas.
          </p>
          <Button onClick={onCreateCommunity}>
            <Plus className="mr-2 h-4 w-4" />
            Criar Primeira Comunidade
          </Button>
        </div>
      )}

      {/* Filter empty state */}
      {!loading && communities.length > 0 && filteredCommunities.length === 0 && (
        <div className="text-center py-12">
          <div className="mb-4 rounded-full bg-gray-100 p-4 mx-auto w-fit">
            <Users className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900">
            Nenhuma comunidade encontrada
          </h3>
          <p className="text-gray-500 mb-4">
            Não há comunidades que correspondam ao filtro selecionado.
          </p>
          <Button variant="outline" onClick={() => setFilter("all")}>
            Ver todas as comunidades
          </Button>
        </div>
      )}
    </div>
  );
}
