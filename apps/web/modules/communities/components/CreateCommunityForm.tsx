"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { Plus, X, Upload, Users, DollarSign, Globe, Lock } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const createCommunitySchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").max(100, "Nome muito longo"),
  slug: z.string().min(1, "Slug é obrigatório").max(50, "Slug muito longo"),
  description: z.string().optional(),
  shortDescription: z.string().max(200, "Descrição curta muito longa").optional(),
  thumbnail: z.string().url().optional(),
  banner: z.string().url().optional(),
  isPublic: z.boolean().default(false),
  isActive: z.boolean().default(true),
  maxMembers: z.number().int().positive().optional(),
  priceCents: z.number().int().min(0).optional(),
  currency: z.string().default("BRL"),
  billingType: z.enum(["ONE_TIME", "MONTHLY", "YEARLY"]).default("ONE_TIME"),
  accessType: z.enum(["FREE", "PAID", "INVITE_ONLY"]).default("PAID"),
  platformType: z.enum(["DISCORD", "TELEGRAM", "WHATSAPP", "CUSTOM"]).optional(),
  platformUrl: z.string().url().optional(),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
});

type CreateCommunityFormData = z.infer<typeof createCommunitySchema>;

interface CreateCommunityFormProps {
  organizationId: string;
  onSubmit: (data: CreateCommunityFormData) => Promise<void>;
  loading?: boolean;
  className?: string;
}

export function CreateCommunityForm({
  organizationId,
  onSubmit,
  loading = false,
  className,
}: CreateCommunityFormProps) {
  const [newTag, setNewTag] = useState("");
  const [newFeature, setNewFeature] = useState("");

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateCommunityFormData>({
    resolver: zodResolver(createCommunitySchema),
    defaultValues: {
      isPublic: false,
      isActive: true,
      currency: "BRL",
      billingType: "ONE_TIME",
      accessType: "PAID",
      tags: [],
      features: [],
    },
  });

  const watchedValues = watch();
  const tags = watchedValues.tags || [];
  const features = watchedValues.features || [];

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setValue("tags", [...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setValue("tags", tags.filter((tag) => tag !== tagToRemove));
  };

  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setValue("features", [...features, newFeature.trim()]);
      setNewFeature("");
    }
  };

  const removeFeature = (featureToRemove: string) => {
    setValue("features", features.filter((feature) => feature !== featureToRemove));
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setValue("name", name);
    if (!watchedValues.slug) {
      setValue("slug", generateSlug(name));
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={cn("space-y-6", className)}>
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Informações Básicas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nome da Comunidade *</Label>
              <Input
                id="name"
                {...register("name")}
                onChange={handleNameChange}
                placeholder="Ex: AI Automation Society"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                {...register("slug")}
                placeholder="Ex: ai-automation-society"
                className={errors.slug ? "border-red-500" : ""}
              />
              {errors.slug && (
                <p className="text-sm text-red-500">{errors.slug.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="shortDescription">Descrição Curta</Label>
            <Input
              id="shortDescription"
              {...register("shortDescription")}
              placeholder="Uma breve descrição da comunidade"
              maxLength={200}
            />
            <p className="text-xs text-gray-500">
              {watchedValues.shortDescription?.length || 0}/200 caracteres
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição Completa</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Descreva detalhadamente o que os membros podem esperar da comunidade"
              rows={4}
            />
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="thumbnail">Thumbnail URL</Label>
              <Input
                id="thumbnail"
                {...register("thumbnail")}
                placeholder="https://example.com/image.jpg"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="banner">Banner URL</Label>
              <Input
                id="banner"
                {...register("banner")}
                placeholder="https://example.com/banner.jpg"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing & Access */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Preços e Acesso
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="accessType">Tipo de Acesso</Label>
              <Select
                value={watchedValues.accessType}
                onValueChange={(value) => setValue("accessType", value as any)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="FREE">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Gratuito
                    </div>
                  </SelectItem>
                  <SelectItem value="PAID">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Pago
                    </div>
                  </SelectItem>
                  <SelectItem value="INVITE_ONLY">
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      Apenas Convite
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {watchedValues.accessType === "PAID" && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="priceCents">Preço (em centavos)</Label>
                  <Input
                    id="priceCents"
                    type="number"
                    {...register("priceCents", { valueAsNumber: true })}
                    placeholder="9900"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billingType">Tipo de Cobrança</Label>
                  <Select
                    value={watchedValues.billingType}
                    onValueChange={(value) => setValue("billingType", value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ONE_TIME">Pagamento único</SelectItem>
                      <SelectItem value="MONTHLY">Mensal</SelectItem>
                      <SelectItem value="YEARLY">Anual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxMembers">Máximo de Membros</Label>
            <Input
              id="maxMembers"
              type="number"
              {...register("maxMembers", { valueAsNumber: true })}
              placeholder="1000"
            />
          </div>
        </CardContent>
      </Card>

      {/* Platform Integration */}
      <Card>
        <CardHeader>
          <CardTitle>Integração com Plataforma</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="platformType">Tipo de Plataforma</Label>
              <Select
                value={watchedValues.platformType || ""}
                onValueChange={(value) => setValue("platformType", value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecionar plataforma" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DISCORD">Discord</SelectItem>
                  <SelectItem value="TELEGRAM">Telegram</SelectItem>
                  <SelectItem value="WHATSAPP">WhatsApp</SelectItem>
                  <SelectItem value="CUSTOM">Personalizada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="platformUrl">URL da Plataforma</Label>
              <Input
                id="platformUrl"
                {...register("platformUrl")}
                placeholder="https://discord.gg/your-community"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      <Card>
        <CardHeader>
          <CardTitle>Tags</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Adicionar tag"
              onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} variant="outline">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Recursos da Comunidade</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={newFeature}
              onChange={(e) => setNewFeature(e.target.value)}
              placeholder="Adicionar recurso"
              onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addFeature())}
            />
            <Button type="button" onClick={addFeature} variant="outline">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {features.length > 0 && (
            <div className="space-y-2">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <span className="text-sm">{feature}</span>
                  <X
                    className="h-3 w-3 cursor-pointer text-red-500"
                    onClick={() => removeFeature(feature)}
                  />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Configurações</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="isPublic">Comunidade Pública</Label>
              <p className="text-sm text-gray-500">
                Visível no marketplace público
              </p>
            </div>
            <Switch
              id="isPublic"
              checked={watchedValues.isPublic}
              onCheckedChange={(checked) => setValue("isPublic", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="isActive">Comunidade Ativa</Label>
              <p className="text-sm text-gray-500">
                Aceita novos membros
              </p>
            </div>
            <Switch
              id="isActive"
              checked={watchedValues.isActive}
              onCheckedChange={(checked) => setValue("isActive", checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit */}
      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline">
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Criando..." : "Criar Comunidade"}
        </Button>
      </div>
    </form>
  );
}
