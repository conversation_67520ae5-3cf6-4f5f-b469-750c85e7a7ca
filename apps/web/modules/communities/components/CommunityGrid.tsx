"use client";

import { CommunityCard } from "./CommunityCard";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";

interface Community {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  thumbnail?: string;
  memberCount: number;
  maxMembers?: number;
  priceCents?: number;
  currency: string;
  billingType: string;
  accessType: string;
  isPublic: boolean;
  tags: string[];
  organization: {
    id: string;
    name: string;
    logo?: string;
  };
}

interface CommunityGridProps {
  communities: Community[];
  loading?: boolean;
  showJoinButton?: boolean;
  onJoin?: (communityId: string) => void;
  className?: string;
  columns?: 1 | 2 | 3 | 4;
}

function CommunityCardSkeleton() {
  return (
    <div className="overflow-hidden rounded-lg border bg-white">
      <Skeleton className="h-48 w-full" />
      <div className="p-6">
        <div className="mb-3 flex items-center gap-2">
          <Skeleton className="h-5 w-5 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="mb-2 h-6 w-3/4" />
        <Skeleton className="mb-4 h-4 w-full" />
        <Skeleton className="mb-4 h-4 w-2/3" />
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="mb-1 h-6 w-16" />
            <Skeleton className="h-3 w-20" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
    </div>
  );
}

export function CommunityGrid({
  communities,
  loading = false,
  showJoinButton = true,
  onJoin,
  className,
  columns = 3,
}: CommunityGridProps) {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  };

  if (loading) {
    return (
      <div className={cn("grid gap-6", gridCols[columns], className)}>
        {Array.from({ length: 6 }).map((_, index) => (
          <CommunityCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (communities.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="mb-4 rounded-full bg-gray-100 p-4">
          <svg
            className="h-8 w-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
        </div>
        <h3 className="mb-2 text-lg font-semibold text-gray-900">
          Nenhuma comunidade encontrada
        </h3>
        <p className="text-gray-500">
          Não há comunidades disponíveis no momento.
        </p>
      </div>
    );
  }

  return (
    <div className={cn("grid gap-6", gridCols[columns], className)}>
      {communities.map((community) => (
        <CommunityCard
          key={community.id}
          community={community}
          showJoinButton={showJoinButton}
          onJoin={onJoin}
        />
      ))}
    </div>
  );
}
