"use client";

import { CommunityInternal } from "../pages/CommunityInternal";
import { useRouter, useParams } from "next/navigation";

interface CommunityInternalClientProps {
  communities: any[];
  loading?: boolean;
  onCreateCommunity?: () => void;
  onEditCommunity?: (communityId: string) => void;
  onViewCommunity?: (communityId: string) => void;
  className?: string;
}

export function CommunityInternalClient(props: CommunityInternalClientProps) {
  const router = useRouter();
  const params = useParams();
  const organizationSlug = params.organizationSlug as string;

  const handleCreateCommunity = () => {
    router.push(`/app/${organizationSlug}/communities/new`);
  };

  const handleEditCommunity = (communityId: string) => {
    router.push(`/app/${organizationSlug}/communities/${communityId}/edit`);
  };

  const handleViewCommunity = (communityId: string) => {
    router.push(`/app/${organizationSlug}/communities/${communityId}`);
  };

  return (
    <CommunityInternal
      {...props}
      onCreateCommunity={handleCreateCommunity}
      onEditCommunity={handleEditCommunity}
      onViewCommunity={handleViewCommunity}
    />
  );
}
