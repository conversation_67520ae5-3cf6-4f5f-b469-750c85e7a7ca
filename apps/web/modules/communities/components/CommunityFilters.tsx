"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { Search, X, Filter } from "lucide-react";
import { useState } from "react";

interface CommunityFiltersProps {
  search: string;
  onSearchChange: (search: string) => void;
  accessType: string;
  onAccessTypeChange: (type: string) => void;
  sortBy: string;
  onSortByChange: (sort: string) => void;
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  availableTags: string[];
  className?: string;
}

export function CommunityFilters({
  search,
  onSearchChange,
  accessType,
  onAccessTypeChange,
  sortBy,
  onSortByChange,
  tags,
  onTagsChange,
  availableTags,
  className,
}: CommunityFiltersProps) {
  const [showFilters, setShowFilters] = useState(false);

  const handleTagToggle = (tag: string) => {
    if (tags.includes(tag)) {
      onTagsChange(tags.filter((t) => t !== tag));
    } else {
      onTagsChange([...tags, tag]);
    }
  };

  const clearFilters = () => {
    onSearchChange("");
    onAccessTypeChange("all");
    onSortByChange("newest");
    onTagsChange([]);
  };

  const hasActiveFilters = search || accessType !== "all" || tags.length > 0;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search and main filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Buscar comunidades..."
            value={search}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Quick filters */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filtros
            {hasActiveFilters && (
              <Badge status="info" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
                {[search, accessType !== "all" ? 1 : 0, tags.length].reduce((a, b) => Number(a) + Number(b), 0)}
              </Badge>
            )}
          </Button>

          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4" />
              Limpar
            </Button>
          )}
        </div>
      </div>

      {/* Advanced filters */}
      {showFilters && (
        <div className="rounded-lg border bg-gray-50 p-4">
          <div className="grid gap-4 md:grid-cols-3">
            {/* Access Type */}
            <div className="space-y-2">
              <Label htmlFor="access-type">Tipo de Acesso</Label>
              <Select value={accessType} onValueChange={onAccessTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecionar tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="FREE">Gratuito</SelectItem>
                  <SelectItem value="PAID">Pago</SelectItem>
                  <SelectItem value="INVITE_ONLY">Convite</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort By */}
            <div className="space-y-2">
              <Label htmlFor="sort-by">Ordenar por</Label>
              <Select value={sortBy} onValueChange={onSortByChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecionar ordenação" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Mais recentes</SelectItem>
                  <SelectItem value="oldest">Mais antigas</SelectItem>
                  <SelectItem value="members">Mais membros</SelectItem>
                  <SelectItem value="name">Nome A-Z</SelectItem>
                  <SelectItem value="price_low">Menor preço</SelectItem>
                  <SelectItem value="price_high">Maior preço</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex flex-wrap gap-1">
                {availableTags.slice(0, 10).map((tag) => (
                  <Badge
                    key={tag}
                    variant={tags.includes(tag) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
                {availableTags.length > 10 && (
                  <Badge variant="outline" className="cursor-pointer">
                    +{availableTags.length - 10}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active filters display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {search && (
            <Badge status="info" className="flex items-center gap-1">
              Busca: "{search}"
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onSearchChange("")}
              />
            </Badge>
          )}
          
          {accessType !== "all" && (
            <Badge status="info" className="flex items-center gap-1">
              Tipo: {accessType === "FREE" ? "Gratuito" : accessType === "PAID" ? "Pago" : "Convite"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onAccessTypeChange("all")}
              />
            </Badge>
          )}
          
          {tags.map((tag) => (
            <Badge key={tag} status="info" className="flex items-center gap-1">
              {tag}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleTagToggle(tag)}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
