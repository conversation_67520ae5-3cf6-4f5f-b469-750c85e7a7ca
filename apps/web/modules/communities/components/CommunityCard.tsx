"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { cn } from "@ui/lib";
import { Users, ExternalLink, Lock, Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface CommunityCardProps {
  community: {
    id: string;
    name: string;
    description?: string;
    shortDescription?: string;
    thumbnail?: string;
    memberCount: number;
    maxMembers?: number;
    priceCents?: number;
    currency: string;
    billingType: string;
    accessType: string;
    isPublic: boolean;
    tags: string[];
    organization: {
      id: string;
      name: string;
      logo?: string;
    };
  };
  showJoinButton?: boolean;
  onJoin?: (communityId: string) => void;
  className?: string;
}

export function CommunityCard({
  community,
  showJoinButton = true,
  onJoin,
  className,
}: CommunityCardProps) {
  const formatPrice = (priceCents?: number, currency: string = "BRL", billingType: string = "ONE_TIME") => {
    if (!priceCents) return "Gratuito";
    
    const price = priceCents / 100;
    const formattedPrice = new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: currency,
    }).format(price);
    
    if (billingType === "MONTHLY") {
      return `${formattedPrice}/mês`;
    } else if (billingType === "YEARLY") {
      return `${formattedPrice}/ano`;
    }
    
    return formattedPrice;
  };

  const formatMemberCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  const isFull = community.maxMembers && community.memberCount >= community.maxMembers;
  const isPaid = community.accessType === "PAID" && community.priceCents && community.priceCents > 0;
  const isFree = community.accessType === "FREE" || !community.priceCents;

  return (
    <Card className={cn(
      "group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
      className
    )}>
      <CardContent className="p-0">
        {/* Thumbnail */}
        <div className="relative h-48 w-full overflow-hidden">
          {community.thumbnail ? (
            <Image
              src={community.thumbnail}
              alt={community.name}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
              <Users className="h-16 w-16 text-white opacity-50" />
            </div>
          )}
          
          {/* Overlay with badges */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          
          {/* Access type badge */}
          <div className="absolute top-3 left-3">
            <Badge 
              status={isFree ? "info" : isPaid ? "success" : "error"}
              className="backdrop-blur-sm"
            >
              {isFree ? "Gratuito" : isPaid ? "Pago" : "Privado"}
            </Badge>
          </div>
          
          {/* Member count */}
          <div className="absolute top-3 right-3">
            <div className="flex items-center gap-1 rounded-full bg-black/50 px-3 py-1 text-sm text-white backdrop-blur-sm">
              <Users className="h-3 w-3" />
              <span>{formatMemberCount(community.memberCount)}</span>
            </div>
          </div>
          
          {/* Full indicator */}
          {isFull && (
            <div className="absolute top-3 right-3">
              <Badge status="error" className="backdrop-blur-sm">
                <Lock className="mr-1 h-3 w-3" />
                Lotado
              </Badge>
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="p-6">
          {/* Organization */}
          <div className="mb-3 flex items-center gap-2">
            {community.organization.logo && (
              <Image
                src={community.organization.logo}
                alt={community.organization.name}
                width={20}
                height={20}
                className="rounded-full"
              />
            )}
            <span className="text-sm text-muted-foreground">
              {community.organization.name}
            </span>
          </div>
          
          {/* Title */}
          <h3 className="mb-2 text-xl font-bold text-gray-900 line-clamp-1">
            {community.name}
          </h3>
          
          {/* Description */}
          <p className="mb-4 text-sm text-gray-600 line-clamp-2">
            {community.shortDescription || community.description}
          </p>
          
          {/* Tags */}
          {community.tags.length > 0 && (
            <div className="mb-4 flex flex-wrap gap-1">
              {community.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} status="info" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {community.tags.length > 3 && (
                <Badge status="info" className="text-xs">
                  +{community.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          {/* Price and Action */}
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-gray-900">
                {formatPrice(community.priceCents, community.currency, community.billingType)}
              </span>
              {community.maxMembers && (
                <span className="text-xs text-muted-foreground">
                  Máx. {formatMemberCount(community.maxMembers)} membros
                </span>
              )}
            </div>
            
            {showJoinButton && (
              <div className="flex gap-2">
                {community.isPublic ? (
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                  >
                    <Link href={`/communities/${community.id}`}>
                      <ExternalLink className="mr-1 h-3 w-3" />
                      Ver
                    </Link>
                  </Button>
                ) : null}
                
                {!isFull && (
                  <Button
                    size="sm"
                    onClick={() => onJoin?.(community.id)}
                    disabled={community.accessType === "INVITE_ONLY"}
                  >
                    {isFree ? "Entrar" : "Comprar"}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
