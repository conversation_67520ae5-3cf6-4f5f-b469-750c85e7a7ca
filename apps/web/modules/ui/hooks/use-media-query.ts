"use client";

import { useEffect, useState } from "react";

const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    // Check if it's mobile based on common mobile breakpoints
    const mobileQuery = window.matchMedia("(max-width: 768px)");
    setIsMobile(mobileQuery.matches);

    const listener = () => {
      setMatches(media.matches);
      setIsMobile(mobileQuery.matches);
    };

    media.addEventListener("change", listener);
    mobileQuery.addEventListener("change", listener);

    return () => {
      media.removeEventListener("change", listener);
      mobileQuery.removeEventListener("change", listener);
    };
  }, [matches, query]);

  return { matches, isMobile };
};

export { useMediaQuery };
