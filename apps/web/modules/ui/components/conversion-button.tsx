'use client';

import { But<PERSON> } from '@ui/components/button';
import { Lock, Zap, CheckCircle } from 'lucide-react';
import { cn } from '@ui/lib';

interface ConversionButtonProps {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'urgent' | 'success' | 'pix' | 'boleto';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  discount?: number;
  type?: 'button' | 'submit' | 'reset';
}

export function ConversionButton({
  onClick,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'lg',
  children,
  className,
  discount,
  type = 'button',
  ...props
}: ConversionButtonProps) {
  const getIcon = () => {
    if (loading) return null;

    switch (variant) {
      case 'success':
        return <CheckCircle className="h-5 w-5" />;
      case 'pix':
        return <Zap className="h-5 w-5" />;
      case 'boleto':
        return <Lock className="h-5 w-5" />;
      case 'urgent':
        return <Zap className="h-5 w-5" />;
      default:
        return <Lock className="h-5 w-5" />;
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'urgent':
        return 'bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white shadow-lg';
      case 'success':
        return 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg';
      case 'pix':
        return 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg';
      case 'boleto':
        return 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg';
      default:
          return 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-10 px-4 text-sm';
      case 'md':
        return 'h-12 px-6 text-base';
      case 'lg':
        return 'h-14 px-8 text-lg';
      default:
        return 'h-14 px-8 text-lg';
    }
  };

  return (
    <Button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        'relative font-bold transition-all duration-200 hover:scale-105 active:scale-95',
        'focus:outline-none focus:ring-2 focus:ring-blue-500/50',
        getVariantClasses(),
        getSizeClasses(),
        variant === 'urgent' && 'animate-pulse',
        className
      )}
      {...props}
    >


      <div className="flex items-center justify-center gap-2">
        {getIcon()}
        <span>
          {loading ? 'Processando...' : children}
        </span>
      </div>

      {/* Loading spinner */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
        </div>
      )}
    </Button>
  );
}

// Specialized button components for different use cases
export function UrgentPurchaseButton({
  discount,
  onClick,
  disabled = false,
  loading = false,
  type = 'submit'
}: {
  discount?: number;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  type?: 'button' | 'submit' | 'reset';
}) {
  return (
    <ConversionButton
      variant="success"
      size="lg"
      discount={discount}
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      type={type}
      className="w-full"
    >
      Finalizar Compra
    </ConversionButton>
  );
}

export function PixButton({
  onClick,
  disabled = false,
  loading = false,
  type = 'submit'
}: {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  type?: 'button' | 'submit' | 'reset';
}) {
  return (
    <ConversionButton
      variant="pix"
      size="lg"
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      type={type}
      className="w-full"
    >
      Pagar com PIX
    </ConversionButton>
  );
}

export function BoletoButton({
  onClick,
  disabled = false,
  loading = false,
  type = 'submit'
}: {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  type?: 'button' | 'submit' | 'reset';
}) {
  return (
    <ConversionButton
      variant="boleto"
      size="lg"
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      type={type}
      className="w-full"
    >
      Gerar Boleto
    </ConversionButton>
  );
}

export function SuccessButton({
  onClick,
  disabled = false,
  loading = false
}: {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
}) {
  return (
    <ConversionButton
      variant="success"
      size="lg"
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      className="w-full"
    >
      Compra Confirmada!
    </ConversionButton>
  );
}
