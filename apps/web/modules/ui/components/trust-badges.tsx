'use client';

import { Shield, Lock, Award, Users, Clock, CheckCircle, Star, ShieldCheck, CreditCard, RefreshCcw } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';

interface TrustBadgeProps {
  icon: React.ReactNode;
  title: string;
  description?: string;
  variant?: 'default' | 'success' | 'warning' | 'info';
  className?: string;
}

function TrustBadge({ icon, title, description, variant = 'default', className = '' }: TrustBadgeProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'warning':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className={`flex items-center gap-2 p-2 rounded-lg border ${getVariantStyles()} ${className}`}>
      <div className="flex-shrink-0">{icon}</div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">{title}</p>
        {description && <p className="text-xs opacity-75">{description}</p>}
      </div>
    </div>
  );
}

export function SecurityBadges({ className = '' }: { className?: string }) {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 gap-2 ${className}`}>
      <TrustBadge
        icon={<Shield className="h-4 w-4" />}
        title="SSL Seguro"
        description="Criptografia de ponta a ponta"
        variant="success"
      />
      <TrustBadge
        icon={<Lock className="h-4 w-4" />}
        title="PCI DSS"
        description="Padrão bancário"
        variant="success"
      />
      <TrustBadge
        icon={<Award className="h-4 w-4" />}
        title="Garantia 7 dias"
        description="Reembolso total"
        variant="info"
      />
      <TrustBadge
        icon={<Users className="h-4 w-4" />}
        title="+10.000 clientes"
        description="Satisfação garantida"
        variant="info"
      />
    </div>
  );
}

export function TrustIndicators({ className = '' }: { className?: string }) {
  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <span>Pagamento 100% seguro</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Clock className="h-4 w-4 text-blue-600" />
        <span>Acesso imediato após pagamento</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Star className="h-4 w-4 text-yellow-500" />
        <span>Avaliação 4.9/5 pelos clientes</span>
      </div>
    </div>
  );
}

export function SocialProof({
  totalCustomers = 12547,
  averageRating = 4.9,
  totalReviews = 2847,
  className = ''
}: {
  totalCustomers?: number;
  averageRating?: number;
  totalReviews?: number;
  className?: string;
}) {
  return (
    <Card className={`border-green-200 bg-green-50 ${className}`}>
      <CardContent className="pt-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-2">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < Math.floor(averageRating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                }`}
              />
            ))}
            <span className="ml-1 text-sm font-medium text-gray-700">
              {averageRating} ({totalReviews.toLocaleString()} avaliações)
            </span>
          </div>
          <p className="text-sm text-gray-600">
            Junte-se a <span className="font-semibold text-green-800">{totalCustomers.toLocaleString()}</span> clientes satisfeitos
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

export function MoneyBackGuarantee({ className = '' }: { className?: string }) {
  return (
    <Card className={`border-blue-200 bg-blue-50 ${className}`}>
      <CardContent className="pt-4">
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Award className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-blue-900">Garantia de 30 dias</h3>
            <p className="text-sm text-blue-700">
              Se não ficar satisfeito, devolvemos 100% do seu dinheiro, sem perguntas.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function LiveActivity({
  recentPurchases = 12,
  viewingNow = 8,
  className = ''
}: {
  recentPurchases?: number;
  viewingNow?: number;
  className?: string;
}) {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        <span>{recentPurchases} pessoas compraram nos últimos 30 minutos</span>
      </div>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        <span>{viewingNow} pessoas estão vendo esta página agora</span>
      </div>
    </div>
  );
}

// Simple Trust Badges Component
interface TrustBadgesProps {
  className?: string;
  badges?: Array<{
    icon: React.ElementType;
    text: string;
  }>;
}

export function TrustBadges({
  badges = [
    { icon: ShieldCheck, text: 'Compra Segura' },

    { icon: RefreshCcw, text: 'Garantia de Satisfação' },
  ],
  className,
}: TrustBadgesProps) {
  return (
    <div
      className={`grid grid-cols-2 gap-4 rounded-lg border bg-gray-50 p-4 text-center text-sm text-gray-700 md:grid-cols-4 ${className || ''}`}
    >
      {badges.map((badge, index) => (
        <div key={index} className="flex flex-col items-center gap-1">
          <badge.icon className="h-5 w-5 text-green-600" />
          <span>{badge.text}</span>
        </div>
      ))}
    </div>
  );
}
