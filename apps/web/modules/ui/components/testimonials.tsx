'use client';

import { useState, useEffect } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  rating: number;
  text: string;
  location?: string;
  verified?: boolean;
}

interface TestimonialsProps {
  testimonials?: Testimonial[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  maxTestimonials?: number;
  className?: string;
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Empreendedora Digital',
    rating: 5,
    text: 'Incrível! Consegui aumentar minhas vendas em 300% em apenas 2 meses. O produto superou todas as minhas expectativas.',
    location: 'São Paulo, SP',
    verified: true
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Marketing Manager',
    rating: 5,
    text: 'A melhor compra que já fiz! O conteúdo é de altíssima qualidade e os resultados apareceram rapidamente.',
    location: 'Rio de Janeiro, RJ',
    verified: true
  },
  {
    id: '3',
    name: 'Ana Costa',
    role: 'Consultora',
    rating: 5,
    text: 'Transformou completamente meu negócio. Recomendo para todos que querem crescer na área digital.',
    location: 'Belo Horizonte, MG',
    verified: true
  },
  {
    id: '4',
    name: 'Carlos Oliveira',
    role: 'CEO',
    rating: 5,
    text: 'Investimento que se pagou em 30 dias. Conteúdo prático e aplicável imediatamente.',
    location: 'Porto Alegre, RS',
    verified: true
  },
  {
    id: '5',
    name: 'Fernanda Lima',
    role: 'Influencer',
    rating: 5,
    text: 'Produto excepcional! Me ajudou a monetizar meu conhecimento de forma profissional.',
    location: 'Salvador, BA',
    verified: true
  }
];

export function Testimonials({
  testimonials = defaultTestimonials,
  autoPlay = true,
  autoPlayInterval = 5000,
  showControls = true,
  maxTestimonials = 3,
  className = ""
}: TestimonialsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayedTestimonials, setDisplayedTestimonials] = useState<Testimonial[]>([]);

  useEffect(() => {
    // Seleciona testimonials aleatórios se houver mais que o máximo
    const shuffled = [...testimonials].sort(() => Math.random() - 0.5);
    setDisplayedTestimonials(shuffled.slice(0, maxTestimonials));
  }, [testimonials, maxTestimonials]);

  useEffect(() => {
    if (!autoPlay || displayedTestimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, displayedTestimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayedTestimonials.length) % displayedTestimonials.length);
  };

  if (displayedTestimonials.length === 0) return null;

  const currentTestimonial = displayedTestimonials[currentIndex];

  return (
    <div className={`relative ${className}`}>
      <Card className="border-2 border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 mb-4">
            <Quote className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-green-800">Depoimentos de Clientes</span>
          </div>

          <div className="text-center">
            <div className="flex justify-center mb-3">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${
                    i < currentTestimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>

            <blockquote className="text-gray-700 italic mb-4">
              "{currentTestimonial.text}"
            </blockquote>

            <div className="flex items-center justify-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={currentTestimonial.avatar} />
                <AvatarFallback>
                  {currentTestimonial.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>

              <div className="text-left">
                <div className="flex items-center gap-2">
                  <p className="font-semibold text-gray-900">{currentTestimonial.name}</p>
                  {currentTestimonial.verified && (
                    <span className="text-green-600 text-xs">✓ Verificado</span>
                  )}
                </div>
                <p className="text-sm text-gray-600">{currentTestimonial.role}</p>
                {currentTestimonial.location && (
                  <p className="text-xs text-gray-500">{currentTestimonial.location}</p>
                )}
              </div>
            </div>
          </div>

          {showControls && displayedTestimonials.length > 1 && (
            <div className="flex justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={prevTestimonial}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={nextTestimonial}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {displayedTestimonials.length > 1 && (
            <div className="flex justify-center gap-1 mt-3">
              {displayedTestimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-green-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export function TestimonialCard({
  testimonial,
  className = ""
}: {
  testimonial: Testimonial;
  className?: string;
}) {
  return (
    <Card className={`border border-gray-200 ${className}`}>
      <CardContent className="pt-4">
        <div className="flex items-center gap-2 mb-3">
          <Quote className="h-4 w-4 text-gray-400" />
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-3 w-3 ${
                  i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        <p className="text-sm text-gray-700 italic mb-3">"{testimonial.text}"</p>

        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={testimonial.avatar} />
            <AvatarFallback className="text-xs">
              {testimonial.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-medium">{testimonial.name}</p>
            <p className="text-xs text-gray-500">{testimonial.role}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
