'use client';

import { useState, useEffect } from 'react';
import { AlertTriangle, Users, Clock, TrendingUp, Zap } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';

interface ScarcityProps {
  totalStock?: number;
  soldToday?: number;
  remainingStock?: number;
  showProgress?: boolean;
  showUrgency?: boolean;
  className?: string;
}

export function StockScarcity({
  totalStock = 100,
  soldToday = 47,
  remainingStock,
  showProgress = true,
  showUrgency = true,
  className = ''
}: ScarcityProps) {
  const [currentSold, setCurrentSold] = useState(soldToday);
  const actualRemaining = remainingStock || (totalStock - currentSold);
  const percentageSold = (currentSold / totalStock) * 100;

  useEffect(() => {
    // Simula vendas em tempo real
    const interval = setInterval(() => {
      setCurrentSold(prev => {
        const newSold = prev + Math.floor(Math.random() * 3);
        return Math.min(newSold, totalStock - 1);
      });
    }, 30000); // Atualiza a cada 30 segundos

    return () => clearInterval(interval);
  }, [totalStock]);

  const getUrgencyLevel = () => {
    if (percentageSold >= 90) return { level: 'critical', color: 'red', text: 'ÚLTIMAS UNIDADES!' };
    if (percentageSold >= 75) return { level: 'high', color: 'orange', text: 'ESTOQUE BAIXO!' };
    if (percentageSold >= 50) return { level: 'medium', color: 'yellow', text: 'ESTOQUE LIMITADO!' };
    return { level: 'low', color: 'green', text: 'Disponível' };
  };

  const urgency = getUrgencyLevel();

  return (
    <Card className={`border-2 border-${urgency.color}-200 bg-${urgency.color}-50 ${className}`}>
      <CardContent className="pt-4">
        <div className="flex items-center gap-2 mb-3">
          <AlertTriangle className={`h-5 w-5 text-${urgency.color}-600`} />
          <span className={`font-bold text-${urgency.color}-800`}>{urgency.text}</span>
        </div>

        {showProgress && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Vendido hoje:</span>
              <span className="font-semibold">{currentSold} de {totalStock}</span>
            </div>
            <Progress value={percentageSold} className="h-2" />
            <div className="flex justify-between text-xs text-gray-500">
              <span>0</span>
              <span>{totalStock}</span>
            </div>
          </div>
        )}

        <div className="mt-3 text-center">
          <p className="text-sm text-gray-700">
            Apenas <span className="font-bold text-red-600">{actualRemaining}</span> unidades restantes
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

export function TimeScarcity({
  endTime = new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 horas por padrão
  message = "Oferta especial termina em:",
  className = ''
}: {
  endTime?: Date;
  message?: string;
  className?: string;
}) {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = endTime.getTime() - now;

      if (distance < 0) {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
        return;
      }

      const hours = Math.floor(distance / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      setTimeLeft({ hours, minutes, seconds });
    }, 1000);

    return () => clearInterval(timer);
  }, [endTime]);

  const isUrgent = timeLeft.hours === 0 && timeLeft.minutes < 30;

  return (
    <Card className={`border-2 ${isUrgent ? 'border-red-200 bg-red-50' : 'border-orange-200 bg-orange-50'} ${className}`}>
      <CardContent className="pt-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Clock className={`h-5 w-5 ${isUrgent ? 'text-red-600' : 'text-orange-600'}`} />
            <span className={`font-semibold ${isUrgent ? 'text-red-800' : 'text-orange-800'}`}>
              {message}
            </span>
          </div>

          <div className="flex items-center justify-center gap-2">
            <div className="text-center">
              <div className={`text-2xl font-bold ${isUrgent ? 'text-red-600' : 'text-orange-600'}`}>
                {timeLeft.hours.toString().padStart(2, '0')}
              </div>
              <div className="text-xs opacity-75">HORAS</div>
            </div>
            <div className={`text-2xl font-bold ${isUrgent ? 'text-red-600' : 'text-orange-600'}`}>:</div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${isUrgent ? 'text-red-600' : 'text-orange-600'}`}>
                {timeLeft.minutes.toString().padStart(2, '0')}
              </div>
              <div className="text-xs opacity-75">MIN</div>
            </div>
            <div className={`text-2xl font-bold ${isUrgent ? 'text-red-600' : 'text-orange-600'}`}>:</div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${isUrgent ? 'text-red-600' : 'text-orange-600'}`}>
                {timeLeft.seconds.toString().padStart(2, '0')}
              </div>
              <div className="text-xs opacity-75">SEG</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SocialProofScarcity({
  recentPurchases = 12,
  viewingNow = 8,
  className = ''
}: {
  recentPurchases?: number;
  viewingNow?: number;
  className?: string;
}) {
  return (
    <Card className={`border-blue-200 bg-blue-50 ${className}`}>
      <CardContent className="pt-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-700">
              <span className="font-semibold text-green-600">{recentPurchases}</span> pessoas compraram nos últimos 30 minutos
            </span>
          </div>

          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-700">
              <span className="font-semibold text-blue-600">{viewingNow}</span> pessoas estão vendo esta página agora
            </span>
          </div>

          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-orange-500" />
            <span className="text-sm text-gray-700">
              Produto em alta! <span className="font-semibold text-orange-600">+47%</span> de vendas esta semana
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function LimitedOffer({
  originalPrice = 197,
  discountPrice = 97,
  discountPercentage = 51,
  endDate = new Date(Date.now() + 24 * 60 * 60 * 1000),
  className = ''
}: {
  originalPrice?: number;
  discountPrice?: number;
  discountPercentage?: number;
  endDate?: Date;
  className?: string;
}) {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = endDate.getTime() - now;

      if (distance < 0) {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
        return;
      }

      const hours = Math.floor(distance / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      setTimeLeft({ hours, minutes, seconds });
    }, 1000);

    return () => clearInterval(timer);
  }, [endDate]);

  return (
    <Card className={`border-2 border-red-200 bg-red-50 ${className}`}>
      <CardContent className="pt-4">
        <div className="text-center">
          <Badge className="mb-3 bg-red-600 text-white animate-pulse">
            OFERTA LIMITADA
          </Badge>

          <div className="mb-3">
            <div className="flex items-center justify-center gap-2">
              <span className="text-2xl font-bold text-red-600">
                R$ {discountPrice.toFixed(2).replace('.', ',')}
              </span>
              <span className="text-lg text-gray-500 line-through">
                R$ {originalPrice.toFixed(2).replace('.', ',')}
              </span>
            </div>
            <p className="text-sm text-gray-600">
              Economia de {discountPercentage}% - Apenas hoje!
            </p>
          </div>

          <div className="text-sm text-gray-700 mb-2">
            Oferta termina em:
          </div>

          <div className="flex items-center justify-center gap-1 text-lg font-bold text-red-600">
            <span>{timeLeft.hours.toString().padStart(2, '0')}</span>:
            <span>{timeLeft.minutes.toString().padStart(2, '0')}</span>:
            <span>{timeLeft.seconds.toString().padStart(2, '0')}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Simple Scarcity Component
interface SimpleScarcityProps {
  message?: string;
  currentStock?: number;
  threshold?: number;
  className?: string;
}

export function Scarcity({
  message = 'Últimas unidades disponíveis!',
  currentStock = 5, // Mock value
  threshold = 10,
  className,
}: SimpleScarcityProps) {
  if (currentStock > threshold) {
    return null;
  }

  return (
    <div
      className={`flex items-center justify-center gap-2 rounded-md bg-yellow-50 p-3 text-sm font-semibold text-yellow-700 shadow-sm ${className || ''}`}
    >
      <AlertTriangle className="h-4 w-4" />
      <span>
        {message} {currentStock <= threshold && `Restam apenas ${currentStock} unidades!`}
      </span>
    </div>
  );
}
