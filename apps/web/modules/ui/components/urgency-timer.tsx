'use client';

import { useState, useEffect } from 'react';
import { Clock, AlertTriangle } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';

interface UrgencyTimerProps {
  endTime?: Date;
  message?: string;
  variant?: 'default' | 'warning' | 'danger';
  showIcon?: boolean;
  className?: string;
}

export function UrgencyTimer({
  endTime = new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 horas por padrão
  message = "Oferta especial expira em:",
  variant = 'warning',
  showIcon = true,
  className = ""
}: UrgencyTimerProps) {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = endTime.getTime() - now;

      if (distance < 0) {
        setIsExpired(true);
        clearInterval(timer);
        return;
      }

      const hours = Math.floor(distance / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      setTimeLeft({ hours, minutes, seconds });
    }, 1000);

    return () => clearInterval(timer);
  }, [endTime]);

  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return 'bg-red-600 text-white border-red-700';
      case 'warning':
        return 'bg-orange-600 text-white border-orange-700';
      default:
        return 'bg-blue-600 text-white border-blue-700';
    }
  };

  if (isExpired) {
    return (
      <Card className={`border-red-200 bg-red-50 ${className}`}>
        <CardContent className="pt-4">
          <div className="flex items-center justify-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-semibold">Oferta expirada!</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-2 ${getVariantStyles()} ${className}`}>
      <CardContent className="py-4 gap-6 flex justify-center items-center">
        <div className="flex pt-1 items-center   justify-center gap-2">
          {showIcon && <Clock className="h-5 w-5" />}
          <span className="font-semibold">{message}</span>
        </div>
        <div className="flex items-center justify-center gap-2  ">
          <div className="text-center">
            <div className="text-2xl font-bold">{timeLeft.hours.toString().padStart(2, '0')}</div>
            <div className="text-xs opacity-90">HORAS</div>
          </div>
          <div className="text-2xl font-bold">:</div>
          <div className="text-center">
            <div className="text-2xl font-bold">{timeLeft.minutes.toString().padStart(2, '0')}</div>
            <div className="text-xs opacity-90">MIN</div>
          </div>
          <div className="text-2xl font-bold">:</div>
          <div className="text-center">
            <div className="text-2xl font-bold">{timeLeft.seconds.toString().padStart(2, '0')}</div>
            <div className="text-xs opacity-90">SEG</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function UrgencyBadge({
  text = "LIMITADO",
  variant = 'danger',
  className = ""
}: {
  text?: string;
  variant?: 'default' | 'warning' | 'danger';
  className?: string;
}) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return 'bg-red-600 text-white animate-pulse';
      case 'warning':
        return 'bg-orange-600 text-white';
      default:
        return 'bg-blue-600 text-white';
    }
  };

  return (
    <Badge className={`${getVariantStyles()} ${className}`}>
      {text}
    </Badge>
  );
}
