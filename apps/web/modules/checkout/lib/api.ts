import { apiClient } from "../../shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { InferRequestType, InferResponseType } from "hono";

// Types
export type CheckoutLink = {
  id: string;
  productId: string;
  offerId?: string;
  url: string;
  expiresAt?: string;
  utmParams?: Record<string, string>;
  customParams?: Record<string, string>;
  clickCount: number;
  conversionCount: number;
  totalRevenue: number;
  createdAt: string;
  updatedAt: string;
};

export type CheckoutLinkAnalytics = {
  totalClicks: number;
  totalConversions: number;
  conversionRate: number;
  totalRevenue: number;
  averageOrderValue: number;
};

export type GenerateCheckoutLinkRequest = {
  productId: string;
  offerId?: string;
  expiresAt?: string;
  utmParams?: Record<string, string>;
  customParams?: Record<string, string>;
};

export type GenerateBulkCheckoutLinksRequest = {
  links: GenerateCheckoutLinkRequest[];
};

export type TrackLinkRequest = {
  linkId: string;
  action: "click" | "conversion";
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  utmParams?: Record<string, string>;
  orderId?: string;
  amount?: number;
  paymentMethod?: string;
};

// Query Keys
export const checkoutLinksQueryKey = (productId?: string) =>
  productId ? ["checkout-links", productId] : ["checkout-links"];

export const checkoutLinkAnalyticsQueryKey = (linkId: string) =>
  ["checkout-link-analytics", linkId];

// Queries
export const useCheckoutLinkAnalyticsQuery = (linkId: string) => {
  return useQuery({
    queryKey: checkoutLinkAnalyticsQueryKey(linkId),
    queryFn: async (): Promise<CheckoutLinkAnalytics> => {
      // For now, return mock data since analytics endpoint doesn't exist
      return {
        totalClicks: 0,
        totalConversions: 0,
        conversionRate: 0,
        totalRevenue: 0,
        averageOrderValue: 0,
      };
    },
    enabled: !!linkId,
  });
};

// Mutations
export const useGenerateCheckoutLinkMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GenerateCheckoutLinkRequest): Promise<CheckoutLink> => {
      // Use the existing payments API to create checkout link
      const response = await apiClient.payments["create-checkout-link"].$post({
        query: {
          type: "one-time",
          productId: data.productId,
          redirectUrl: data.utmParams?.redirectUrl,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate checkout link");
      }

      const result = await response.json();

      // Transform the response to match our CheckoutLink type
      return {
        id: `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        productId: data.productId,
        offerId: data.offerId,
        url: result.checkoutLink.url,
        expiresAt: data.expiresAt,
        utmParams: data.utmParams,
        customParams: data.customParams,
        clickCount: 0,
        conversionCount: 0,
        totalRevenue: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: checkoutLinksQueryKey(data.productId),
      });
    },
  });
};

export const useGenerateBulkCheckoutLinksMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GenerateBulkCheckoutLinksRequest) => {
      // Generate links one by one using the existing API
      const checkoutLinks: CheckoutLink[] = [];

      for (const linkData of data.links) {
        try {
          const response = await apiClient.payments["create-checkout-link"].$post({
            query: {
              type: "one-time",
              productId: linkData.productId,
              redirectUrl: linkData.utmParams?.redirectUrl,
            },
          });

          if (response.ok) {
            const result = await response.json();
            checkoutLinks.push({
              id: `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              productId: linkData.productId,
              offerId: linkData.offerId,
              url: result.checkoutLink.url,
              expiresAt: linkData.expiresAt,
              utmParams: linkData.utmParams,
              customParams: linkData.customParams,
              clickCount: 0,
              conversionCount: 0,
              totalRevenue: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            });
          }
        } catch (error) {
          console.error(`Failed to generate link for product ${linkData.productId}:`, error);
        }
      }

      return { checkoutLinks };
    },
    onSuccess: (data) => {
      // Invalidate related queries for all affected products
      data.checkoutLinks.forEach((link) => {
        queryClient.invalidateQueries({
          queryKey: checkoutLinksQueryKey(link.productId),
        });
      });
    },
  });
};

export const useTrackLinkMutation = () => {
  return useMutation({
    mutationFn: async (data: TrackLinkRequest) => {
      // For now, just log the tracking data since we don't have a tracking endpoint
      console.log("Tracking link event:", data);

      // Return a mock response
      return { success: true, tracked: true };
    },
  });
};
