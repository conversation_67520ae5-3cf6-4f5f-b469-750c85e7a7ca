"use client";

import { useState, useEffect } from "react";
import { AiAgentsDashboard } from "../components/AiAgentsDashboard";
import { AgentConfigForm } from "../components/AgentConfigForm";
import { Button } from "@ui/components/button";
import { ArrowLeft, Plus } from "lucide-react";

// Mock data - em produção viria da API
const mockAgents = [
  {
    id: "1",
    name: "Agente de Vendas - Curso X",
    description: "Bot especializado em converter leads do curso de marketing digital",
    type: "SALES" as const,
    status: "ACTIVE" as const,
    totalInteractions: 1247,
    totalLeads: 234,
    totalConversions: 89,
    avgResponseTime: 15.5,
    satisfactionScore: 4.2,
    lastActiveAt: "2024-01-15T10:30:00Z"
  },
  {
    id: "2",
    name: "Suporte Técnico",
    description: "Assistente para resolver dúvidas sobre acesso e funcionalidades",
    type: "SUPPORT" as const,
    status: "ACTIVE" as const,
    totalInteractions: 892,
    totalLeads: 0,
    totalConversions: 0,
    avgResponseTime: 8.2,
    satisfactionScore: 4.5,
    lastActiveAt: "2024-01-15T09:15:00Z"
  },
  {
    id: "3",
    name: "Welcome Bot",
    description: "Guia novos clientes no onboarding e primeiros passos",
    type: "ONBOARDING" as const,
    status: "PAUSED" as const,
    totalInteractions: 456,
    totalLeads: 0,
    totalConversions: 0,
    avgResponseTime: 12.1,
    satisfactionScore: 4.0,
    lastActiveAt: "2024-01-14T16:45:00Z"
  }
];

const mockMetrics = {
  totalAgents: 3,
  totalLeads: 234,
  totalConversions: 89,
  totalInteractions: 2595,
  avgResponseTime: 12.3,
  satisfactionScore: 4.2
};

type ViewMode = "dashboard" | "create" | "edit";

export default function AiAgentsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("dashboard");
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateAgent = async (data: any) => {
    setIsLoading(true);
    try {
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("Creating agent:", data);
      setViewMode("dashboard");
    } catch (error) {
      console.error("Error creating agent:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditAgent = async (data: any) => {
    setIsLoading(true);
    try {
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("Updating agent:", data);
      setViewMode("dashboard");
    } catch (error) {
      console.error("Error updating agent:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestAgent = () => {
    console.log("Testing agent...");
    // Implementar teste do agente
  };

  const handleBackToDashboard = () => {
    setViewMode("dashboard");
    setSelectedAgent(null);
  };

  const handleCreateNew = () => {
    setSelectedAgent(null);
    setViewMode("create");
  };

  const handleEditAgentClick = (agentId: string) => {
    setSelectedAgent(agentId);
    setViewMode("edit");
  };

  if (viewMode === "create") {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <Button variant="ghost" onClick={handleBackToDashboard} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar ao Dashboard
          </Button>
        </div>
        <AgentConfigForm
          onSubmit={handleCreateAgent}
          onTest={handleTestAgent}
          isLoading={isLoading}
        />
      </div>
    );
  }

  if (viewMode === "edit") {
    const agent = mockAgents.find(a => a.id === selectedAgent);
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <Button variant="ghost" onClick={handleBackToDashboard} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar ao Dashboard
          </Button>
        </div>
        <AgentConfigForm
          initialData={agent}
          onSubmit={handleEditAgent}
          onTest={handleTestAgent}
          isLoading={isLoading}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <AiAgentsDashboard 
        agents={mockAgents}
        metrics={mockMetrics}
        onCreateNew={handleCreateNew}
        onEditAgent={handleEditAgentClick}
      />
    </div>
  );
}
