export type AgentType = "SALES" | "SUPPORT" | "ONBOARDING" | "COMMUNITY";
export type AgentStatus = "ACTIVE" | "INACTIVE" | "PAUSED" | "TRAINING";
export type AgentPersonality = "FRIENDLY" | "PROFESSIONAL" | "ENTHUSIASTIC" | "TECHNICAL" | "CASUAL";
export type IntegrationType = "WHATSAPP" | "TELEGRAM" | "CHAT_WIDGET" | "EMAIL" | "CRM";

export interface AiAgent {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  type: AgentType;
  status: AgentStatus;
  personality: AgentPersonality;
  tone?: string;
  language: string;
  systemPrompt?: string;
  welcomeMessage?: string;
  fallbackMessage?: string;
  trainingData?: Record<string, any>;
  knowledgeBase?: Record<string, any>;
  conversionGoal?: number;
  leadGoal?: number;
  responseTimeGoal?: number;
  isPublic: boolean;
  allowEscalation: boolean;
  maxInteractions?: number;
  totalInteractions: number;
  totalLeads: number;
  totalConversions: number;
  avgResponseTime?: number;
  satisfactionScore?: number;
  lastActiveAt?: string;
  createdAt: string;
  updatedAt: string;
  integrations?: AgentIntegration[];
  conversations?: AgentConversation[];
  templates?: AgentTemplate[];
  analytics?: AgentAnalytics[];
}

export interface AgentIntegration {
  id: string;
  agentId: string;
  type: IntegrationType;
  name: string;
  settings: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AgentConversation {
  id: string;
  agentId: string;
  organizationId: string;
  customerId?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  messages: Array<{
    role: "user" | "assistant" | "system";
    content: string;
    timestamp: string;
  }>;
  status: string;
  channel: string;
  interactionCount: number;
  leadGenerated: boolean;
  conversionGenerated: boolean;
  satisfactionRating?: number;
  escalatedToHuman: boolean;
  startedAt: string;
  lastMessageAt: string;
  completedAt?: string;
}

export interface AgentTemplate {
  id: string;
  agentId: string;
  name: string;
  description?: string;
  type: string;
  content: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AgentAnalytics {
  id: string;
  agentId: string;
  organizationId: string;
  date: string;
  period: string;
  totalInteractions: number;
  totalLeads: number;
  totalConversions: number;
  avgResponseTime?: number;
  satisfactionScore?: number;
  escalationRate?: number;
  whatsappInteractions: number;
  telegramInteractions: number;
  chatInteractions: number;
  emailInteractions: number;
  createdAt: string;
}

export interface AgentMetrics {
  totalAgents: number;
  totalLeads: number;
  totalConversions: number;
  totalInteractions: number;
  avgResponseTime: number;
  satisfactionScore: number;
}

export interface AgentPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  features: string[];
  limits: {
    maxAgents: number;
    maxInteractions: number;
    maxIntegrations: number;
    maxTemplates: number;
    maxTrainingDocuments: number;
    maxConversations: number;
  };
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    description: string;
  }>;
  isPopular: boolean;
  isEnterprise: boolean;
}

export interface TrainingDocument {
  id: string;
  name: string;
  type: "pdf" | "doc" | "docx" | "txt" | "md";
  content: string;
  metadata: {
    size: number;
    uploadedAt: string;
    processedAt?: string;
  };
}

export interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  createdAt: string;
}

export interface QAPair {
  id: string;
  question: string;
  answer: string;
  category: string;
  confidence: number;
  createdAt: string;
}

export interface ConversationExample {
  id: string;
  messages: Array<{
    role: "user" | "assistant";
    content: string;
    timestamp: string;
  }>;
  satisfaction?: number;
  createdAt: string;
}

export interface TrainingData {
  id: string;
  agentId: string;
  documents: TrainingDocument[];
  knowledgeBase: KnowledgeItem[];
  qaPairs: QAPair[];
  conversations: ConversationExample[];
}

export interface TrainingResult {
  success: boolean;
  processedDocuments: number;
  extractedKnowledge: number;
  qaPairsGenerated: number;
  confidence: number;
  errors: string[];
  recommendations: string[];
}

export interface WhatsAppMessage {
  to: string;
  type: "text" | "template" | "interactive" | "image" | "document";
  text?: {
    body: string;
  };
  template?: {
    name: string;
    language: {
      code: string;
    };
    components?: any[];
  };
  interactive?: {
    type: "button" | "list" | "product" | "product_list";
    header?: {
      type: string;
      text?: string;
    };
    body: {
      text: string;
    };
    footer?: {
      text: string;
    };
    action: {
      buttons?: Array<{
        type: string;
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: any[];
    };
  };
  image?: {
    link?: string;
    id?: string;
    caption?: string;
  };
  document?: {
    link?: string;
    id?: string;
    filename?: string;
    caption?: string;
  };
}

export interface WhatsAppWebhook {
  object: string;
  entry: Array<{
    id: string;
    changes: Array<{
      value: {
        messaging_product: string;
        metadata: {
          display_phone_number: string;
          phone_number_id: string;
        };
        contacts?: Array<{
          profile: {
            name: string;
          };
          wa_id: string;
        }>;
        messages?: Array<{
          from: string;
          id: string;
          timestamp: string;
          type: string;
          text?: {
            body: string;
          };
          interactive?: {
            type: string;
            button_reply?: {
              id: string;
              title: string;
            };
            list_reply?: {
              id: string;
              title: string;
            };
          };
        }>;
        statuses?: Array<{
          id: string;
          status: "sent" | "delivered" | "read" | "failed";
          timestamp: string;
          recipient_id: string;
        }>;
      };
      field: string;
    }>;
  }>;
}
