"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { 
  Bot, 
  Plus, 
  Play, 
  Pause, 
  Settings, 
  BarChart3, 
  MessageSquare, 
  Users, 
  TrendingUp,
  Clock,
  Star,
  Zap
} from "lucide-react";
import { cn } from "@ui/lib";

interface AiAgent {
  id: string;
  name: string;
  description: string;
  type: "SALES" | "SUPPORT" | "ONBOARDING" | "COMMUNITY";
  status: "ACTIVE" | "INACTIVE" | "PAUSED" | "TRAINING";
  totalInteractions: number;
  totalLeads: number;
  totalConversions: number;
  avgResponseTime: number;
  satisfactionScore: number;
  lastActiveAt: string;
}

interface AiAgentsDashboardProps {
  agents: AiAgent[];
  metrics: {
    totalAgents: number;
    totalLeads: number;
    totalConversions: number;
    totalInteractions: number;
    avgResponseTime: number;
    satisfactionScore: number;
  };
}

const agentTypeConfig = {
  SALES: {
    label: "Vendas",
    icon: TrendingUp,
    color: "bg-green-100 text-green-800",
    description: "Converte visitantes em clientes"
  },
  SUPPORT: {
    label: "Suporte",
    icon: MessageSquare,
    color: "bg-blue-100 text-blue-800",
    description: "Resolve dúvidas e problemas"
  },
  ONBOARDING: {
    label: "Onboarding",
    icon: Users,
    color: "bg-purple-100 text-purple-800",
    description: "Guia novos clientes"
  },
  COMMUNITY: {
    label: "Comunidade",
    icon: Users,
    color: "bg-orange-100 text-orange-800",
    description: "Modera e engaja comunidades"
  }
};

const statusConfig = {
  ACTIVE: {
    label: "Ativo",
    color: "bg-green-100 text-green-800",
    icon: Play
  },
  INACTIVE: {
    label: "Inativo",
    color: "bg-gray-100 text-gray-800",
    icon: Pause
  },
  PAUSED: {
    label: "Pausado",
    color: "bg-yellow-100 text-yellow-800",
    icon: Pause
  },
  TRAINING: {
    label: "Treinando",
    color: "bg-blue-100 text-blue-800",
    icon: Zap
  }
};

export function AiAgentsDashboard({ agents, metrics }: AiAgentsDashboardProps) {
  const [selectedTab, setSelectedTab] = useState("overview");

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
    return num.toString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agentes de IA</h1>
          <p className="text-muted-foreground">
            Automatize vendas, suporte e engajamento com assistentes inteligentes
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Novo Agente
        </Button>
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Agentes</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalAgents}</div>
            <p className="text-xs text-muted-foreground">
              {agents.filter(a => a.status === "ACTIVE").length} ativos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leads</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(metrics.totalLeads)}</div>
            <p className="text-xs text-muted-foreground">
              +12% vs mês anterior
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversões</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(metrics.totalConversions)}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalLeads > 0 ? Math.round((metrics.totalConversions / metrics.totalLeads) * 100) : 0}% taxa de conversão
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tempo de Resposta</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatTime(metrics.avgResponseTime)}</div>
            <p className="text-xs text-muted-foreground">
              Tempo médio
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="agents">Meus Agentes</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Geral
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Satisfação do Cliente</span>
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="font-semibold">{metrics.satisfactionScore.toFixed(1)}/5</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total de Interações</span>
                  <span className="font-semibold">{formatNumber(metrics.totalInteractions)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Taxa de Conversão</span>
                  <span className="font-semibold">
                    {metrics.totalLeads > 0 ? Math.round((metrics.totalConversions / metrics.totalLeads) * 100) : 0}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Agentes Ativos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {agents.filter(a => a.status === "ACTIVE").slice(0, 3).map((agent) => {
                    const config = agentTypeConfig[agent.type];
                    const Icon = config.icon;
                    return (
                      <div key={agent.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={cn("p-2 rounded-lg", config.color)}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="font-medium">{agent.name}</p>
                            <p className="text-sm text-muted-foreground">{config.label}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{agent.totalInteractions}</p>
                          <p className="text-xs text-muted-foreground">interações</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {agents.map((agent) => {
              const typeConfig = agentTypeConfig[agent.type];
              const statusConfig = statusConfig[agent.status];
              const TypeIcon = typeConfig.icon;
              const StatusIcon = statusConfig.icon;

              return (
                <Card key={agent.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={cn("p-2 rounded-lg", typeConfig.color)}>
                          <TypeIcon className="h-5 w-5" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{agent.name}</CardTitle>
                          <CardDescription>{agent.description}</CardDescription>
                        </div>
                      </div>
                      <Badge className={cn("gap-1", statusConfig.color)}>
                        <StatusIcon className="h-3 w-3" />
                        {statusConfig.label}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Interações</p>
                        <p className="font-semibold">{formatNumber(agent.totalInteractions)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Leads</p>
                        <p className="font-semibold">{formatNumber(agent.totalLeads)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Conversões</p>
                        <p className="font-semibold">{formatNumber(agent.totalConversions)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Tempo Médio</p>
                        <p className="font-semibold">{formatTime(agent.avgResponseTime)}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm font-medium">{agent.satisfactionScore.toFixed(1)}</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analytics Detalhados</CardTitle>
              <CardDescription>
                Métricas de performance dos seus agentes de IA
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Analytics em Desenvolvimento</h3>
                <p className="text-muted-foreground">
                  Em breve você terá acesso a gráficos detalhados e relatórios avançados.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Templates de Agentes</CardTitle>
              <CardDescription>
                Use templates pré-configurados para criar agentes rapidamente
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Templates em Desenvolvimento</h3>
                <p className="text-muted-foreground">
                  Em breve você terá acesso a templates prontos para diferentes tipos de negócio.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
