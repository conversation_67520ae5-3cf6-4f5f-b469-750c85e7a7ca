"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { ScrollArea } from "@ui/components/scroll-area";
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  Clock,
  Star
} from "lucide-react";
import { useAgent, type AgentConfig, type ConversationContext } from "@repo/ai";
import { cn } from "@ui/lib";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: string;
  metadata?: {
    confidence?: number;
    intent?: string;
    nextAction?: string;
  };
}

interface AgentChatProps {
  agent: AgentConfig;
  context: ConversationContext;
  onResponse?: (response: any) => void;
  className?: string;
}

export function AgentChat({ agent, context, onResponse, className }: AgentChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const { sendMessage, isProcessing, lastResponse, error } = useAgent({
    agentConfig: agent,
    context: {
      ...context,
      conversationHistory: messages,
    },
    onResponse: (response) => {
      const newMessage: Message = {
        id: Date.now().toString(),
        role: "assistant",
        content: response.message,
        timestamp: new Date().toISOString(),
        metadata: {
          confidence: response.confidence,
          intent: response.intent,
          nextAction: response.nextAction,
        },
      };
      
      setMessages(prev => [...prev, newMessage]);
      setIsTyping(false);
      onResponse?.(response);
    },
    onError: (err) => {
      console.error("Erro no chat:", err);
      setIsTyping(false);
    },
  });

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    try {
      await sendMessage(inputMessage);
    } catch (err) {
      console.error("Erro ao enviar mensagem:", err);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Auto scroll para última mensagem
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getIntentColor = (intent?: string) => {
    switch (intent) {
      case "sales": return "bg-green-100 text-green-800";
      case "support": return "bg-blue-100 text-blue-800";
      case "onboarding": return "bg-purple-100 text-purple-800";
      case "community": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getIntentLabel = (intent?: string) => {
    switch (intent) {
      case "sales": return "Vendas";
      case "support": return "Suporte";
      case "onboarding": return "Onboarding";
      case "community": return "Comunidade";
      default: return "Geral";
    }
  };

  return (
    <Card className={cn("h-[600px] flex flex-col", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Bot className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg">{agent.name}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {agent.type === "SALES" && "Agente de Vendas"}
                {agent.type === "SUPPORT" && "Agente de Suporte"}
                {agent.type === "ONBOARDING" && "Agente de Onboarding"}
                {agent.type === "COMMUNITY" && "Agente de Comunidade"}
              </p>
            </div>
          </div>
          <Badge variant="outline" className="gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            Online
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea ref={scrollAreaRef} className="flex-1 px-4">
          <div className="space-y-4 py-4">
            {messages.length === 0 && (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Inicie uma conversa</h3>
                <p className="text-muted-foreground">
                  {agent.welcomeMessage || "Olá! Como posso ajudá-lo hoje?"}
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.role === "user" ? "justify-end" : "justify-start"
                )}
              >
                {message.role === "assistant" && (
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Bot className="h-4 w-4 text-primary" />
                  </div>
                )}
                
                <div
                  className={cn(
                    "max-w-[80%] rounded-lg px-4 py-2",
                    message.role === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  
                  {message.role === "assistant" && message.metadata && (
                    <div className="flex items-center gap-2 mt-2">
                      {message.metadata.intent && (
                        <Badge className={cn("text-xs", getIntentColor(message.metadata.intent))}>
                          {getIntentLabel(message.metadata.intent)}
                        </Badge>
                      )}
                      {message.metadata.confidence && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Star className="h-3 w-3" />
                          {Math.round(message.metadata.confidence * 100)}%
                        </div>
                      )}
                    </div>
                  )}
                  
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatTime(message.timestamp)}
                  </p>
                </div>

                {message.role === "user" && (
                  <div className="p-2 bg-muted rounded-lg">
                    <User className="h-4 w-4" />
                  </div>
                )}
              </div>
            ))}

            {isTyping && (
              <div className="flex gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Bot className="h-4 w-4 text-primary" />
                </div>
                <div className="bg-muted rounded-lg px-4 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Digitando...</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Error Display */}
        {error && (
          <div className="px-4 py-2 bg-destructive/10 border-t">
            <div className="flex items-center gap-2 text-destructive text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>Erro: {error.message}</span>
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="border-t p-4">
          <div className="flex gap-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Digite sua mensagem..."
              disabled={isProcessing}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isProcessing}
              size="icon"
            >
              {isProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
