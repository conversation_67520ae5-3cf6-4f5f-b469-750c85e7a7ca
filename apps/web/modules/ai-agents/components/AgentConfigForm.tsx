"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Badge } from "@ui/components/badge";
import { 
  Bo<PERSON>, 
  Settings, 
  MessageSquare, 
  Brain, 
  Target, 
  Zap,
  Save,
  TestTube,
  Upload,
  FileText,
  Users,
  TrendingUp,
  MessageCircle,
  Users2
} from "lucide-react";

const agentConfigSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional(),
  type: z.enum(["SALES", "SUPPORT", "ONBOARDING", "COMMUNITY"]),
  personality: z.enum(["FRIENDLY", "PROFESSIONAL", "ENTHUSIASTIC", "TECHNICAL", "CASUAL"]),
  language: z.string().default("pt-BR"),
  systemPrompt: z.string().optional(),
  welcomeMessage: z.string().optional(),
  fallbackMessage: z.string().optional(),
  conversionGoal: z.number().min(0).max(1).optional(),
  leadGoal: z.number().min(0).optional(),
  responseTimeGoal: z.number().min(0).optional(),
  allowEscalation: z.boolean().default(true),
  maxInteractions: z.number().min(1).optional(),
});

type AgentConfigFormData = z.infer<typeof agentConfigSchema>;

interface AgentConfigFormProps {
  initialData?: Partial<AgentConfigFormData>;
  onSubmit: (data: AgentConfigFormData) => void;
  onTest?: () => void;
  isLoading?: boolean;
}

const agentTypeConfig = {
  SALES: {
    label: "Vendas",
    icon: TrendingUp,
    description: "Converte visitantes em clientes",
    color: "bg-green-100 text-green-800"
  },
  SUPPORT: {
    label: "Suporte",
    icon: MessageSquare,
    description: "Resolve dúvidas e problemas",
    color: "bg-blue-100 text-blue-800"
  },
  ONBOARDING: {
    label: "Onboarding",
    icon: Users,
    description: "Guia novos clientes",
    color: "bg-purple-100 text-purple-800"
  },
  COMMUNITY: {
    label: "Comunidade",
    icon: Users2,
    description: "Modera e engaja comunidades",
    color: "bg-orange-100 text-orange-800"
  }
};

const personalityConfig = {
  FRIENDLY: {
    label: "Amigável",
    description: "Tom caloroso e acolhedor"
  },
  PROFESSIONAL: {
    label: "Profissional",
    description: "Tom formal e técnico"
  },
  ENTHUSIASTIC: {
    label: "Entusiasmado",
    description: "Tom animado e motivador"
  },
  TECHNICAL: {
    label: "Técnico",
    description: "Tom especializado e detalhado"
  },
  CASUAL: {
    label: "Casual",
    description: "Tom descontraído e informal"
  }
};

export function AgentConfigForm({ 
  initialData, 
  onSubmit, 
  onTest, 
  isLoading = false 
}: AgentConfigFormProps) {
  const [activeTab, setActiveTab] = useState("basic");
  const [trainingFiles, setTrainingFiles] = useState<File[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<AgentConfigFormData>({
    resolver: zodResolver(agentConfigSchema),
    defaultValues: {
      name: "",
      description: "",
      type: "SALES",
      personality: "FRIENDLY",
      language: "pt-BR",
      allowEscalation: true,
      ...initialData
    }
  });

  const selectedType = watch("type");
  const selectedPersonality = watch("personality");

  const handleFormSubmit = (data: AgentConfigFormData) => {
    onSubmit(data);
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      setTrainingFiles(prev => [...prev, ...Array.from(files)]);
    }
  };

  const removeTrainingFile = (index: number) => {
    setTrainingFiles(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Configurar Agente de IA</h1>
          <p className="text-muted-foreground">
            Configure seu agente inteligente para automatizar vendas e atendimento
          </p>
        </div>
        <div className="flex gap-2">
          {onTest && (
            <Button variant="outline" onClick={onTest}>
              <TestTube className="h-4 w-4 mr-2" />
              Testar
            </Button>
          )}
          <Button onClick={handleSubmit(handleFormSubmit)} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Salvando..." : "Salvar Agente"}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Básico</TabsTrigger>
          <TabsTrigger value="personality">Personalidade</TabsTrigger>
          <TabsTrigger value="training">Treinamento</TabsTrigger>
          <TabsTrigger value="goals">Objetivos</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Informações Básicas
              </CardTitle>
              <CardDescription>
                Configure as informações fundamentais do seu agente
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome do Agente *</Label>
                  <Input
                    id="name"
                    {...register("name")}
                    placeholder="Ex: Agente de Vendas do Curso X"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Tipo de Agente *</Label>
                  <Select value={watch("type")} onValueChange={(value: string) => setValue("type", value as any)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(agentTypeConfig).map(([key, config]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center gap-2">
                            <config.icon className="h-4 w-4" />
                            {config.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Textarea
                  id="description"
                  {...register("description")}
                  placeholder="Descreva o propósito e função do agente..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Idioma</Label>
                <Select value={watch("language")} onValueChange={(value: string) => setValue("language", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                    <SelectItem value="en-US">English (US)</SelectItem>
                    <SelectItem value="es-ES">Español</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedType && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`p-2 rounded-lg ${agentTypeConfig[selectedType].color}`}>
                      {(() => {
                        const IconComponent = agentTypeConfig[selectedType].icon;
                        return <IconComponent className="h-4 w-4" />;
                      })()}
                    </div>
                    <span className="font-medium">{agentTypeConfig[selectedType].label}</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {agentTypeConfig[selectedType].description}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="personality" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Personalidade e Tom
              </CardTitle>
              <CardDescription>
                Defina como o agente se comporta e se comunica
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Personalidade</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Object.entries(personalityConfig).map(([key, config]) => (
                    <div
                      key={key}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedPersonality === key
                          ? "border-primary bg-primary/5"
                          : "border-muted hover:border-primary/50"
                      }`}
                      onClick={() => setValue("personality", key as any)}
                    >
                      <div className="font-medium">{config.label}</div>
                      <div className="text-sm text-muted-foreground">{config.description}</div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="systemPrompt">Instruções do Sistema</Label>
                <Textarea
                  id="systemPrompt"
                  {...register("systemPrompt")}
                  placeholder="Defina instruções específicas para o comportamento do agente..."
                  rows={4}
                />
                <p className="text-sm text-muted-foreground">
                  Essas instruções orientam como o agente deve se comportar em diferentes situações.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="welcomeMessage">Mensagem de Boas-vindas</Label>
                  <Textarea
                    id="welcomeMessage"
                    {...register("welcomeMessage")}
                    placeholder="Olá! Como posso ajudá-lo hoje?"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fallbackMessage">Mensagem de Fallback</Label>
                  <Textarea
                    id="fallbackMessage"
                    {...register("fallbackMessage")}
                    placeholder="Desculpe, não entendi. Pode reformular sua pergunta?"
                    rows={2}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="training" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Treinamento e Conhecimento
              </CardTitle>
              <CardDescription>
                Treine seu agente com documentos e informações específicas
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label>Upload de Documentos</Label>
                  <div className="mt-2">
                    <input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={(e) => handleFileUpload(e.target.files)}
                      className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Formatos suportados: PDF, DOC, DOCX, TXT
                    </p>
                  </div>
                </div>

                {trainingFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>Arquivos Carregados</Label>
                    <div className="space-y-2">
                      {trainingFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span className="text-sm">{file.name}</span>
                            <Badge variant="secondary">
                              {(file.size / 1024).toFixed(1)} KB
                            </Badge>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTrainingFile(index)}
                          >
                            Remover
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Base de Conhecimento</Label>
                  <Textarea
                    placeholder="Adicione informações específicas sobre seus produtos, serviços, políticas, etc..."
                    rows={6}
                  />
                  <p className="text-sm text-muted-foreground">
                    Use este campo para adicionar informações específicas que o agente deve conhecer.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Objetivos e Métricas
              </CardTitle>
              <CardDescription>
                Defina metas e configurações avançadas para o agente
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="conversionGoal">Meta de Conversão (%)</Label>
                  <Input
                    id="conversionGoal"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    {...register("conversionGoal", { valueAsNumber: true })}
                    placeholder="15.0"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="leadGoal">Meta de Leads (mês)</Label>
                  <Input
                    id="leadGoal"
                    type="number"
                    min="0"
                    {...register("leadGoal", { valueAsNumber: true })}
                    placeholder="100"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="responseTimeGoal">Meta de Tempo de Resposta (segundos)</Label>
                  <Input
                    id="responseTimeGoal"
                    type="number"
                    min="0"
                    {...register("responseTimeGoal", { valueAsNumber: true })}
                    placeholder="30"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxInteractions">Máximo de Interações por Conversa</Label>
                  <Input
                    id="maxInteractions"
                    type="number"
                    min="1"
                    {...register("maxInteractions", { valueAsNumber: true })}
                    placeholder="50"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Permitir Escalação para Humano</Label>
                    <p className="text-sm text-muted-foreground">
                      Quando o agente não conseguir resolver, escalar para atendimento humano
                    </p>
                  </div>
                  <Switch
                    checked={watch("allowEscalation")}
                    onCheckedChange={(checked: boolean) => setValue("allowEscalation", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
