"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import { 
  Search, 
  Bot, 
  TrendingUp, 
  MessageSquare, 
  Users, 
  Users2,
  Star,
  Download,
  Eye,
  Copy,
  Check
} from "lucide-react";
import { cn } from "@ui/lib";

interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  type: "SALES" | "SUPPORT" | "ONBOARDING" | "COMMUNITY";
  category: string;
  industry: string;
  difficulty: "BEGINNER" | "INTERMEDIATE" | "ADVANCED";
  rating: number;
  downloads: number;
  features: string[];
  preview: {
    welcomeMessage: string;
    sampleQuestions: string[];
    sampleResponses: string[];
  };
  isPremium: boolean;
  price?: number;
}

const mockTemplates: AgentTemplate[] = [
  {
    id: "1",
    name: "Vendas de Curso Online",
    description: "Template otimizado para vender cursos e produtos digitais",
    type: "SALES",
    category: "Educação",
    industry: "E-learning",
    difficulty: "BEGINNER",
    rating: 4.8,
    downloads: 1250,
    features: [
      "Qualificação de leads",
      "Apresentação de benefícios",
      "Tratamento de objeções",
      "Agendamento de calls",
      "Follow-up automático"
    ],
    preview: {
      welcomeMessage: "Olá! 👋 Sou o assistente de vendas do [Nome do Curso]. Como posso ajudá-lo a conhecer nossos produtos?",
      sampleQuestions: [
        "Qual o preço do curso?",
        "Tem desconto disponível?",
        "Como funciona a garantia?",
        "Posso ver uma amostra do conteúdo?"
      ],
      sampleResponses: [
        "O investimento é de R$ 497, mas hoje temos uma oferta especial...",
        "Sim! Temos 30% de desconto para novos alunos...",
        "Oferecemos garantia incondicional de 30 dias..."
      ]
    },
    isPremium: false
  },
  {
    id: "2",
    name: "Suporte Técnico Avançado",
    description: "Sistema completo de suporte com escalação inteligente",
    type: "SUPPORT",
    category: "Tecnologia",
    industry: "SaaS",
    difficulty: "ADVANCED",
    rating: 4.9,
    downloads: 890,
    features: [
      "Diagnóstico automático",
      "Base de conhecimento",
      "Escalação inteligente",
      "Tickets automáticos",
      "Análise de sentimentos"
    ],
    preview: {
      welcomeMessage: "Olá! Sou o assistente de suporte. Vou ajudá-lo a resolver sua questão da forma mais rápida possível.",
      sampleQuestions: [
        "Não consigo fazer login",
        "Como resetar minha senha?",
        "O sistema está lento",
        "Preciso de ajuda com integração"
      ],
      sampleResponses: [
        "Vou verificar seu acesso. Pode me informar seu email?",
        "Para resetar sua senha, acesse o link de recuperação...",
        "Estou verificando a performance do sistema..."
      ]
    },
    isPremium: true,
    price: 97
  },
  {
    id: "3",
    name: "Onboarding de Membros",
    description: "Jornada completa de onboarding para novos membros",
    type: "ONBOARDING",
    category: "Comunidade",
    industry: "Membership",
    difficulty: "INTERMEDIATE",
    rating: 4.7,
    downloads: 650,
    features: [
      "Boas-vindas personalizadas",
      "Tutorial interativo",
      "Configuração de perfil",
      "Primeiros passos",
      "Engajamento inicial"
    ],
    preview: {
      welcomeMessage: "🎉 Bem-vindo à nossa comunidade! Vou te guiar pelos primeiros passos para aproveitar ao máximo sua experiência.",
      sampleQuestions: [
        "Como começar na plataforma?",
        "Onde encontro os materiais?",
        "Como participar da comunidade?",
        "Qual a próxima etapa?"
      ],
      sampleResponses: [
        "Vamos começar configurando seu perfil...",
        "Os materiais estão organizados por módulos...",
        "A comunidade é o coração da nossa plataforma..."
      ]
    },
    isPremium: false
  },
  {
    id: "4",
    name: "Moderação de Comunidade",
    description: "Sistema inteligente para moderar grupos e comunidades",
    type: "COMMUNITY",
    category: "Comunidade",
    industry: "Social Media",
    difficulty: "ADVANCED",
    rating: 4.6,
    downloads: 420,
    features: [
      "Moderação automática",
      "Detecção de spam",
      "Análise de sentimentos",
      "Relatórios de atividade",
      "Engajamento inteligente"
    ],
    preview: {
      welcomeMessage: "👋 Olá! Sou o moderador da comunidade. Estou aqui para manter um ambiente positivo e produtivo para todos.",
      sampleQuestions: [
        "Quais são as regras do grupo?",
        "Como faço uma pergunta?",
        "Posso compartilhar meu produto?",
        "Onde encontro ajuda?"
      ],
      sampleResponses: [
        "As regras estão fixadas no topo do grupo...",
        "Para fazer perguntas, use o formato correto...",
        "Compartilhamentos comerciais são permitidos apenas...",
        "Estou aqui para ajudar! Qual sua dúvida?"
      ]
    },
    isPremium: true,
    price: 197
  }
];

const industryFilters = [
  "Todos",
  "E-learning",
  "SaaS",
  "E-commerce",
  "Membership",
  "Social Media",
  "Consultoria",
  "Outros"
];

const categoryFilters = [
  "Todos",
  "Educação",
  "Tecnologia",
  "Comunidade",
  "Vendas",
  "Suporte",
  "Marketing",
  "Outros"
];

export function AgentTemplates() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedIndustry, setSelectedIndustry] = useState("Todos");
  const [selectedCategory, setSelectedCategory] = useState("Todos");
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("Todos");
  const [showPremiumOnly, setShowPremiumOnly] = useState(false);
  const [copiedTemplate, setCopiedTemplate] = useState<string | null>(null);

  const filteredTemplates = mockTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesIndustry = selectedIndustry === "Todos" || template.industry === selectedIndustry;
    const matchesCategory = selectedCategory === "Todos" || template.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === "Todos" || template.difficulty === selectedDifficulty;
    const matchesPremium = !showPremiumOnly || template.isPremium;

    return matchesSearch && matchesIndustry && matchesCategory && matchesDifficulty && matchesPremium;
  });

  const handleUseTemplate = (template: AgentTemplate) => {
    console.log("Using template:", template);
    // Implementar uso do template
  };

  const handlePreviewTemplate = (template: AgentTemplate) => {
    console.log("Previewing template:", template);
    // Implementar preview do template
  };

  const handleCopyTemplate = async (template: AgentTemplate) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(template, null, 2));
      setCopiedTemplate(template.id);
      setTimeout(() => setCopiedTemplate(null), 2000);
    } catch (error) {
      console.error("Failed to copy template:", error);
    }
  };

  const getTypeConfig = (type: string) => {
    const configs = {
      SALES: { icon: TrendingUp, color: "bg-green-100 text-green-800", label: "Vendas" },
      SUPPORT: { icon: MessageSquare, color: "bg-blue-100 text-blue-800", label: "Suporte" },
      ONBOARDING: { icon: Users, color: "bg-purple-100 text-purple-800", label: "Onboarding" },
      COMMUNITY: { icon: Users2, color: "bg-orange-100 text-orange-800", label: "Comunidade" }
    };
    return configs[type as keyof typeof configs];
  };

  const getDifficultyConfig = (difficulty: string) => {
    const configs = {
      BEGINNER: { color: "bg-green-100 text-green-800", label: "Iniciante" },
      INTERMEDIATE: { color: "bg-yellow-100 text-yellow-800", label: "Intermediário" },
      ADVANCED: { color: "bg-red-100 text-red-800", label: "Avançado" }
    };
    return configs[difficulty as keyof typeof configs];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Templates de Agentes</h1>
        <p className="text-muted-foreground">
          Use templates pré-configurados para criar agentes rapidamente
        </p>
      </div>

      {/* Filters */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              {industryFilters.map(filter => (
                <option key={filter} value={filter}>{filter}</option>
              ))}
            </select>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              {categoryFilters.map(filter => (
                <option key={filter} value={filter}>{filter}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedDifficulty === "Todos" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedDifficulty("Todos")}
          >
            Todos
          </Button>
          <Button
            variant={selectedDifficulty === "BEGINNER" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedDifficulty("BEGINNER")}
          >
            Iniciante
          </Button>
          <Button
            variant={selectedDifficulty === "INTERMEDIATE" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedDifficulty("INTERMEDIATE")}
          >
            Intermediário
          </Button>
          <Button
            variant={selectedDifficulty === "ADVANCED" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedDifficulty("ADVANCED")}
          >
            Avançado
          </Button>
          <Button
            variant={showPremiumOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowPremiumOnly(!showPremiumOnly)}
          >
            Premium
          </Button>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => {
          const typeConfig = getTypeConfig(template.type);
          const difficultyConfig = getDifficultyConfig(template.difficulty);
          const TypeIcon = typeConfig.icon;

          return (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn("p-2 rounded-lg", typeConfig.color)}>
                      <TypeIcon className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </div>
                  </div>
                  {template.isPremium && (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      Premium
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>{template.rating}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Download className="h-4 w-4 text-muted-foreground" />
                    <span>{template.downloads}</span>
                  </div>
                  <Badge className={cn("text-xs", difficultyConfig.color)}>
                    {difficultyConfig.label}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Recursos incluídos:</p>
                  <div className="flex flex-wrap gap-1">
                    {template.features.slice(0, 3).map((feature, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {template.features.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.features.length - 3} mais
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Preview:</p>
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm italic">"{template.preview.welcomeMessage}"</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleUseTemplate(template)}
                  >
                    <Bot className="h-4 w-4 mr-2" />
                    Usar Template
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreviewTemplate(template)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCopyTemplate(template)}
                  >
                    {copiedTemplate === template.id ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {template.isPremium && (
                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Preço:</span>
                      <span className="text-lg font-bold text-primary">
                        R$ {template.price}/mês
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Nenhum template encontrado</h3>
          <p className="text-muted-foreground">
            Tente ajustar os filtros para encontrar o template ideal.
          </p>
        </div>
      )}
    </div>
  );
}
