"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Badge } from "@ui/components/badge";
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  MessageSquare, 
  Clock, 
  Star,
  Download,
  Calendar,
  Filter,
  RefreshCw
} from "lucide-react";

interface AnalyticsData {
  period: string;
  totalInteractions: number;
  totalLeads: number;
  totalConversions: number;
  avgResponseTime: number;
  satisfactionScore: number;
  escalationRate: number;
  channelBreakdown: {
    whatsapp: number;
    telegram: number;
    chat: number;
    email: number;
  };
  hourlyDistribution: Array<{
    hour: number;
    interactions: number;
  }>;
  topQuestions: Array<{
    question: string;
    count: number;
    satisfaction: number;
  }>;
  conversionFunnel: {
    visitors: number;
    interactions: number;
    leads: number;
    conversions: number;
  };
}

const mockAnalyticsData: AnalyticsData = {
  period: "last_30_days",
  totalInteractions: 2595,
  totalLeads: 234,
  totalConversions: 89,
  avgResponseTime: 12.3,
  satisfactionScore: 4.2,
  escalationRate: 8.5,
  channelBreakdown: {
    whatsapp: 1456,
    telegram: 678,
    chat: 312,
    email: 149
  },
  hourlyDistribution: [
    { hour: 0, interactions: 45 },
    { hour: 1, interactions: 32 },
    { hour: 2, interactions: 28 },
    { hour: 3, interactions: 25 },
    { hour: 4, interactions: 30 },
    { hour: 5, interactions: 35 },
    { hour: 6, interactions: 42 },
    { hour: 7, interactions: 58 },
    { hour: 8, interactions: 78 },
    { hour: 9, interactions: 95 },
    { hour: 10, interactions: 112 },
    { hour: 11, interactions: 125 },
    { hour: 12, interactions: 98 },
    { hour: 13, interactions: 105 },
    { hour: 14, interactions: 118 },
    { hour: 15, interactions: 132 },
    { hour: 16, interactions: 145 },
    { hour: 17, interactions: 128 },
    { hour: 18, interactions: 115 },
    { hour: 19, interactions: 108 },
    { hour: 20, interactions: 95 },
    { hour: 21, interactions: 82 },
    { hour: 22, interactions: 68 },
    { hour: 23, interactions: 55 }
  ],
  topQuestions: [
    { question: "Qual o preço do curso?", count: 145, satisfaction: 4.5 },
    { question: "Como funciona a garantia?", count: 98, satisfaction: 4.2 },
    { question: "Tem desconto disponível?", count: 87, satisfaction: 4.0 },
    { question: "Posso ver uma amostra?", count: 76, satisfaction: 4.3 },
    { question: "Como faço o pagamento?", count: 65, satisfaction: 4.1 }
  ],
  conversionFunnel: {
    visitors: 5000,
    interactions: 2595,
    leads: 234,
    conversions: 89
  }
};

export function AgentAnalytics() {
  const [selectedPeriod, setSelectedPeriod] = useState("last_30_days");
  const [selectedAgent, setSelectedAgent] = useState("all");
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simular refresh dos dados
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  const handleExport = () => {
    console.log("Exporting analytics data...");
    // Implementar exportação
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
    return num.toString();
  };

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <div className="h-4 w-4" />;
  };

  const getTrendColor = (current: number, previous: number) => {
    if (current > previous) return "text-green-600";
    if (current < previous) return "text-red-600";
    return "text-muted-foreground";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics de Agentes</h1>
          <p className="text-muted-foreground">
            Métricas detalhadas de performance dos seus agentes de IA
        </p>
      </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="last_7_days">Últimos 7 dias</SelectItem>
            <SelectItem value="last_30_days">Últimos 30 dias</SelectItem>
            <SelectItem value="last_90_days">Últimos 90 dias</SelectItem>
            <SelectItem value="last_year">Último ano</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedAgent} onValueChange={setSelectedAgent}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos os agentes</SelectItem>
            <SelectItem value="sales">Agente de Vendas</SelectItem>
            <SelectItem value="support">Agente de Suporte</SelectItem>
            <SelectItem value="onboarding">Agente de Onboarding</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Interações</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(mockAnalyticsData.totalInteractions)}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(mockAnalyticsData.totalInteractions, 2100)}
              <span className={getTrendColor(mockAnalyticsData.totalInteractions, 2100)}>
                +23.5% vs período anterior
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leads Gerados</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(mockAnalyticsData.totalLeads)}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(mockAnalyticsData.totalLeads, 180)}
              <span className={getTrendColor(mockAnalyticsData.totalLeads, 180)}>
                +30.0% vs período anterior
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversões</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(mockAnalyticsData.totalConversions)}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(mockAnalyticsData.totalConversions, 65)}
              <span className={getTrendColor(mockAnalyticsData.totalConversions, 65)}>
                +36.9% vs período anterior
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Satisfação</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalyticsData.satisfactionScore.toFixed(1)}/5</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(mockAnalyticsData.satisfactionScore, 3.8)}
              <span className={getTrendColor(mockAnalyticsData.satisfactionScore, 3.8)}>
                +10.5% vs período anterior
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="channels">Canais</TabsTrigger>
          <TabsTrigger value="questions">Perguntas</TabsTrigger>
          <TabsTrigger value="funnel">Funil</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance por Hora</CardTitle>
                <CardDescription>
                  Distribuição de interações ao longo do dia
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {mockAnalyticsData.hourlyDistribution.slice(8, 20).map((data) => (
                    <div key={data.hour} className="flex items-center gap-3">
                      <div className="w-8 text-sm text-muted-foreground">
                        {data.hour.toString().padStart(2, '0')}:00
                      </div>
                      <div className="flex-1 bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full"
                          style={{ width: `${(data.interactions / 145) * 100}%` }}
                        />
                      </div>
                      <div className="w-12 text-sm text-right">
                        {data.interactions}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Métricas de Performance</CardTitle>
                <CardDescription>
                  Indicadores de qualidade e eficiência
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Tempo Médio de Resposta</span>
                  <span className="font-semibold">{formatTime(mockAnalyticsData.avgResponseTime)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Taxa de Escalação</span>
                  <span className="font-semibold">{mockAnalyticsData.escalationRate}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Taxa de Conversão</span>
                  <span className="font-semibold">
                    {((mockAnalyticsData.totalConversions / mockAnalyticsData.totalLeads) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Taxa de Lead</span>
                  <span className="font-semibold">
                    {((mockAnalyticsData.totalLeads / mockAnalyticsData.totalInteractions) * 100).toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Distribuição por Canais</CardTitle>
              <CardDescription>
                Interações por canal de comunicação
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(mockAnalyticsData.channelBreakdown).map(([channel, count]) => {
                  const percentage = (count / mockAnalyticsData.totalInteractions) * 100;
                  return (
                    <div key={channel} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium capitalize">{channel}</span>
                        <span className="text-sm text-muted-foreground">
                          {count} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Perguntas Mais Frequentes</CardTitle>
              <CardDescription>
                Top perguntas e suas métricas de satisfação
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAnalyticsData.topQuestions.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{item.question}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-sm text-muted-foreground">
                          {item.count} perguntas
                        </span>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500" />
                          <span className="text-sm">{item.satisfaction.toFixed(1)}</span>
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline">
                      #{index + 1}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="funnel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Funil de Conversão</CardTitle>
              <CardDescription>
                Jornada completa do visitante até a conversão
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {[
                  { label: "Visitantes", value: mockAnalyticsData.conversionFunnel.visitors, color: "bg-blue-500" },
                  { label: "Interações", value: mockAnalyticsData.conversionFunnel.interactions, color: "bg-green-500" },
                  { label: "Leads", value: mockAnalyticsData.conversionFunnel.leads, color: "bg-yellow-500" },
                  { label: "Conversões", value: mockAnalyticsData.conversionFunnel.conversions, color: "bg-red-500" }
                ].map((step, index) => {
                  const percentage = index === 0 ? 100 : (step.value / mockAnalyticsData.conversionFunnel.visitors) * 100;
                  const conversionRate = index > 0 ? (step.value / mockAnalyticsData.conversionFunnel.visitors) * 100 : 100;
                  
                  return (
                    <div key={step.label} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{step.label}</span>
                        <div className="text-right">
                          <span className="font-semibold">{formatNumber(step.value)}</span>
                          <span className="text-sm text-muted-foreground ml-2">
                            ({conversionRate.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-muted rounded-full h-3">
                        <div 
                          className={`${step.color} h-3 rounded-full`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
