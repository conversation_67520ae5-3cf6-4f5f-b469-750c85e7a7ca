// Componentes principais
export { AiAgentsDashboard } from "./components/AiAgentsDashboard";
export { AgentConfigForm } from "./components/AgentConfigForm";
export { AgentTemplates } from "./components/AgentTemplates";
export { AgentAnalytics } from "./components/AgentAnalytics";
export { AgentChat } from "./components/AgentChat";

// Páginas
export { default as AiAgentsPage } from "./pages/AiAgentsPage";

// Tipos e interfaces
export type {
  AiAgent,
  AgentType,
  AgentStatus,
  AgentPersonality,
  IntegrationType,
  AgentIntegration,
  AgentConversation,
  AgentTemplate,
  AgentAnalytics as AgentAnalyticsType,
} from "./types";

// Utilitários
export {
  agentTypeConfig,
  personalityConfig,
  statusConfig,
  formatTime,
  formatNumber,
} from "./utils";
