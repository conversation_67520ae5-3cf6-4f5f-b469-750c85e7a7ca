import { Bo<PERSON>, <PERSON><PERSON>dingUp, MessageSquare, Users, Users2, Play, Pause, Zap } from "lucide-react";

export const agentTypeConfig = {
  SALES: {
    label: "Vendas",
    icon: TrendingUp,
    color: "bg-green-100 text-green-800",
    description: "Converte visitantes em clientes"
  },
  SUPPORT: {
    label: "Suporte",
    icon: MessageSquare,
    color: "bg-blue-100 text-blue-800",
    description: "Resolve dúvidas e problemas"
  },
  ONBOARDING: {
    label: "Onboarding",
    icon: Users,
    color: "bg-purple-100 text-purple-800",
    description: "Guia novos clientes"
  },
  COMMUNITY: {
    label: "Comunidade",
    icon: Users2,
    color: "bg-orange-100 text-orange-800",
    description: "Modera e engaja comunidades"
  }
};

export const personalityConfig = {
  FRIENDLY: {
    label: "Amigável",
    description: "Tom caloroso e acolhedor"
  },
  PROFESSIONAL: {
    label: "Profissional",
    description: "Tom formal e técnico"
  },
  ENTHUSIASTIC: {
    label: "Entusiasmado",
    description: "Tom animado e motivador"
  },
  TECHNICAL: {
    label: "Técnico",
    description: "Tom especializado e detalhado"
  },
  CASUAL: {
    label: "Casual",
    description: "Tom descontraído e informal"
  }
};

export const statusConfig = {
  ACTIVE: {
    label: "Ativo",
    color: "bg-green-100 text-green-800",
    icon: Play
  },
  INACTIVE: {
    label: "Inativo",
    color: "bg-gray-100 text-gray-800",
    icon: Pause
  },
  PAUSED: {
    label: "Pausado",
    color: "bg-yellow-100 text-yellow-800",
    icon: Pause
  },
  TRAINING: {
    label: "Treinando",
    color: "bg-blue-100 text-blue-800",
    icon: Zap
  }
};

export function formatTime(seconds: number): string {
  if (seconds < 60) return `${Math.round(seconds)}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.round(seconds % 60);
  return `${minutes}m ${remainingSeconds}s`;
}

export function formatNumber(num: number): string {
  if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
  return num.toString();
}

export function formatCurrency(amount: number, currency: string = "BRL"): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: currency,
  }).format(amount / 100);
}

export function getTrendIcon(current: number, previous: number) {
  if (current > previous) return "↗️";
  if (current < previous) return "↘️";
  return "→";
}

export function getTrendColor(current: number, previous: number): string {
  if (current > previous) return "text-green-600";
  if (current < previous) return "text-red-600";
  return "text-muted-foreground";
}

export function calculateConversionRate(conversions: number, leads: number): number {
  if (leads === 0) return 0;
  return Math.round((conversions / leads) * 100);
}

export function calculateLeadRate(leads: number, interactions: number): number {
  if (interactions === 0) return 0;
  return Math.round((leads / interactions) * 100);
}

export function getAgentStatusColor(status: string): string {
  const config = statusConfig[status as keyof typeof statusConfig];
  return config?.color || "bg-gray-100 text-gray-800";
}

export function getAgentTypeColor(type: string): string {
  const config = agentTypeConfig[type as keyof typeof agentTypeConfig];
  return config?.color || "bg-gray-100 text-gray-800";
}

export function getPersonalityLabel(personality: string): string {
  const config = personalityConfig[personality as keyof typeof personalityConfig];
  return config?.label || personality;
}

export function getPersonalityDescription(personality: string): string {
  const config = personalityConfig[personality as keyof typeof personalityConfig];
  return config?.description || "";
}

export function generateAgentId(): string {
  return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function validateAgentName(name: string): { valid: boolean; error?: string } {
  if (!name || name.trim().length === 0) {
    return { valid: false, error: "Nome é obrigatório" };
  }
  
  if (name.length < 3) {
    return { valid: false, error: "Nome deve ter pelo menos 3 caracteres" };
  }
  
  if (name.length > 100) {
    return { valid: false, error: "Nome deve ter no máximo 100 caracteres" };
  }
  
  return { valid: true };
}

export function validateSystemPrompt(prompt: string): { valid: boolean; error?: string } {
  if (prompt && prompt.length > 2000) {
    return { valid: false, error: "Prompt do sistema deve ter no máximo 2000 caracteres" };
  }
  
  return { valid: true };
}

export function validateWelcomeMessage(message: string): { valid: boolean; error?: string } {
  if (message && message.length > 500) {
    return { valid: false, error: "Mensagem de boas-vindas deve ter no máximo 500 caracteres" };
  }
  
  return { valid: true };
}

export function validateFallbackMessage(message: string): { valid: boolean; error?: string } {
  if (message && message.length > 500) {
    return { valid: false, error: "Mensagem de fallback deve ter no máximo 500 caracteres" };
  }
  
  return { valid: true };
}

export function validateConversionGoal(goal: number): { valid: boolean; error?: string } {
  if (goal < 0 || goal > 1) {
    return { valid: false, error: "Meta de conversão deve estar entre 0 e 1" };
  }
  
  return { valid: true };
}

export function validateLeadGoal(goal: number): { valid: boolean; error?: string } {
  if (goal < 0) {
    return { valid: false, error: "Meta de leads deve ser maior ou igual a 0" };
  }
  
  return { valid: true };
}

export function validateResponseTimeGoal(goal: number): { valid: boolean; error?: string } {
  if (goal < 0) {
    return { valid: false, error: "Meta de tempo de resposta deve ser maior ou igual a 0" };
  }
  
  return { valid: true };
}

export function validateMaxInteractions(max: number): { valid: boolean; error?: string } {
  if (max < 1) {
    return { valid: false, error: "Máximo de interações deve ser maior que 0" };
  }
  
  return { valid: true };
}

export function getDefaultSystemPrompt(type: string): string {
  const prompts = {
    SALES: `Você é um agente de vendas especializado em converter leads em clientes. 
    Seja persuasivo, mas não agressivo. Foque nos benefícios e na solução de problemas.
    Sempre pergunte sobre as necessidades do cliente antes de apresentar soluções.`,
    
    SUPPORT: `Você é um agente de suporte técnico. Seja prestativo e eficiente.
    Tente resolver problemas rapidamente. Se não conseguir resolver, escalar para um humano.
    Sempre confirme se o problema foi resolvido.`,
    
    ONBOARDING: `Você é um agente de onboarding que ajuda novos clientes.
    Seja acolhedor e guie-os pelos primeiros passos.
    Explique claramente como usar a plataforma e seus benefícios.`,
    
    COMMUNITY: `Você é um moderador de comunidade. Mantenha um ambiente positivo.
    Seja respeitoso e incentive a participação.
    Remova conteúdo inadequado e incentive discussões produtivas.`
  };
  
  return prompts[type as keyof typeof prompts] || "Você é um assistente de IA útil e prestativo.";
}

export function getDefaultWelcomeMessage(type: string): string {
  const messages = {
    SALES: "Olá! 👋 Sou seu assistente de vendas. Como posso ajudá-lo a encontrar a solução ideal para suas necessidades?",
    SUPPORT: "Olá! Sou seu assistente de suporte. Estou aqui para ajudá-lo com qualquer dúvida ou problema. Como posso ajudá-lo hoje?",
    ONBOARDING: "Bem-vindo! 🎉 Estou aqui para guiá-lo pelos primeiros passos e ajudá-lo a aproveitar ao máximo nossa plataforma.",
    COMMUNITY: "Olá! 👋 Bem-vindo à nossa comunidade! Estou aqui para moderar e ajudar a manter um ambiente positivo e produtivo."
  };
  
  return messages[type as keyof typeof messages] || "Olá! Como posso ajudá-lo hoje?";
}

export function getDefaultFallbackMessage(type: string): string {
  const messages = {
    SALES: "Desculpe, não entendi sua pergunta. Pode reformular ou me contar mais sobre o que você está procurando?",
    SUPPORT: "Não consegui entender sua solicitação. Pode me dar mais detalhes sobre o problema que está enfrentando?",
    ONBOARDING: "Não entendi sua pergunta. Pode me explicar melhor o que você gostaria de saber?",
    COMMUNITY: "Não entendi sua mensagem. Pode reformular sua pergunta ou comentário?"
  };
  
  return messages[type as keyof typeof messages] || "Desculpe, não entendi. Pode reformular sua pergunta?";
}

export function getAgentTypeIcon(type: string) {
  const config = agentTypeConfig[type as keyof typeof agentTypeConfig];
  return config?.icon || Bot;
}

export function getStatusIcon(status: string) {
  const config = statusConfig[status as keyof typeof statusConfig];
  return config?.icon || Pause;
}

export function getPersonalityIcon(personality: string) {
  // Mapear personalidades para ícones
  const icons = {
    FRIENDLY: "😊",
    PROFESSIONAL: "👔",
    ENTHUSIASTIC: "🚀",
    TECHNICAL: "🔧",
    CASUAL: "😎"
  };
  
  return icons[personality as keyof typeof icons] || "🤖";
}

export function getChannelIcon(channel: string) {
  const icons = {
    whatsapp: "📱",
    telegram: "✈️",
    chat: "💬",
    email: "📧",
    crm: "📊"
  };
  
  return icons[channel as keyof typeof icons] || "📞";
}

export function getChannelColor(channel: string): string {
  const colors = {
    whatsapp: "bg-green-100 text-green-800",
    telegram: "bg-blue-100 text-blue-800",
    chat: "bg-purple-100 text-purple-800",
    email: "bg-gray-100 text-gray-800",
    crm: "bg-orange-100 text-orange-800"
  };
  
  return colors[channel as keyof typeof colors] || "bg-gray-100 text-gray-800";
}

export function formatChannelName(channel: string): string {
  const names = {
    whatsapp: "WhatsApp",
    telegram: "Telegram",
    chat: "Chat",
    email: "Email",
    crm: "CRM"
  };
  
  return names[channel as keyof typeof names] || channel;
}

export function getSatisfactionColor(score: number): string {
  if (score >= 4.5) return "text-green-600";
  if (score >= 3.5) return "text-yellow-600";
  if (score >= 2.5) return "text-orange-600";
  return "text-red-600";
}

export function getSatisfactionLabel(score: number): string {
  if (score >= 4.5) return "Excelente";
  if (score >= 3.5) return "Bom";
  if (score >= 2.5) return "Regular";
  if (score >= 1.5) return "Ruim";
  return "Muito Ruim";
}

export function getResponseTimeColor(time: number): string {
  if (time <= 5) return "text-green-600";
  if (time <= 15) return "text-yellow-600";
  if (time <= 30) return "text-orange-600";
  return "text-red-600";
}

export function getResponseTimeLabel(time: number): string {
  if (time <= 5) return "Muito Rápido";
  if (time <= 15) return "Rápido";
  if (time <= 30) return "Normal";
  if (time <= 60) return "Lento";
  return "Muito Lento";
}

export function calculateAgentScore(agent: {
  totalInteractions: number;
  totalLeads: number;
  totalConversions: number;
  avgResponseTime: number;
  satisfactionScore: number;
}): number {
  const { totalInteractions, totalLeads, totalConversions, avgResponseTime, satisfactionScore } = agent;
  
  // Fatores de pontuação
  const interactionScore = Math.min(totalInteractions / 1000, 1) * 20;
  const leadScore = Math.min(totalLeads / 100, 1) * 20;
  const conversionScore = Math.min(totalConversions / 50, 1) * 20;
  const responseScore = avgResponseTime <= 15 ? 20 : Math.max(20 - (avgResponseTime - 15) * 2, 0);
  const satisfactionScoreValue = (satisfactionScore / 5) * 20;
  
  return Math.round(interactionScore + leadScore + conversionScore + responseScore + satisfactionScoreValue);
}

export function getAgentScoreLabel(score: number): string {
  if (score >= 90) return "Excelente";
  if (score >= 80) return "Muito Bom";
  if (score >= 70) return "Bom";
  if (score >= 60) return "Regular";
  if (score >= 50) return "Ruim";
  return "Muito Ruim";
}

export function getAgentScoreColor(score: number): string {
  if (score >= 90) return "text-green-600";
  if (score >= 80) return "text-blue-600";
  if (score >= 70) return "text-yellow-600";
  if (score >= 60) return "text-orange-600";
  return "text-red-600";
}
