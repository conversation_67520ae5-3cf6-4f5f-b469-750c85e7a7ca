import { cn } from "@ui/lib";

export function Logo({
	withLabel = true,
	className,
}: {
	className?: string;
	withLabel?: boolean;
}) {
	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>
			<svg
				className="size-8"
				viewBox="0 0 96 96"
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
			>
				<title>SupGateway</title>
				<g clipPath="url(#clip0_2_59)">
					<path
						d="M80.9376 87.7942C90.5823 78.1493 96.0008 65.0683 96.0008 51.4286C96.0008 37.789 90.5823 24.7078 80.9376 15.0631C71.293 5.41836 58.2118 2.45251e-06 44.5721 0C30.9324 -2.45251e-06 17.8514 5.41834 8.20667 15.0631L25.1772 32.0335C27.8552 34.7114 32.1795 34.5778 35.5863 32.9234C38.3607 31.5763 41.4293 30.857 44.5721 30.857C50.028 30.857 55.2605 33.0245 59.1183 36.8825C62.9763 40.7402 65.1435 45.9727 65.1435 51.4286C65.1435 54.5714 64.4244 57.6401 63.0773 60.4145C61.423 63.8213 61.2893 68.1456 63.967 70.8235L80.9376 87.7942Z"
						fill="#297AFF"
					/>
					<path
						d="M72 96H46.8235C43.1863 96 39.6979 94.5552 37.1261 91.9831L4.01683 58.8739C1.4449 56.3021 0 52.8137 0 49.1765V24L72 96Z"
						fill="#34C2FF"
					/>
					<path
						d="M25.7143 95.9998H10.2857C4.60507 95.9998 0 91.3949 0 85.7141V70.2855L25.7143 95.9998Z"
						fill="#34C2FF"
					/>
				</g>
				<defs>
					<clipPath id="clip0_2_59">
						<rect width="96" height="96" fill="white" />
					</clipPath>
				</defs>
			</svg>
			{withLabel && (
				<span className="ml-2 hidden text-lg md:block">SupGateway</span>
			)}
		</span>
	);
}
