"use client";

import { formatCurrencyFromCents, formatCurrencyCompactFromCents } from "@lib/utils";
import { useLocaleCurrency } from "../../hooks/use-locale-currency";
import { cn } from "@ui/lib";

interface AmountLabelProps {
  amountInCents: number;
  currency?: string;
  className?: string;
  showCurrency?: boolean;
  compact?: boolean;
}

export function AmountLabel({
  amountInCents,
  currency,
  className,
  showCurrency = true,
  compact = false,
}: AmountLabelProps) {
  const localeCurrency = useLocaleCurrency();
  const displayCurrency = currency || localeCurrency;

  const formattedAmount = compact
    ? formatCurrencyCompactFromCents(amountInCents, displayCurrency)
    : formatCurrencyFromCents(amountInCents, displayCurrency);

  return (
    <span className={cn("font-medium", className)}>
      {showCurrency ? formattedAmount : formattedAmount.replace(/[^\d.,\s-]/g, "").trim()}
    </span>
  );
}

export function AmountDisplay({
  amountInCents,
  currency,
  className,
  showCurrency = true,
}: Omit<AmountLabelProps, "compact">) {
  return (
    <AmountLabel
      amountInCents={amountInCents}
      currency={currency}
      className={className}
      showCurrency={showCurrency}
      compact={false}
    />
  );
}

export function CompactAmountDisplay({
  amountInCents,
  currency,
  className,
  showCurrency = true,
}: Omit<AmountLabelProps, "compact">) {
  return (
    <AmountLabel
      amountInCents={amountInCents}
      currency={currency}
      className={className}
      showCurrency={showCurrency}
      compact={true}
    />
  );
}