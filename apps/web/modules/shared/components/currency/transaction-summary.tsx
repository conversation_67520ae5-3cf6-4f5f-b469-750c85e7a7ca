"use client";

import { formatCurrencyFromCents } from "@lib/utils";
import { useLocaleCurrency } from "../../hooks/use-locale-currency";
import { cn } from "@ui/lib";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";

interface TransactionItem {
  label: string;
  amountInCents: number;
  currency?: string;
  type?: "positive" | "negative" | "neutral";
}

interface TransactionSummaryProps {
  items: TransactionItem[];
  totalAmountInCents?: number;
  totalLabel?: string;
  currency?: string;
  className?: string;
  showTotal?: boolean;
}

const typeClasses = {
  positive: "text-green-600",
  negative: "text-red-600",
  neutral: "text-gray-900",
};

export function TransactionSummary({
  items,
  totalAmountInCents,
  totalLabel = "Total",
  currency,
  className,
  showTotal = true,
}: TransactionSummaryProps) {
  const localeCurrency = useLocaleCurrency();
  const displayCurrency = currency || localeCurrency;

  const calculatedTotal = totalAmountInCents ?? items.reduce((sum, item) => sum + item.amountInCents, 0);
  const formattedTotal = formatCurrencyFromCents(calculatedTotal, displayCurrency);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="text-lg">Resumo da Transação</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {items.map((item, index) => {
          const itemCurrency = item.currency || displayCurrency;
          const formattedAmount = formatCurrencyFromCents(item.amountInCents, itemCurrency);
          const typeClass = typeClasses[item.type || "neutral"];

          return (
            <div key={index} className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{item.label}</span>
              <span className={cn("font-medium", typeClass)}>
                {formattedAmount}
              </span>
            </div>
          );
        })}
        
        {showTotal && (
          <>
            <hr className="my-3" />
            <div className="flex justify-between items-center">
              <span className="font-semibold">{totalLabel}</span>
              <span className="font-bold text-lg">
                {formattedTotal}
              </span>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

export function InlineTransactionSummary({
  items,
  totalAmountInCents,
  totalLabel = "Total",
  currency,
  className,
  showTotal = true,
}: TransactionSummaryProps) {
  const localeCurrency = useLocaleCurrency();
  const displayCurrency = currency || localeCurrency;

  const calculatedTotal = totalAmountInCents ?? items.reduce((sum, item) => sum + item.amountInCents, 0);
  const formattedTotal = formatCurrencyFromCents(calculatedTotal, displayCurrency);

  return (
    <div className={cn("space-y-2", className)}>
      {items.map((item, index) => {
        const itemCurrency = item.currency || displayCurrency;
        const formattedAmount = formatCurrencyFromCents(item.amountInCents, itemCurrency);
        const typeClass = typeClasses[item.type || "neutral"];

        return (
          <div key={index} className="flex justify-between items-center text-sm">
            <span className="text-gray-600">{item.label}</span>
            <span className={cn("font-medium", typeClass)}>
              {formattedAmount}
            </span>
          </div>
        );
      })}
      
      {showTotal && (
        <div className="flex justify-between items-center pt-2 border-t">
          <span className="font-semibold">{totalLabel}</span>
          <span className="font-bold">
            {formattedTotal}
          </span>
        </div>
      )}
    </div>
  );
}