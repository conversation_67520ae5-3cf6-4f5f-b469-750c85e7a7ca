'use client';

import { motion } from 'framer-motion';
import {
  ShieldCheckIcon,
  ClockIcon,
  TrendingUpIcon,
  GlobeIcon,
  CreditCardIcon,
  CheckCircleIcon,
  ZapIcon,
  BarChart3Icon
} from 'lucide-react';

const benefits = [
  {
    icon: ZapIcon,
    title: 'Integração em Minutos',
    description: 'API REST simples e SDKs prontos para as principais linguagens. Comece a aceitar pagamentos em menos de 30 minutos.',
    stats: '< 30min',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Segurança PCI DSS',
    description: 'Certificação PCI DSS Level 1 e compliance total com LGPD. Seus dados e dos seus clientes sempre protegidos.',
    stats: '99.9%',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: TrendingUpIcon,
    title: 'Conversão Otimizada',
    description: 'Checkout otimizado para o mercado brasileiro com as melhores práticas de UX para maximizar conversões.',
    stats: '+35%',
    color: 'from-purple-500 to-purple-600'
  },
  {
    icon: ClockIcon,
    title: 'Suporte 24/7',
    description: 'Equipe técnica especializada disponível 24 horas por dia, 7 dias por semana, incluindo feriados.',
    stats: '24/7',
    color: 'from-orange-500 to-orange-600'
  },
  {
    icon: BarChart3Icon,
    title: 'Analytics Avançado',
    description: 'Dashboard completo com métricas em tempo real, relatórios detalhados e insights para otimizar suas vendas.',
    stats: 'Real-time',
    color: 'from-indigo-500 to-indigo-600'
  },
  {
    icon: GlobeIcon,
    title: 'Multi-tenant',
    description: 'Arquitetura white-label que permite personalização completa da marca e experiência do usuário.',
    stats: '100%',
    color: 'from-red-500 to-red-600'
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6
    }
  }
};

export function Benefits() {
  return (
    <section className="py-24 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
              <CheckCircleIcon className="w-4 h-4" />
              Por que escolher nosso gateway
            </div>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent"
            variants={itemVariants}
          >
            Diferenciais que
            <span className="block bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent">
              fazem a diferença
            </span>
          </motion.h2>

          <motion.p
            className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            Desenvolvido especificamente para o mercado brasileiro, com foco em performance,
            segurança e experiência do usuário.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <motion.div
                key={benefit.title}
                className="group relative p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                variants={itemVariants}
                whileHover={{ y: -5 }}
              >
                <div className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-5 transition-opacity duration-300 rounded-2xl" />

                <div className="relative z-10">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${benefit.color} shadow-lg mb-6`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                      {benefit.title}
                    </h3>
                    <div className={`px-3 py-1 rounded-full bg-gradient-to-r ${benefit.color} text-white text-sm font-bold`}>
                      {benefit.stats}
                    </div>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

      </div>
    </section>
  );
}
