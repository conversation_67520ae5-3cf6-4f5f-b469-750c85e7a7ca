"use client";
import type { OrganizationMemberRole } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { useSession } from "@saas/auth/hooks/use-session";
import { useOrganizationMemberRoles } from "@saas/organizations/hooks/member-roles";
import {
	fullOrganizationQueryKey,
	useFullOrganizationQuery,
} from "@saas/organizations/lib/api";
import { UserAvatar } from "@shared/components/UserAvatar";
import { useQueryClient } from "@tanstack/react-query";
import type {
	ColumnDef,
	ColumnFiltersState,
	SortingState,
} from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Table, TableBody, TableCell, TableRow } from "@ui/components/table";
import { LogOutIcon, MoreVerticalIcon, TrashIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";
import { OrganizationRoleSelect } from "./OrganizationRoleSelect";
import { ConfirmAdminRoleModal } from "./ConfirmAdminRoleModal";

export function OrganizationMembersList({
	organizationId,
}: {
	organizationId: string;
}) {
	const t = useTranslations();
	const queryClient = useQueryClient();
	const { user } = useSession();
	const { data: organization } = useFullOrganizationQuery(organizationId);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
	const [confirmAdminModal, setConfirmAdminModal] = useState<{
		open: boolean;
		memberId: string;
		memberName: string;
		memberEmail: string;
	}>({
		open: false,
		memberId: "",
		memberName: "",
		memberEmail: "",
	});
	const [isUpdatingRole, setIsUpdatingRole] = useState(false);
	const memberRoles = useOrganizationMemberRoles();

	const userIsOrganizationAdmin = isOrganizationAdmin(organization, user);

	// Filter out customers from members list
	const filteredMembers = organization?.members?.filter(member => {
		// Only show members who are not customers
		// Customers typically have role "CUSTOMER" or are identified by other criteria
		return member.role !== "CUSTOMER" && member.user?.role !== "CUSTOMER";
	}) || [];

	const updateMemberRole = async (
		memberId: string,
		role: OrganizationMemberRole,
		memberName: string,
		memberEmail: string,
	) => {
		// If changing to ADMIN role, show confirmation modal
		if (role === "ADMIN") {
			setConfirmAdminModal({
				open: true,
				memberId,
				memberName,
				memberEmail,
			});
			return;
		}

		// For other roles, update directly
		await performRoleUpdate(memberId, role);
	};

	const performRoleUpdate = async (memberId: string, role: OrganizationMemberRole) => {
		setIsUpdatingRole(true);
		try {
			await authClient.organization.updateMemberRole({
				memberId,
				role,
				organizationId,
			});

			queryClient.invalidateQueries({
				queryKey: fullOrganizationQueryKey(organizationId),
			});

			toast.success(
				t("organizations.settings.members.notifications.updateMembership.success.description")
			);
		} catch (error) {
			toast.error(
				t("organizations.settings.members.notifications.updateMembership.error.description")
			);
		} finally {
			setIsUpdatingRole(false);
		}
	};

	const handleConfirmAdminRole = async () => {
		await performRoleUpdate(confirmAdminModal.memberId, "ADMIN");
		setConfirmAdminModal({
			open: false,
			memberId: "",
			memberName: "",
			memberEmail: "",
		});
	};

	const removeMember = async (memberId: string) => {
		toast.promise(
			async () => {
				await authClient.organization.removeMember({
					memberIdOrEmail: memberId,
					organizationId,
				});
			},
			{
				loading: t(
					"organizations.settings.members.notifications.removeMember.loading.description",
				),
				success: () => {
					queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});

					return t(
						"organizations.settings.members.notifications.removeMember.success.description",
					);
				},
				error: t(
					"organizations.settings.members.notifications.removeMember.error.description",
				),
			},
		);
	};

	const columns: ColumnDef<
		NonNullable<typeof organization>["members"][number]
	>[] = [
		{
			accessorKey: "user",
			header: "",
			accessorFn: (row) => row.user,
			cell: ({ row }) =>
				row.original.user ? (
					<div className="flex items-center gap-2">
						<UserAvatar
							name={
								row.original.user.name ??
								row.original.user.email
							}
							avatarUrl={row.original.user?.image}
						/>
						<div>
							<strong className="block">
								{row.original.user.name}
							</strong>
							<small className="text-foreground/60">
								{row.original.user.email}
							</small>
						</div>
					</div>
				) : null,
		},
		{
			accessorKey: "actions",
			header: "",
			cell: ({ row }) => {
				return (
					<div className="flex flex-row justify-end gap-2">
						{userIsOrganizationAdmin ? (
							<>
								<OrganizationRoleSelect
									value={row.original.role}
									onSelect={async (value) =>
										updateMemberRole(
											row.original.id,
											value,
											row.original.user?.name || row.original.user?.email || "Usuário",
											row.original.user?.email || ""
										)
									}
									disabled={
										!userIsOrganizationAdmin ||
										row.original.role === "owner" ||
										isUpdatingRole
									}
								/>
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button size="icon" variant="ghost">
											<MoreVerticalIcon className="size-4" />
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent>
										{row.original.userId !== user?.id && (
											<DropdownMenuItem
												disabled={
													!isOrganizationAdmin(
														organization,
														user,
													)
												}
												className="text-destructive"
												onClick={async () =>
													removeMember(
														row.original.id,
													)
												}
											>
												<TrashIcon className="mr-2 size-4" />
												{t(
													"organizations.settings.members.removeMember",
												)}
											</DropdownMenuItem>
										)}
										{row.original.userId === user?.id && (
											<DropdownMenuItem
												className="text-destructive"
												onClick={async () =>
													removeMember(
														row.original.id,
													)
												}
											>
												<LogOutIcon className="mr-2 size-4" />
												{t(
													"organizations.settings.members.leaveOrganization",
												)}
											</DropdownMenuItem>
										)}
									</DropdownMenuContent>
								</DropdownMenu>
							</>
						) : (
							<span className="font-medium text-foreground/60 text-sm">
								{
									memberRoles[
										row.original
											.role as keyof typeof memberRoles
									]
								}
							</span>
						)}
					</div>
				);
			},
		},
	];

	const table = useReactTable({
		data: filteredMembers,
		columns,
		manualPagination: true,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		state: {
			sorting,
			columnFilters,
		},
	});

	return (
		<>
			<div className="rounded-md border">
				<Table>
					<TableBody>
						{table.getRowModel().rows?.length ? (
							table.getRowModel().rows.map((row) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
								>
									{row.getVisibleCells().map((cell) => (
										<TableCell key={cell.id}>
											{flexRender(
												cell.column.columnDef.cell,
												cell.getContext(),
											)}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell
									colSpan={columns.length}
									className="h-24 text-center"
								>
									No results.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>

			{/* Confirmation Modal for Admin Role */}
			<ConfirmAdminRoleModal
				open={confirmAdminModal.open}
				onOpenChange={(open) => setConfirmAdminModal(prev => ({ ...prev, open }))}
				onConfirm={handleConfirmAdminRole}
				memberName={confirmAdminModal.memberName}
				memberEmail={confirmAdminModal.memberEmail}
				isUpdating={isUpdatingRole}
			/>
		</>
	);
}
