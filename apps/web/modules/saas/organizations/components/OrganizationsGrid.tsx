"use client";

import { config } from "@repo/config";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useOrganizationListQuery } from "@saas/organizations/lib/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
	ChevronRightIcon,
	Building2Icon
} from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";

export function OrganizationsGrid() {
	const t = useTranslations();
	const { setActiveOrganization } = useActiveOrganization();
	const { data: allOrganizations, isLoading } = useOrganizationListQuery();

	if (isLoading) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{[1, 2, 3].map((i) => (
					<Card key={i} className="animate-pulse">
						<CardContent className="p-6">
							<div className="flex items-center space-x-4">
								<div className="rounded-full bg-muted h-12 w-12"></div>
								<div className="space-y-2 flex-1">
									<div className="h-4 bg-muted rounded w-3/4"></div>
									<div className="h-3 bg-muted rounded w-1/2"></div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		);
	}

	if (!allOrganizations || allOrganizations.length === 0) {
		return (
			<div className="text-center py-12">
				<div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
					<Building2Icon className="h-12 w-12 text-muted-foreground" />
				</div>
				<h3 className="text-lg font-semibold mb-2">Nenhuma empresa encontrada</h3>
				<p className="text-muted-foreground mb-6">
					Comece criando sua primeira empresa para gerenciar seu negócio.
				</p>
				{config.organizations.enableUsersToCreateOrganizations && (
					<Button asChild>
						<Link href="/app/new-organization">
							<PlusIcon className="h-4 w-4 mr-2" />
							Criar Primeira Empresa
						</Link>
					</Button>
				)}
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{allOrganizations.map((organization) => (
				<Card
					key={organization.id}
					className="group hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer"
					onClick={() => setActiveOrganization(organization.slug)}
				>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-4">
								<OrganizationLogo
									name={organization.name}
									logoUrl={organization.logo}
									className="size-12 rounded-lg"
								/>
								<div className="flex-1 min-w-0">
									<CardTitle className="text-lg font-semibold truncate">
										{organization.name}
									</CardTitle>
								</div>
							</div>
							<div className="flex items-center gap-3">
								<Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
									Ativo
								</Badge>
								<ChevronRightIcon className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
							</div>
						</div>
					</CardContent>
				</Card>
			))}

		</div>
	);
}
