"use client";

import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import {
  Users2Icon,
  ClockIcon,
  UserPlusIcon,
  CheckCircleIcon,
  TrendingUpIcon,
  UserCheckIcon,
} from "lucide-react";
import { useOrganizationMembers } from "../hooks/use-organization-members";
import { useTranslations } from "next-intl";

export function EnhancedMembersMetrics() {
  const t = useTranslations();
  const {
    totalMembers,
    totalPendingInvitations,
    members,
    pendingInvitations,
    isLoading
  } = useOrganizationMembers();

  // Calcular métricas derivadas
  const totalInvitations = members.length + pendingInvitations.length;
  const acceptanceRate = totalInvitations > 0
    ? Math.round((members.length / totalInvitations) * 100)
    : 0;

  const recentMembers = members.filter(member => {
    const memberDate = new Date(member.createdAt);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return memberDate >= thirtyDaysAgo;
  }).length;

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value}%`;
  };

  if (isLoading) {
    return (
      <MetricGrid columns={4}>
        {[1, 2, 3, 4].map((i) => (
          <MetricCard
            key={i}
            title={t("common.loading")}
            value="..."
            change="..."
            icon={Users2Icon}
            isLoading
          />
        ))}
      </MetricGrid>
    );
  }

  const metricsData = [
    {
      title: t("organizations.members.stats.activeMembers"),
      value: totalMembers.toLocaleString('pt-BR'),
      change: `${recentMembers} novos últimos 30 dias`,
      isPositive: recentMembers > 0,
      icon: Users2Icon,
      description: t("organizations.members.stats.activeMembers"),
      badge: {
        text: t("organizations.members.badge.members"),
        variant: "default" as const,
      },
      className: "bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20",
    },
    {
      title: t("organizations.members.stats.pendingInvitations"),
      value: totalPendingInvitations.toLocaleString('pt-BR'),
      change: totalPendingInvitations > 0 ? "aguardando resposta" : "nenhum pendente",
      isPositive: totalPendingInvitations === 0,
      icon: ClockIcon,
      description: t("organizations.members.stats.pendingInvitations"),
      badge: {
        text: t("organizations.members.badge.pending"),
        variant: totalPendingInvitations > 0 ? "outline" as const : "secondary" as const,
      },
      className: "bg-gradient-to-br from-amber-50/50 to-transparent dark:from-amber-950/20",
    },
    {
      title: t("organizations.members.stats.totalInvitations"),
      value: totalInvitations.toLocaleString('pt-BR'),
      change: t("organizations.members.stats.allTime"),
      isPositive: true,
      icon: UserPlusIcon,
      description: t("organizations.members.stats.totalInvitations"),
      badge: {
        text: "Total",
        variant: "outline" as const,
      },
      className: "bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20",
    },
    {
      title: t("organizations.members.stats.acceptanceRate"),
      value: `${acceptanceRate}%`,
      change: acceptanceRate >= 80 ? "excelente taxa" : acceptanceRate >= 60 ? "boa taxa" : "pode melhorar",
      isPositive: acceptanceRate >= 60,
      icon: CheckCircleIcon,
      description: t("organizations.members.stats.invitationsAccepted"),
      badge: {
        text: acceptanceRate >= 80 ? "Excelente" : acceptanceRate >= 60 ? "Bom" : "Melhorar",
        variant: acceptanceRate >= 80 ? "default" as const : acceptanceRate >= 60 ? "secondary" as const : "outline" as const,
      },
      className: "bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20",
    },
  ];

  return (
    <MetricGrid columns={4}>
      {metricsData.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          change={metric.change}
          isPositive={metric.isPositive}
          icon={metric.icon}
          description={metric.description}
          badge={metric.badge}
          className={metric.className}
        />
      ))}
    </MetricGrid>
  );
}
