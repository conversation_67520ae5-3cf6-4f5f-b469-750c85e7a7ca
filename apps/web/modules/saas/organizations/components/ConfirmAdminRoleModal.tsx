"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Loader2, ShieldIcon, AlertTriangleIcon } from "lucide-react";

interface ConfirmAdminRoleModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  memberName: string;
  memberEmail: string;
  isUpdating?: boolean;
}

export function ConfirmAdminRoleModal({
  open,
  onOpenChange,
  onConfirm,
  memberName,
  memberEmail,
  isUpdating = false,
}: ConfirmAdminRoleModalProps) {
  const t = useTranslations();
  const [isConfirming, setIsConfirming] = useState(false);

  const handleConfirm = async () => {
    setIsConfirming(true);
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
    } finally {
      setIsConfirming(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-[500px]">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/20">
              <AlertTriangleIcon className="h-5 w-5 text-amber-600 dark:text-amber-400" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">
                Confirmar Elevação para Administrador
              </AlertDialogTitle>
            </div>
          </div>
        </AlertDialogHeader>

        <AlertDialogDescription className="space-y-3 text-left">
          <p>
            Você está prestes a elevar <strong>{memberName}</strong> ({memberEmail})
            para o cargo de <strong>Administrador</strong> da organização.
          </p>

          <div className="rounded-lg bg-amber-50 dark:bg-amber-900/10 p-4 space-y-2">
            <div className="flex items-start gap-2">
              <ShieldIcon className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-amber-800 dark:text-amber-200 mb-1">
                  Permissões de Administrador:
                </p>
                <ul className="text-amber-700 dark:text-amber-300 space-y-1 text-xs">
                  <li>• Gerenciar membros da organização</li>
                  <li>• Convidar e remover membros</li>
                  <li>• Alterar configurações da organização</li>
                  <li>• Acessar dados financeiros</li>
                  <li>• Configurar integrações</li>
                </ul>
              </div>
            </div>
          </div>

          <p className="text-sm font-medium text-red-600 dark:text-red-400">
            ⚠️ Esta ação não pode ser desfeita facilmente.
            Certifique-se de que confia nesta pessoa antes de continuar.
          </p>
        </AlertDialogDescription>

        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel asChild>
            <Button variant="outline" disabled={isConfirming || isUpdating}>
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              disabled={isConfirming || isUpdating}
              className="bg-red-600 hover:bg-red-700"
            >
              {isConfirming || isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Elevando...
                </>
              ) : (
                <>
                  <ShieldIcon className="mr-2 h-4 w-4" />
                  Sim, Elevar para Admin
                </>
              )}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
