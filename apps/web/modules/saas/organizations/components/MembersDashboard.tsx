"use client";

import { useState } from "react";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useFullOrganizationQuery } from "@saas/organizations/lib/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Button } from "@ui/components/button";
import { Users2Icon, UserPlusIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { InviteMemberModal } from "./InviteMemberModal";
import { OrganizationMembersList } from "./OrganizationMembersList";
import { OrganizationInvitationsList } from "./OrganizationInvitationsList";
import { EnhancedMembersMetrics } from "./EnhancedMembersMetrics";

interface MembersDashboardProps {
	organizationId: string;
}

export function MembersDashboard({ organizationId }: MembersDashboardProps) {
	const t = useTranslations();
	const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
	const { data: organization } = useFullOrganizationQuery(organizationId);

	if (!organization) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<Users2Icon className="mx-auto h-12 w-12 text-muted-foreground" />
					<h3 className="mt-2 text-sm font-semibold text-foreground">
						{t("common.loading")}
					</h3>
				</div>
			</div>
		);
	}

	const activeMembers = organization.members || [];
	const pendingInvitations = organization.invitations?.filter(
		(invitation) => invitation.status === "pending"
	) || [];

	return (
		<div className="space-y-6">
			{/* Enhanced Stats Cards */}
			<EnhancedMembersMetrics />

			{/* Members and Invitations Tabs */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between">
					<div>
						<CardTitle>{t("organizations.settings.members.title")}</CardTitle>
						<CardDescription>
							{t("organizations.settings.members.description")}
						</CardDescription>
					</div>
					<Button onClick={() => setIsInviteModalOpen(true)}>
						<UserPlusIcon className="mr-2 h-4 w-4" />
						{t("organizations.settings.members.inviteMember.submit")}
					</Button>
				</CardHeader>
				<CardContent>
					<Tabs defaultValue="members" className="w-full">
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="members">
								{t("organizations.settings.members.activeMembers")} ({activeMembers.length})
							</TabsTrigger>
							<TabsTrigger value="invitations">
								{t("organizations.settings.members.pendingInvitations")} ({pendingInvitations.length})
							</TabsTrigger>
						</TabsList>
						<TabsContent value="members" className="mt-6">
							<OrganizationMembersList organizationId={organizationId} />
						</TabsContent>
						<TabsContent value="invitations" className="mt-6">
							<OrganizationInvitationsList organizationId={organizationId} />
						</TabsContent>
					</Tabs>
				</CardContent>
			</Card>

			{/* Invite Member Modal */}
			<InviteMemberModal
				organizationId={organizationId}
				open={isInviteModalOpen}
				onOpenChange={setIsInviteModalOpen}
			/>
		</div>
	);
}
