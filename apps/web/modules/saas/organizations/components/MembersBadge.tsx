"use client";

import { useOrganizationMembers } from "../hooks/use-organization-members";
import { Badge } from "@ui/components/badge";
import { Users2Icon, ClockIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@ui/lib";

interface MembersBadgeProps {
  className?: string;
  showPending?: boolean;
}

export function MembersBadge({ className, showPending = true }: MembersBadgeProps) {
  const t = useTranslations();
  const { totalMembers, totalPendingInvitations, isLoading } = useOrganizationMembers();

  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Users2Icon className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">
          {t("common.loading")}
        </span>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-1">
        <Users2Icon className="h-4 w-4 text-muted-foreground" />
        <Badge variant="secondary" className="text-xs">
          {totalMembers}
        </Badge>
      </div>

      {showPending && totalPendingInvitations > 0 && (
        <div className="flex items-center gap-1">
          <ClockIcon className="h-4 w-4 text-amber-500" />
          <Badge variant="outline" className="text-xs text-amber-600 border-amber-200">
            {totalPendingInvitations}
          </Badge>
        </div>
      )}
    </div>
  );
}
