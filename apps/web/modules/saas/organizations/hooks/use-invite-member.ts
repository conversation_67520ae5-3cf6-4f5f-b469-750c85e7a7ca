"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@shared/lib/api-client";

interface InviteMemberData {
  organizationId: string;
  email: string;
  role: "MEMBER" | "ADMIN";
}

export function useInviteMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: InviteMemberData) => {
      const response = await apiClient.organizations[":organizationId"].invitations.$post({
        param: {
          organizationId: data.organizationId,
        },
        json: {
          email: data.email,
          role: data.role,
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Failed to invite member: ${response.status} - ${errorData}`);
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate organization queries to refresh member lists
      queryClient.invalidateQueries({
        queryKey: ["fullOrganization", variables.organizationId],
      });

      queryClient.invalidateQueries({
        queryKey: ["user", "activeOrganization"],
      });
    },
  });
}
