"use client";

import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "./use-active-organization";
import { useFullOrganizationQuery } from "../lib/api";

export function useOrganizationMembers() {
  const { activeOrganization } = useActiveOrganization();
  const { data: organization, isLoading, error } = useFullOrganizationQuery(
    activeOrganization?.id || ""
  );

  const members = organization?.members || [];
  const pendingInvitations = organization?.invitations?.filter(
    (invitation) => invitation.status === "pending"
  ) || [];

  return {
    members,
    pendingInvitations,
    totalMembers: members.length,
    totalPendingInvitations: pendingInvitations.length,
    isLoading,
    error,
  };
}
