"use client";

import { useState, useRef } from "react";
import { useFileUpload } from "../hooks";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Progress } from "@ui/components/progress";
import { XIcon, UploadIcon, ImageIcon, PackageIcon } from "lucide-react";
import { cn } from "@ui/lib";

interface ProductImageUploadProps {
  currentImage?: string | null;
  onImageChange: (url: string | null) => void;
  className?: string;
  showLabel?: boolean;
}

export function ProductImageUpload({ 
  currentImage, 
  onImageChange, 
  className,
  showLabel = true 
}: ProductImageUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadFile, isUploading, uploadProgress } = useFileUpload({
    bucket: "checkoutBanners", // Usando bucket que sabemos que funciona
    onSuccess: (url) => {
      console.log("Upload bem-sucedido, URL:", url);
      onImageChange(url);
    },
    onError: (error) => {
      console.error("Erro no upload:", error);
    },
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFile = (file: File) => {
    uploadFile(file);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const removeImage = () => {
    onImageChange(null);
  };

  // Convert R2 URL to CDN URL if needed
  const displayUrl = currentImage && currentImage.includes('r2.cloudflarestorage.com')
    ? currentImage.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : currentImage;

  return (
    <div className={cn("space-y-4", className)}>
      {showLabel && (
        <div>
          <p className="text-sm font-medium text-foreground">Product cover</p>
          <p className="text-xs text-muted-foreground">
            Imagem que representa seu produto
          </p>
        </div>
      )}

      <Card 
        className={cn(
          "border-2 border-dashed transition-colors",
          dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          currentImage && "border-solid border-border"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <CardContent className="p-6">
          {currentImage ? (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={displayUrl || currentImage}
                  alt="Product cover"
                  className="w-full h-32 object-cover rounded-lg"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={removeImage}
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
              
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Enviando imagem...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}

              <div className="flex justify-center">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="px-6"
                >
                  <UploadIcon className="h-4 w-4 mr-2" />
                  {isUploading ? 'Enviando...' : 'Alterar imagem'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {isUploading ? (
                <div className="space-y-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <UploadIcon className="h-6 w-6 text-primary animate-pulse" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Enviando imagem...</span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <Progress value={uploadProgress} className="w-full" />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto">
                    <PackageIcon className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div className="space-y-2 text-center">
                    <p className="text-sm font-medium">
                      Arraste uma imagem aqui ou clique para selecionar
                    </p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, JPEG ou WebP até 10MB
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="px-6"
                    >
                      <UploadIcon className="h-4 w-4 mr-2" />
                      Selecionar Arquivo
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInput}
        className="hidden"
      />
    </div>
  );
}
