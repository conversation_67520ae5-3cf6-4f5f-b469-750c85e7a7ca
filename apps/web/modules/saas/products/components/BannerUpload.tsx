"use client";

import { useState, useRef } from "react";
import { useFileUpload } from "../hooks";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Progress } from "@ui/components/progress";
import { XIcon, UploadIcon, ImageIcon } from "lucide-react";
import { cn } from "@ui/lib";

interface BannerUploadProps {
  currentBanner?: string | null;
  onBannerChange: (url: string | null) => void;
  className?: string;
  showLabel?: boolean;
}

export function BannerUpload({ currentBanner, onBannerChange, className, showLabel = true }: BannerUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadFile, isUploading, uploadProgress } = useFileUpload({
    bucket: "checkoutBanners",
    onSuccess: (url) => {
      onBannerChange(url);
    },
    onError: (error) => {
      console.error("Erro no upload:", error);
    },
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFile = (file: File) => {
    uploadFile(file);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const removeBanner = () => {
    onBannerChange(null);
  };

  return (
    <div className={cn("space-y-4", className)}>
      {showLabel && <Label>Banner do Checkout</Label>}

      {currentBanner ? (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="relative">
                <img
                  src={currentBanner}
                  alt="Banner do checkout"
                  className="w-full h-32 object-cover rounded-lg"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={removeBanner}
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                Banner configurado com sucesso
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card
          className={cn(
            "border-2 border-dashed transition-colors",
            dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
            isUploading && "pointer-events-none"
          )}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              {isUploading ? (
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <UploadIcon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Fazendo upload...</p>
                    <Progress value={uploadProgress} className="w-full" />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto">
                    <ImageIcon className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">
                      Arraste uma imagem aqui ou clique para selecionar
                    </p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, JPEG ou WebP até 10MB
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="px-6"
                    >
                      <UploadIcon className="h-4 w-4 mr-2" />
                      Selecionar Arquivo
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInput}
        className="hidden"
      />
    </div>
  );
}
