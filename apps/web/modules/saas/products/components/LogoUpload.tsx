"use client";

import { useState, useRef } from "react";
import { Button } from "@ui/components/button";
import { UploadIcon, XIcon, ImageIcon } from "lucide-react";
import { useFileUpload } from "../hooks/useFileUpload";
import { cn } from "@ui/lib";

interface LogoUploadProps {
  currentLogo?: string | null;
  onLogoChange: (url: string | null) => void;
  className?: string;
  showLabel?: boolean;
}

export function LogoUpload({ 
  currentLogo, 
  onLogoChange, 
  className,
  showLabel = true 
}: LogoUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadFile, isUploading, uploadProgress } = useFileUpload({
    bucket: "checkoutBanners", // Usando bucket que sabemos que funciona
    onSuccess: (url) => {
      console.log("Upload do logo bem-sucedido, URL:", url);
      onLogoChange(url);
    },
    onError: (error) => {
      console.error("Erro no upload do logo:", error);
    },
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFile = (file: File) => {
    uploadFile(file);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const removeLogo = () => {
    onLogoChange(null);
  };

  // Convert R2 URL to CDN URL if needed
  const displayUrl = currentLogo && currentLogo.includes('r2.cloudflarestorage.com')
    ? currentLogo.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : currentLogo;

  return (
    <div className={cn("space-y-3", className)}>
      <div className="space-y-3">
        {/* Preview do Logo */}
        {displayUrl && (
          <div className="relative group">
            <div className="flex items-center justify-center">
              <img
                src={displayUrl}
                alt="Logo preview"
                className="max-h-32 w-auto object-contain"
                style={{ maxWidth: '100%' }}
              />
            </div>
            <Button
              type="button"
              variant="error"
              size="sm"
              className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={removeLogo}
            >
              <XIcon className="h-3 w-3" />
            </Button>
          </div>
        )}

        {/* Upload Area */}
        {!displayUrl && (
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-4 text-center transition-colors",
              dragActive
                ? "border-primary bg-primary/5"
                : "border-gray-300 hover:border-gray-400",
              isUploading && "pointer-events-none opacity-50"
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/png,image/jpeg,image/jpg,image/webp"
              onChange={handleFileInput}
              className="hidden"
            />

            {isUploading ? (
              <div className="space-y-2">
                <div className="w-6 h-6 mx-auto">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Enviando... {uploadProgress}%
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                <UploadIcon className="h-6 w-6 mx-auto text-muted-foreground" />
                <div>
                  <p className="text-xs font-medium">
                    Arraste um logo aqui ou{" "}
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-primary hover:underline"
                    >
                      clique para selecionar
                    </button>
                  </p>
                  <p className="text-xs text-muted-foreground">
                    PNG, JPG, WEBP até 10MB
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Botão para trocar logo */}
        {displayUrl && !isUploading && (
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="flex-1"
            >
              <ImageIcon className="h-4 w-4 mr-2" />
              Trocar Logo
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={removeLogo}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
