import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
export interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  priceCents: number;
  comparePriceCents?: number;
  currency: string;
  type: "COURSE" | "EBOOK" | "MENTORSHIP" | "SUBSCRIPTION" | "BUNDLE";
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SUSPENDED";
  visibility: "PUBLIC" | "PRIVATE" | "UNLISTED";
  categoryId?: string;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  thumbnail?: string;
  gallery: string[];
  tags: string[];
  features: string[];
  requirements: string[];
  duration?: number;
  level?: string;
  language: string;
  certificate: boolean;
  downloadable: boolean;
  checkoutType: "DEFAULT" | "CUSTOM" | "EXTERNAL";
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  _count?: {
    enrollments: number;
    orders: number;
  };
}

export interface CreateProductData {
  organizationId: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  priceCents: number;
  comparePriceCents?: number;
  currency?: string;
  type: "COURSE" | "EBOOK" | "MENTORSHIP" | "SUBSCRIPTION" | "BUNDLE";
  status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SUSPENDED";
  visibility?: "PUBLIC" | "PRIVATE" | "UNLISTED";
  categoryId?: string;
  thumbnail?: string;
  gallery?: string[];
  tags?: string[];
  features?: string[];
  requirements?: string[];
  duration?: number;
  level?: string;
  language?: string;
  certificate?: boolean;
  downloadable?: boolean;
  checkoutType?: "DEFAULT" | "CUSTOM" | "EXTERNAL";
  settings?: Record<string, any>;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export interface ProductFilters {
  page?: number;
  limit?: number;
  status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SUSPENDED";
  type?: "COURSE" | "EBOOK" | "MENTORSHIP" | "SUBSCRIPTION" | "BUNDLE";
  search?: string;
}

export interface ProductsResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ProductAnalytics {
  totalSales: number;
  totalRevenue: number;
  enrollments: number;
  recentOrders: Array<{
    id: string;
    status: string;
    totalCents: number;
    createdAt: string;
    buyer: {
      name?: string;
      email: string;
    };
  }>;
  monthlyRevenue: Array<{
    createdAt: string;
    _sum: {
      totalCents: number | null;
    };
    _count: {
      id: number;
    };
  }>;
}

// API Functions
const apiClient = {
  async getProducts(organizationId: string, filters: ProductFilters = {}) {
    const params = new URLSearchParams({
      organizationId,
      page: String(filters.page || 1),
      limit: String(filters.limit || 10),
      ...(filters.status && { status: filters.status }),
      ...(filters.type && { type: filters.type }),
      ...(filters.search && { search: filters.search }),
    });

    const response = await fetch(`/api/products?${params}`);
    if (!response.ok) {
      throw new Error("Failed to fetch products");
    }
    return response.json() as Promise<ProductsResponse>;
  },

  async getProduct(id: string) {
    const response = await fetch(`/api/products/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch product");
    }
    return response.json() as Promise<{ product: Product }>;
  },

  async createProduct(data: CreateProductData) {
    const response = await fetch("/api/products", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create product");
    }
    return response.json() as Promise<{ product: Product }>;
  },

  async updateProduct(data: UpdateProductData) {
    const { id, ...updateData } = data;
    const response = await fetch(`/api/products/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update product");
    }
    return response.json() as Promise<{ product: Product }>;
  },

  async deleteProduct(id: string) {
    const response = await fetch(`/api/products/${id}`, {
      method: "DELETE",
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete product");
    }
    return response.json() as Promise<{ message: string }>;
  },

  async updateProductStatus(id: string, status: Product["status"]) {
    const response = await fetch(`/api/products/${id}/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ status }),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update product status");
    }
    return response.json() as Promise<{ product: Product }>;
  },

  async getProductAnalytics(id: string) {
    const response = await fetch(`/api/products/${id}/analytics`);
    if (!response.ok) {
      throw new Error("Failed to fetch product analytics");
    }
    return response.json() as Promise<{ analytics: ProductAnalytics }>;
  },
};

// React Query Hooks
export function useProducts(organizationId: string, filters: ProductFilters = {}) {
  return useQuery({
    queryKey: ["products", "list", organizationId, filters],
    queryFn: () => apiClient.getProducts(organizationId, filters),
    enabled: !!organizationId,
  });
}

export function useProduct(id: string) {
  return useQuery({
    queryKey: ["products", "detail", id],
    queryFn: () => apiClient.getProduct(id),
    enabled: !!id,
  });
}

export function useProductAnalytics(id: string) {
  return useQuery({
    queryKey: ["products", "analytics", id],
    queryFn: () => apiClient.getProductAnalytics(id),
    enabled: !!id,
  });
}

export function useCreateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.createProduct,
    onSuccess: (data, variables) => {
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list", variables.organizationId],
      });

      // Set the new product in cache
      queryClient.setQueryData(["products", "detail", data.product.id], data);
    },
    onError: (error: Error) => {
      toast.error(`Erro ao criar produto: ${error.message}`);
    },
  });
}

export function useUpdateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.updateProduct,
    onSuccess: (data, variables) => {
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list"],
      });

      // Update the product in cache
      queryClient.setQueryData(["products", "detail", variables.id], data);

      toast.success("Produto atualizado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao atualizar produto: ${error.message}`);
    },
  });
}

export function useDeleteProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.deleteProduct,
    onSuccess: (_, productId) => {
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list"],
      });

      // Remove the product from cache
      queryClient.removeQueries({
        queryKey: ["products", "detail", productId],
      });

      toast.success("Produto deletado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao deletar produto: ${error.message}`);
    },
  });
}

export function useUpdateProductStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Product["status"] }) =>
      apiClient.updateProductStatus(id, status),
    onSuccess: (data, variables) => {
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list"],
      });

      // Update the product in cache
      queryClient.setQueryData(["products", "detail", variables.id], data);

      toast.success("Status do produto atualizado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(`Erro ao atualizar status: ${error.message}`);
    },
  });
}

// Export API client for direct use if needed
export { apiClient };
