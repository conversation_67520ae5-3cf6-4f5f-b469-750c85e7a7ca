"use client";

import { useState } from "react";
import { config } from "@repo/config";
import { useSignedUploadUrlMutation } from "@saas/shared/lib/api";
import { v4 as uuid } from "uuid";

interface UseFileUploadOptions {
  bucket: "checkoutBanners" | "onboardingDocuments" | "userDocuments" | "companyDocuments" | "testimonialAvatars" | "products";
  onSuccess?: (url: string) => void;
  onError?: (error: string) => void;
}

export function useFileUpload({ bucket, onSuccess, onError }: UseFileUploadOptions) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const getSignedUploadUrlMutation = useSignedUploadUrlMutation();

  const uploadFile = async (file: File, path?: string) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      // Validar tipo de arquivo
      const allowedTypes = [
        "image/jpeg",
        "image/png",
        "image/jpg",
        "image/webp",
        "application/pdf",
      ];

      if (!allowedTypes.includes(file.type)) {
        throw new Error(`Tipo de arquivo não suportado. Tipos permitidos: ${allowedTypes.join(", ")}`);
      }

      // Validar tamanho do arquivo (10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        throw new Error(`Arquivo muito grande. Tamanho máximo: ${maxSize / (1024 * 1024)}MB`);
      }

      // Gerar caminho do arquivo se não fornecido
      const filePath = path || (bucket === "products" 
        ? `product-images/${Date.now()}-${uuid()}.${file.name.split('.').pop()}`
        : `checkout-banners/${Date.now()}-${uuid()}.${file.name.split('.').pop()}`);

      // Obter URL de upload assinada usando o hook do supastarter
      setUploadProgress(20);
      console.log("Solicitando URL assinada para:", { 
        filePath, 
        bucket: config.storage.bucketNames[bucket],
        bucketName: bucket,
        config: config.storage.bucketNames
      });
      
      const { signedUrl } = await getSignedUploadUrlMutation.mutateAsync({
        path: filePath,
        bucket: config.storage.bucketNames[bucket],
      });
      
      console.log("URL assinada obtida:", signedUrl);

      // Fazer upload do arquivo
      setUploadProgress(50);
      const uploadResult = await fetch(signedUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResult.ok) {
        throw new Error("Falha ao fazer upload do arquivo");
      }

      setUploadProgress(80);

      // Construir URL de acesso direta ao arquivo
      // Usar domínio personalizado CDN se configurado, senão usar endpoint R2
      const bucketName = config.storage.bucketNames[bucket];
      const cdnDomain = config.storage.cdnDomain;
      const r2Endpoint = config.storage.publicEndpoint;

      // Se for checkoutBanners, testimonialAvatars ou products, usar CDN, senão usar R2
      const accessUrl = (bucket === "checkoutBanners" || bucket === "testimonialAvatars" || bucket === "products")
        ? `${cdnDomain}/${filePath}`
        : `${r2Endpoint}/${bucketName}/${filePath}`;

      setUploadProgress(100);
      onSuccess?.(accessUrl);
      return accessUrl;
    } catch (error) {
      console.error("Erro detalhado no upload:", error);
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      onError?.(errorMessage);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return {
    uploadFile,
    isUploading,
    uploadProgress,
  };
}
