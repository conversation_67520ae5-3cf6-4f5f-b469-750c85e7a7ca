"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import { useDashboard } from "../hooks/useDashboard";
import { useMetrics } from "@saas/shared/hooks/useMetrics";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, ResponsiveContainer, Pie, Pie<PERSON>hart, Cell } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@ui/components/chart";
import {
  DollarSign,
  TrendingUp,
  Target,
  CreditCard,
  Receipt,
  Activity,
  Calendar,
  ArrowUpRight,
  Shield,
  Smartphone,
} from "lucide-react";
import { FaPix } from "react-icons/fa6";

interface DashboardClientProps {
  organization: {
    id: string;
    name: string;
    slug: string;
  };
}

type TimeFilter = 'today' | '7d' | '30d' | 'all';

const timeFilterOptions = [
  { value: 'today', label: 'Hoje' },
  { value: '7d', label: '7 dias' },
  { value: '30d', label: '30 dias' },
  { value: 'all', label: 'Sempre' },
];

export function DashboardClient({ organization }: DashboardClientProps) {
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('30d');
  const { data: dashboard, isLoading, error } = useDashboard(organization.id);
  const { data: metrics, isLoading: metricsLoading } = useMetrics(organization.id);

  // Mock revenue data for area chart - Last 6 months
  const revenueData = [
    { month: 'Jul', revenue: 41500, transactions: 94 },
    { month: 'Ago', revenue: 38700, transactions: 87 },
    { month: 'Set', revenue: 45300, transactions: 102 },
    { month: 'Out', revenue: 52100, transactions: 118 },
    { month: 'Nov', revenue: 48900, transactions: 109 },
    { month: 'Dez', revenue: 57600, transactions: 125 },
  ];

  // Payment methods data - 4 main methods
  const paymentMethods = [
    {
      name: 'PIX',
      icon: FaPix,
      revenue: 28900,
      percentage: 45.2,
    },
    {
      name: 'Cartão de Crédito',
      icon: CreditCard,
      revenue: 22300,
      percentage: 34.8,
    },
    {
      name: '3DS Security',
      icon: Shield,
      revenue: 8400,
      percentage: 13.1,
    },
    {
      name: 'Boleto',
      icon: Receipt,
      revenue: 4300,
      percentage: 6.7,
    },
  ];

  // Order status data for pie chart
  const orderStatusData = [
    { status: 'Completado', orders: 245, percentage: 68.2 },
    { status: 'Pendente', orders: 67, percentage: 18.7 },
    { status: 'Processando', orders: 32, percentage: 8.9 },
    { status: 'Cancelado', orders: 15, percentage: 4.2 },
  ];

  const pieChartConfig = {
    orders: {
      label: "Pedidos",
    },
    'Completado': {
      label: "Completado",
      color: "#10b981", // Emerald-500
    },
    'Pendente': {
      label: "Pendente",
      color: "#f59e0b", // Amber-500
    },
    'Processando': {
      label: "Processando",
      color: "#3b82f6", // Blue-500
    },
    'Cancelado': {
      label: "Cancelado",
      color: "#ef4444", // Red-500
    },
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
    }).format(value);
  };

  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-destructive/10 flex items-center justify-center">
            <Activity className="h-8 w-8 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold text-destructive mb-2">
            Erro ao carregar dashboard
          </h2>
          <p className="text-muted-foreground mb-4">
            Não foi possível carregar os dados do dashboard. Tente novamente.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6">
      {/* Header with Time Filter */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Dashboard
          </h1>
          <p className="text-muted-foreground">
            Visão geral completa da sua plataforma
          </p>
        </div>
        <div className="flex items-center gap-3">

          <Select value={timeFilter} onValueChange={(value) => setTimeFilter(value as TimeFilter)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeFilterOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Main Metrics */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <MetricCard
            title="Receita Total"
            value={formatCurrency(125430)}
            change="+12.5%"
            isPositive={true}

            icon={DollarSign}
            description="Receita acumulada no período"
            badge={{
              text: "RECEITA",
              variant: "default"
            }}
            className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
            isLoading={isLoading || metricsLoading}
          />

          <MetricCard
            title="Taxa de Conversão"
            value="3.2%"
            change="+0.8%"
            isPositive={true}
            icon={Target}
            description="Visitantes que compraram"
            badge={{
              text: "CONVERSÃO",
              variant: "outline"
            }}
            className="bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20"
            isLoading={isLoading || metricsLoading}
          />

          <MetricCard
            title="Ticket Médio"
            value={formatCurrency(18750)}
            change="+5.2%"
            isPositive={true}
            icon={TrendingUp}
            description="Valor médio por transação"
            badge={{
              text: "TICKET",
              variant: "secondary"
            }}
            className="bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20"
            isLoading={isLoading || metricsLoading}
          />
        </div>
      </div>

      {/* Charts Section - Side by Side on Desktop */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Revenue Area Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Faturamento Mensal
            </CardTitle>
            <CardDescription>
              Evolução da receita mensal nos últimos 6 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                revenue: {
                  label: "Receita",
                  color: "#10b981", // Emerald-500
                },
              }}
              className="h-[300px] w-full"
            >
              <AreaChart data={revenueData}>
                <defs>
                  <linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="#10b981"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="#10b981"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => value}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => formatCurrency(value)}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator="line" />}
                />
                <Area
                  dataKey="revenue"
                  type="natural"
                  fill="url(#fillRevenue)"
                  fillOpacity={0.4}
                  stroke="#10b981"
                  strokeWidth={2}
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Order Status Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Status dos Pedidos
            </CardTitle>
            <CardDescription>
              Distribuição dos pedidos por status nos últimos 30 dias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={pieChartConfig}
              className="h-[300px] w-full"
            >
              <PieChart>
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
              <Pie
                data={orderStatusData.map((item, index) => ({
                  ...item,
                  fill: Object.values(pieChartConfig).slice(1)[index]?.color || "#8884d8"
                }))}
                dataKey="orders"
                nameKey="status"
                cx="50%"
                cy="50%"
                outerRadius={100}
                innerRadius={50}
                paddingAngle={2}
                label={({ status, percentage }) => `${status}: ${percentage}%`}
              />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Metrics */}
      <div className="space-y-4">
        {/* <div className="flex items-center gap-2">
          <h2 className="text-2xl font-semibold tracking-tight">Faturamento por Meio de Pagamento</h2>
          <Badge status="info">
            {timeFilterOptions.find(opt => opt.value === timeFilter)?.label}
          </Badge>
        </div> */}

        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {paymentMethods.map((method) => {
            const IconComponent = method.icon;
            return (
              <Card key={method.name} className="relative overflow-hidden hover:shadow-md transition-all duration-200">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      {method.name}
                    </CardTitle>
                    <Badge variant="outline" className="text-xs">
                      {method.percentage}%
                    </Badge>
                  </div>
                  <div className="relative">
                    <IconComponent className="h-4 w-4 text-muted-foreground" />
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary/20 rounded-full animate-pulse" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {formatCurrency(method.revenue)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {method.percentage}% do total faturado
                    </div>
                  </div>

                  {/* Progress bar */}
                  {/* <div className="mt-4">
                    <div className="w-full bg-muted rounded-full h-0.5">
                      <div
                        className="bg-emerald-500 h-0.5 rounded-full transition-all duration-300"
                        style={{ width: `${method.percentage}%` }}
                      />
                    </div>
                  </div> */}

                  {/* Growth indicator */}
                  <div className="mt-3 flex items-center gap-1 text-green-600">
                    <ArrowUpRight className="h-3 w-3" />
                    <span className="text-xs font-medium">+{Math.round(method.percentage * 0.1)}%</span>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}
