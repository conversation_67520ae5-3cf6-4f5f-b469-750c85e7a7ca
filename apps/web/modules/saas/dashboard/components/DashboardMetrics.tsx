"use client";

import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import { useMetrics } from "@saas/shared/hooks/useMetrics";
import {
  DollarSign,
  ShoppingCart,
  Users,
} from "lucide-react";

interface DashboardMetricsProps {
  organizationId: string;
  isLoading?: boolean;
}

export function DashboardMetrics({ organizationId, isLoading }: DashboardMetricsProps) {
  const { data: metrics, isLoading: metricsLoading } = useMetrics(organizationId);

  // As 3 métricas mais relevantes para uma organização
  const metricConfigs = [
    {
      key: 'revenue' as const,
      icon: DollarSign,
      className: "bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20",
      badge: { text: "MÊS ATUAL", variant: "default" as const },
    },
    {
      key: 'sales' as const,
      icon: ShoppingCart,
      className: "bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20",
      badge: { text: "VENDAS", variant: "outline" as const },
    },
    {
      key: 'customers' as const,
      icon: Users,
      className: "bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20",
      badge: { text: "ATIVOS", variant: "secondary" as const },
    },
  ];

  return (
    <MetricGrid columns={3}>
      {metricConfigs.map((config) => {
        const { key, icon, className, badge } = config;
        const metric = metrics?.[key];
        if (!metric) return null;

        return (
          <MetricCard
            key={key}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            isPositive={metric.isPositive}
            icon={icon}
            isLoading={isLoading || metricsLoading}
            description={metric.description}
            badge={badge}
            className={className}
          />
        );
      })}
    </MetricGrid>
  );
}
