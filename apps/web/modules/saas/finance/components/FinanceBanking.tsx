"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { WithdrawModal } from "./WithdrawModal";
import {
  BuildingIcon,
  ShieldCheckIcon,
  PlusIcon,
  EyeIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  ExternalLinkIcon,
} from "lucide-react";

interface FinanceBankingProps {
  organizationId: string;
  organizationSlug?: string;
}

import { getSimulationStage, getBankingData } from "../config/simulation";

// Mock data - replace with real API calls
const simulationStage = getSimulationStage();
const mockBankingData = getBankingData(simulationStage);

export function FinanceBanking({ organizationId, organizationSlug }: FinanceBankingProps) {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { label: "Aprovado", status: "success" as const },
      PENDING: { label: "Pendente", status: "warning" as const },
      REJECTED: { label: "Rejeitado", status: "error" as const },
      PROCESSING: { label: "Processando", status: "info" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge status={config.status}>{config.label}</Badge>;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case "PENDING":
        return <AlertCircleIcon className="h-5 w-5 text-yellow-600" />;
      case "REJECTED":
        return <AlertCircleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircleIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Bank Account Card */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <BuildingIcon className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Conta Bancária</CardTitle>
                  <CardDescription>Configure para receber saques</CardDescription>
                </div>
              </div>
              {getStatusBadge(mockBankingData.hasBankAccount ? "COMPLETED" : "PENDING")}
            </div>
          </CardHeader>
          <CardContent>
            {mockBankingData.hasBankAccount ? (
              <div className="space-y-3">
                <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2">
                    <CheckCircleIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">Conta configurada</span>
                  </div>
                  <p className="text-xs text-green-700 mt-1">
                    {mockBankingData.bankAccounts[0]?.bankName} - {mockBankingData.bankAccounts[0]?.accountNumber}
                  </p>
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Gerenciar Conta
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <div className="flex items-center gap-2">
                    <AlertCircleIcon className="h-4 w-4 text-amber-600" />
                    <span className="text-sm font-medium text-amber-800">Conta não configurada</span>
                  </div>
                  <p className="text-xs text-amber-700 mt-1">
                    Adicione uma conta para receber saques
                  </p>
                </div>
                <Button size="sm" className="w-full">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Adicionar Conta
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* KYC Card */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-lg">
                  <ShieldCheckIcon className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Validação KYC</CardTitle>
                  <CardDescription>Verificação de identidade</CardDescription>
                </div>
              </div>
              {getStatusBadge(mockBankingData.kycStatus)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${mockBankingData.hasDocumentsUploaded ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className={mockBankingData.hasDocumentsUploaded ? 'text-green-700' : 'text-gray-600'}>
                    Documentos enviados
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${mockBankingData.hasKycCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className={mockBankingData.hasKycCompleted ? 'text-green-700' : 'text-gray-600'}>
                    Identidade validada
                  </span>
                </div>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => {
                  window.location.href = `/app/${organizationSlug}/onboarding/kyc`;
                }}
              >
                <ExternalLinkIcon className="h-4 w-4 mr-2" />
                {mockBankingData.hasKycCompleted ? 'Revalidar KYC' : 'Iniciar Validação'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShieldCheckIcon className="h-5 w-5" />
            Ações Rápidas
          </CardTitle>
          <CardDescription>
            Gerencie sua configuração de saques
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              onClick={() => {
                window.location.href = `/app/${organizationSlug}/onboarding/kyc`;
              }}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <BuildingIcon className="h-5 w-5 text-blue-600" />
                </div>
                <div className="text-left">
                  <div className="font-medium">Configurar Conta</div>
                  <div className="text-sm text-muted-foreground">
                    Adicionar dados bancários
                  </div>
                </div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              onClick={() => {
                window.location.href = `/app/${organizationSlug}/onboarding/kyc`;
              }}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-lg">
                  <ShieldCheckIcon className="h-5 w-5 text-green-600" />
                </div>
                <div className="text-left">
                  <div className="font-medium">Validar Identidade</div>
                  <div className="text-sm text-muted-foreground">
                    Enviar documentos KYC
                  </div>
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
