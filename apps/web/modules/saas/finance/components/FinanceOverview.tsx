"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import { WithdrawModal } from "./WithdrawModal";
import {
  TrendingUpIcon,
  DollarSignIcon,
  ClockIcon,
  PiggyBankIcon,
  WalletIcon,
  RefreshCcw,
  ShieldCheckIcon,
  DownloadIcon,
  ArrowUpRightIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from "lucide-react";

interface FinanceOverviewProps {
  organizationId: string;
  organizationSlug?: string;
}

// Mock data - replace with real API calls
const mockMetrics = {
  totalBalance: 125430.50,
  availableBalance: 98750.25,
  pendingBalance: 26680.25,
  totalRevenue: 542890.75,
  monthlyRevenue: 45230.80,
  totalFees: 12450.30,
  totalWithdraws: 89650.40,
  transactionCount: 2847,
  pendingTransactions: 23,
  failedTransactions: 5,
};

const mockRecentTransactions = [
  {
    id: "1",
    type: "CREDIT",
    description: "Venda - Curso Premium",
    amount: 1500.00,
    status: "COMPLETED",
    createdAt: "2024-01-20T10:30:00Z",
    paymentMethod: "PIX",
  },
  {
    id: "2",
    type: "DEBIT",
    description: "Taxa de Processamento",
    amount: -45.00,
    status: "COMPLETED",
    createdAt: "2024-01-20T09:15:00Z",
    paymentMethod: "PLATFORM_FEE",
  },
  {
    id: "3",
    type: "CREDIT",
    description: "Venda - Consultoria",
    amount: 2500.00,
    status: "PENDING",
    createdAt: "2024-01-20T08:45:00Z",
    paymentMethod: "CREDIT_CARD",
  },
];

export function FinanceOverview({ organizationId, organizationSlug }: FinanceOverviewProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { label: "Concluída", variant: "default" as const },
      PENDING: { label: "Pendente", variant: "secondary" as const },
      FAILED: { label: "Falhou", variant: "destructive" as const },
      PROCESSING: { label: "Processando", variant: "outline" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTransactionIcon = (type: string) => {
    return type === "CREDIT" ? (
      <ArrowUpIcon className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowDownIcon className="h-4 w-4 text-red-600" />
    );
  };

  const financialMetrics = [
    {
      title: "Saldo Disponível",
      value: formatCurrency(mockMetrics.availableBalance),
      change: "+12.5%",
      isPositive: true,
      icon: WalletIcon,
      description: `Total: ${formatCurrency(mockMetrics.totalBalance)}`,
      variant: "green-gradient" as const,
    },
    {
      title: "Receita Mensal",
      value: formatCurrency(mockMetrics.monthlyRevenue),
      change: "+12.5%",
      isPositive: true,
      icon: TrendingUpIcon,
      description: "vs mês anterior",
    },
    {
      title: "Em Processamento",
      value: formatCurrency(mockMetrics.pendingBalance),
      change: `${mockMetrics.pendingTransactions} transações`,
      isPositive: false,
      icon: ClockIcon,
      description: "Aguardando liquidação",
    },
    {
      title: "Saques Realizados",
      value: formatCurrency(mockMetrics.totalWithdraws),
      change: "+8.2%",
      isPositive: true,
      icon: PiggyBankIcon,
      description: "Total sacado este mês",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Financial Metrics Cards */}
      <MetricGrid columns={4}>
        {financialMetrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            isPositive={metric.isPositive}
            icon={metric.icon}
            description={metric.description}
            badge={metric.badge}
            variant={metric.variant}
          />
        ))}
      </MetricGrid>

    </div>
  );
}
