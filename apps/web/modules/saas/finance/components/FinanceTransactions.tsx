"use client";

import { useState, useMemo } from "react";
import { DataTable, DataTableColumn, DataTableAction } from "@saas/shared/components/DataTable";
import { ActionBar } from "@saas/shared/components/ActionBar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import {
  DownloadIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CreditCardIcon,
  BanknoteIcon,
  BuildingIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ReceiptIcon,
  RefreshCcw,
} from "lucide-react";
import { FaPix } from "react-icons/fa6";

interface FinanceTransactionsProps {
  organizationId: string;
}

// Mock transaction data - replace with real API calls
const mockTransactions = [
  {
    id: "txn_001",
    type: "CREDIT",
    status: "COMPLETED",
    amountCents: 150000, // R$ 1,500.00
    currency: "BRL",
    description: "Venda - Curso Premium de Marketing Digital",
    paymentMethod: "PIX",
    fromUser: "João Silva",
    toUser: null,
    externalId: "pix_123456789",
    processedAt: "2024-01-20T10:30:00Z",
    settledAt: "2024-01-20T10:31:00Z",
    createdAt: "2024-01-20T10:30:00Z",
    fees: [
      { type: "PLATFORM_FEE", amountCents: 4500, percentage: 3.0 }
    ]
  },
  {
    id: "txn_002",
    type: "DEBIT",
    status: "COMPLETED",
    amountCents: 4500, // R$ 45.00
    currency: "BRL",
    description: "Taxa de Plataforma",
    paymentMethod: "PLATFORM_FEE",
    fromUser: null,
    toUser: null,
    externalId: null,
    processedAt: "2024-01-20T10:31:00Z",
    settledAt: "2024-01-20T10:31:00Z",
    createdAt: "2024-01-20T10:31:00Z",
    fees: []
  },
  {
    id: "txn_003",
    type: "CREDIT",
    status: "PENDING",
    amountCents: 250000, // R$ 2,500.00
    currency: "BRL",
    description: "Venda - Consultoria Empresarial",
    paymentMethod: "CREDIT_CARD",
    fromUser: "Maria Santos",
    toUser: null,
    externalId: "cc_987654321",
    processedAt: null,
    settledAt: null,
    createdAt: "2024-01-20T08:45:00Z",
    fees: [
      { type: "PAYMENT_PROCESSOR_FEE", amountCents: 7250, percentage: 2.9 },
      { type: "PLATFORM_FEE", amountCents: 7500, percentage: 3.0 }
    ]
  },
  {
    id: "txn_004",
    type: "TRANSFER",
    status: "COMPLETED",
    amountCents: 50000, // R$ 500.00
    currency: "BRL",
    description: "Comissão de Afiliado",
    paymentMethod: "INTERNAL",
    fromUser: "Sistema",
    toUser: "Carlos Oliveira",
    externalId: null,
    processedAt: "2024-01-19T15:20:00Z",
    settledAt: "2024-01-19T15:20:00Z",
    createdAt: "2024-01-19T15:20:00Z",
    fees: []
  },
  {
    id: "txn_005",
    type: "REFUND",
    status: "FAILED",
    amountCents: 120000, // R$ 1,200.00
    currency: "BRL",
    description: "Estorno - Curso Básico",
    paymentMethod: "PIX",
    fromUser: null,
    toUser: "Ana Costa",
    externalId: "refund_456789",
    processedAt: "2024-01-19T12:10:00Z",
    settledAt: null,
    createdAt: "2024-01-19T12:00:00Z",
    fees: []
  },
  {
    id: "txn_006",
    type: "CREDIT",
    status: "COMPLETED",
    amountCents: 75000, // R$ 750.00
    currency: "BRL",
    description: "Venda - E-book Marketing",
    paymentMethod: "PIX",
    fromUser: "Pedro Lima",
    toUser: null,
    externalId: "pix_789123456",
    processedAt: "2024-01-18T16:30:00Z",
    settledAt: "2024-01-18T16:31:00Z",
    createdAt: "2024-01-18T16:30:00Z",
    fees: [
      { type: "PLATFORM_FEE", amountCents: 2250, percentage: 3.0 }
    ]
  },
  {
    id: "txn_007",
    type: "CREDIT",
    status: "PENDING",
    amountCents: 320000, // R$ 3,200.00
    currency: "BRL",
    description: "Venda - Curso Avançado",
    paymentMethod: "CREDIT_CARD",
    fromUser: "Carla Mendes",
    toUser: null,
    externalId: "cc_456789123",
    processedAt: null,
    settledAt: null,
    createdAt: "2024-01-18T14:15:00Z",
    fees: [
      { type: "PAYMENT_PROCESSOR_FEE", amountCents: 9280, percentage: 2.9 },
      { type: "PLATFORM_FEE", amountCents: 9600, percentage: 3.0 }
    ]
  },
  {
    id: "txn_008",
    type: "DEBIT",
    status: "COMPLETED",
    amountCents: 15000, // R$ 150.00
    currency: "BRL",
    description: "Taxa de Manutenção",
    paymentMethod: "PLATFORM_FEE",
    fromUser: null,
    toUser: null,
    externalId: null,
    processedAt: "2024-01-18T10:00:00Z",
    settledAt: "2024-01-18T10:00:00Z",
    createdAt: "2024-01-18T10:00:00Z",
    fees: []
  },
  {
    id: "txn_009",
    type: "CREDIT",
    status: "COMPLETED",
    amountCents: 45000, // R$ 450.00
    currency: "BRL",
    description: "Venda - Template Design",
    paymentMethod: "PIX",
    fromUser: "Roberto Silva",
    toUser: null,
    externalId: "pix_321654987",
    processedAt: "2024-01-17T20:45:00Z",
    settledAt: "2024-01-17T20:46:00Z",
    createdAt: "2024-01-17T20:45:00Z",
    fees: [
      { type: "PLATFORM_FEE", amountCents: 1350, percentage: 3.0 }
    ]
  },
  {
    id: "txn_010",
    type: "TRANSFER",
    status: "COMPLETED",
    amountCents: 25000, // R$ 250.00
    currency: "BRL",
    description: "Comissão de Parceiro",
    paymentMethod: "INTERNAL",
    fromUser: "Sistema",
    toUser: "Fernanda Oliveira",
    externalId: null,
    processedAt: "2024-01-17T18:20:00Z",
    settledAt: "2024-01-17T18:20:00Z",
    createdAt: "2024-01-17T18:20:00Z",
    fees: []
  },
  {
    id: "txn_011",
    type: "CREDIT",
    status: "COMPLETED",
    amountCents: 180000, // R$ 1,800.00
    currency: "BRL",
    description: "Venda - Mentoria Individual",
    paymentMethod: "CREDIT_CARD",
    fromUser: "Lucas Santos",
    toUser: null,
    externalId: "cc_654321987",
    processedAt: "2024-01-17T15:30:00Z",
    settledAt: "2024-01-17T15:31:00Z",
    createdAt: "2024-01-17T15:30:00Z",
    fees: [
      { type: "PAYMENT_PROCESSOR_FEE", amountCents: 5220, percentage: 2.9 },
      { type: "PLATFORM_FEE", amountCents: 5400, percentage: 3.0 }
    ]
  },
  {
    id: "txn_012",
    type: "REFUND",
    status: "COMPLETED",
    amountCents: 95000, // R$ 950.00
    currency: "BRL",
    description: "Estorno - Workshop Online",
    paymentMethod: "PIX",
    fromUser: null,
    toUser: "Patricia Costa",
    externalId: "refund_987654321",
    processedAt: "2024-01-16T11:45:00Z",
    settledAt: "2024-01-16T11:45:00Z",
    createdAt: "2024-01-16T11:30:00Z",
    fees: []
  }
];

export function FinanceTransactions({ organizationId }: FinanceTransactionsProps) {
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [isDetailSheetOpen, setIsDetailSheetOpen] = useState(false);
  const itemsPerPage = 10;

  const formatCurrency = (amountCents: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amountCents / 100);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "—";
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { label: "Concluída", variant: "default" as const },
      PENDING: { label: "Pendente", variant: "secondary" as const },
      PROCESSING: { label: "Processando", variant: "outline" as const },
      FAILED: { label: "Falhou", variant: "destructive" as const },
      CANCELLED: { label: "Cancelada", variant: "destructive" as const },
      REVERSED: { label: "Revertida", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      CREDIT: { label: "Crédito", variant: "default" as const, icon: ArrowUpIcon },
      DEBIT: { label: "Débito", variant: "secondary" as const, icon: ArrowDownIcon },
      TRANSFER: { label: "Transferência", variant: "outline" as const, icon: ArrowUpIcon },
      REFUND: { label: "Estorno", variant: "destructive" as const, icon: ArrowDownIcon },
      FEE: { label: "Taxa", variant: "secondary" as const, icon: ArrowDownIcon },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.CREDIT;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getPaymentMethodLabel = (method: string) => {
    const methodLabels = {
      PIX: "Pix",
      CREDIT_CARD: "Cartão de Crédito",
      DEBIT_CARD: "Cartão de Débito",
      BANK_TRANSFER: "Transferência Bancária",
      CASH: "Dinheiro",
      PLATFORM_FEE: "Taxa da Plataforma",
      INTERNAL: "Transferência Interna",
    };

    return methodLabels[method as keyof typeof methodLabels] || method;
  };

  const getPaymentMethodIcon = (method: string) => {
    // Cores específicas para cada método
    const getIconColor = (method: string) => {
      switch (method) {
        case 'PIX':
          return 'text-green-600';
        case 'CREDIT_CARD':
        case 'DEBIT_CARD':
          return 'text-blue-600';
        case 'PLATFORM_FEE':
          return 'text-orange-600';
        case 'BANK_TRANSFER':
          return 'text-purple-600';
        case 'INTERNAL':
          return 'text-gray-600';
        default:
          return 'text-gray-600';
      }
    };

    const iconColor = getIconColor(method);

    switch (method) {
      case 'PIX':
        return <FaPix className={`h-4 w-4 ${iconColor}`} />;
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return <CreditCardIcon className={`h-4 w-4 ${iconColor}`} />;
      case 'PLATFORM_FEE':
        return <ReceiptIcon className={`h-4 w-4 ${iconColor}`} />;
      case 'BANK_TRANSFER':
        return <BuildingIcon className={`h-4 w-4 ${iconColor}`} />;
      case 'CASH':
        return <BanknoteIcon className={`h-4 w-4 ${iconColor}`} />;
      case 'INTERNAL':
        return <BuildingIcon className={`h-4 w-4 ${iconColor}`} />;
      default:
        return <CreditCardIcon className={`h-4 w-4 ${iconColor}`} />;
    }
  };

  const columns: DataTableColumn<any>[] = [
    {
      key: "createdAt",
      label: "Data",
      sortable: true,
      render: (_, transaction) => (
        <div className="text-sm">
          {formatDate(transaction.createdAt)}
        </div>
      ),
    },
    {
      key: "type",
      label: "Tipo",
      render: (_, transaction) => getTypeBadge(transaction.type),
    },
    {
      key: "description",
      label: "Descrição",
      render: (_, transaction) => (
        <div>
          <div className="font-medium text-sm">{transaction.description}</div>
          <div className="text-xs text-muted-foreground">
            ID: {transaction.id}
          </div>
        </div>
      ),
    },
    {
      key: "paymentMethod",
      label: "Método",
      render: (_, transaction) => (
        <div className="flex items-center gap-2">
          {getPaymentMethodIcon(transaction.paymentMethod)}
          <span className="text-sm">{getPaymentMethodLabel(transaction.paymentMethod)}</span>
        </div>
      ),
    },
    {
      key: "amountCents",
      label: "Valor",
      sortable: true,
      render: (_, transaction) => (
        <div className={`text-sm font-medium ${
          transaction.type === "CREDIT" ? "text-green-600" : "text-red-600"
        }`}>
          {transaction.type === "CREDIT" ? "+" : "-"}
          {formatCurrency(transaction.amountCents)}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (_, transaction) => getStatusBadge(transaction.status),
    },
    {
      key: "settledAt",
      label: "Liquidação",
      render: (_, transaction) => (
        <div className="text-sm text-muted-foreground">
          {formatDate(transaction.settledAt)}
        </div>
      ),
    },
  ];

  const actions: DataTableAction<any>[] = [];

  const bulkActions = [
    {
      label: "Exportar Selecionadas",
      icon: DownloadIcon,
      onClick: (selectedIds: string[]) => {
        console.log("Exportar:", selectedIds);
      },
    },
    {
      label: "Reprocessar Selecionadas",
      icon: RefreshCcw,
      onClick: (selectedIds: string[]) => {
        console.log("Reprocessar:", selectedIds);
      },
    },
  ];

  const paginatedTransactions = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return mockTransactions.slice(startIndex, endIndex);
  }, [currentPage, itemsPerPage]);

  const totalPages = Math.ceil(mockTransactions.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleViewDetails = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsDetailSheetOpen(true);
  };

  return (
    <div className="space-y-6">
      <ActionBar
        selectedCount={selectedTransactions.length}
        onClearSelection={() => setSelectedTransactions([])}
        bulkActions={bulkActions}
      />

      <DataTable
        data={paginatedTransactions}
        columns={columns}
        selectedItems={selectedTransactions}
        onSelectionChange={setSelectedTransactions}
        onRowClick={handleViewDetails}
        emptyMessage="Nenhuma transação encontrada"
        loadingMessage="Carregando transações..."
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Mostrando {((currentPage - 1) * itemsPerPage) + 1} a {Math.min(currentPage * itemsPerPage, mockTransactions.length)} de {mockTransactions.length} transações
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Anterior
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
              {totalPages > 5 && (
                <>
                  <span className="text-muted-foreground">...</span>
                  <Button
                    variant={currentPage === totalPages ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    className="w-8 h-8 p-0"
                  >
                    {totalPages}
                  </Button>
                </>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Próxima
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Transaction Details Sheet */}
      <Sheet open={isDetailSheetOpen} onOpenChange={setIsDetailSheetOpen}>
        <SheetContent className="w-full sm:max-w-lg">
          <SheetHeader>
            <SheetTitle>Detalhes da Transação</SheetTitle>
          </SheetHeader>
          {selectedTransaction && (
            <div className="mt-6 space-y-6">
              {/* Transaction Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getPaymentMethodIcon(selectedTransaction.paymentMethod)}
                  <div>
                    <h3 className="font-semibold">{getPaymentMethodLabel(selectedTransaction.paymentMethod)}</h3>
                    <p className="text-sm text-muted-foreground">ID: {selectedTransaction.id}</p>
                  </div>
                </div>
                {getStatusBadge(selectedTransaction.status)}
              </div>

              {/* Amount */}
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-center">
                  <p className="text-2xl font-bold">{formatCurrency(selectedTransaction.amountCents)}</p>
                  <p className="text-sm text-muted-foreground">Valor da transação</p>
                </div>
              </div>

              {/* Transaction Details */}
              <div className="space-y-4">
                <h4 className="font-medium">Informações da Transação</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Data de criação:</span>
                    <span className="text-sm font-medium">{formatDate(selectedTransaction.createdAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Data de liquidação:</span>
                    <span className="text-sm font-medium">{formatDate(selectedTransaction.settledAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Status:</span>
                    <span className="text-sm font-medium">{getStatusBadge(selectedTransaction.status)}</span>
                  </div>
                  {selectedTransaction.fromUser && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Cliente:</span>
                      <span className="text-sm font-medium">{selectedTransaction.fromUser}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <h4 className="font-medium">Descrição</h4>
                <p className="text-sm text-muted-foreground">{selectedTransaction.description}</p>
              </div>

              {/* Fees */}
              {selectedTransaction.fees && selectedTransaction.fees.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium">Taxas</h4>
                  <div className="space-y-2">
                    {selectedTransaction.fees.map((fee: any, index: number) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-muted-foreground">{fee.description}</span>
                        <span className="font-medium">{formatCurrency(fee.amountCents)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 pt-4 border-t">
                <Button variant="outline" size="sm" className="flex-1">
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Baixar Comprovante
                </Button>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}
