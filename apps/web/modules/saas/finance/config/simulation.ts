// Configuração de simulação para desenvolvimento
// Mude estas variáveis para testar diferentes estados da aplicação

export const SIMULATION_CONFIG = {
  // Estado da conta para saques
  ACCOUNT_APPROVED: true, // true = conta aprovada, false = conta não aprovada

  // Estado do KYC
  KYC_COMPLETED: true, // true = KYC completado, false = KYC pendente

  // Estado da conta bancária
  BANK_ACCOUNT_ADDED: true, // true = conta bancária adicionada, false = sem conta

  // Estado dos documentos
  DOCUMENTS_UPLOADED: true, // true = documentos enviados, false = documentos pendentes
};

// Função para obter o estágio baseado na configuração
export const getSimulationStage = () => {
  const { ACCOUNT_APPROVED, KYC_COMPLETED, BANK_ACCOUNT_ADDED, DOCUMENTS_UPLOADED } = SIMULATION_CONFIG;

  if (ACCOUNT_APPROVED && KYC_COMPLETED && BANK_ACCOUNT_ADDED && DOCUMENTS_UPLOADED) {
    return "APPROVED";
  }

  if (BANK_ACCOUNT_ADDED && DOCUMENTS_UPLOADED && !KYC_COMPLETED) {
    return "KYC_PENDING";
  }

  if (BANK_ACCOUNT_ADDED && !DOCUMENTS_UPLOADED) {
    return "DOCUMENTS_UPLOADED";
  }

  if (BANK_ACCOUNT_ADDED && !KYC_COMPLETED) {
    return "BANK_ACCOUNT_ADDED";
  }

  return "NOT_STARTED";
};

// Função para obter dados de conta bancária
export const getBankingData = (stage: string) => {
  switch (stage) {
    case "NOT_STARTED":
      return {
        hasBankAccount: false,
        hasKycCompleted: false,
        hasDocumentsUploaded: false,
        kycStatus: "NOT_STARTED",
        documentsStatus: "NOT_STARTED",
        bankAccounts: [],
        currentStep: 1,
        totalSteps: 4
      };
    case "BANK_ACCOUNT_ADDED":
      return {
        hasBankAccount: true,
        hasKycCompleted: false,
        hasDocumentsUploaded: false,
        kycStatus: "PENDING",
        documentsStatus: "NOT_STARTED",
        bankAccounts: [{
          id: "1",
          bankName: "Banco do Brasil",
          accountNumber: "****1234",
          accountType: "Conta Corrente",
          isActive: true
        }],
        currentStep: 2,
        totalSteps: 4
      };
    case "DOCUMENTS_UPLOADED":
      return {
        hasBankAccount: true,
        hasKycCompleted: false,
        hasDocumentsUploaded: true,
        kycStatus: "PENDING",
        documentsStatus: "UPLOADED",
        bankAccounts: [{
          id: "1",
          bankName: "Banco do Brasil",
          accountNumber: "****1234",
          accountType: "Conta Corrente",
          isActive: true
        }],
        currentStep: 3,
        totalSteps: 4
      };
    case "KYC_PENDING":
      return {
        hasBankAccount: true,
        hasKycCompleted: false,
        hasDocumentsUploaded: true,
        kycStatus: "PENDING",
        documentsStatus: "PENDING",
        bankAccounts: [{
          id: "1",
          bankName: "Banco do Brasil",
          accountNumber: "****1234",
          accountType: "Conta Corrente",
          isActive: true
        }],
        currentStep: 3,
        totalSteps: 4
      };
    case "KYC_APPROVED":
      return {
        hasBankAccount: true,
        hasKycCompleted: true,
        hasDocumentsUploaded: true,
        kycStatus: "COMPLETED",
        documentsStatus: "COMPLETED",
        bankAccounts: [{
          id: "1",
          bankName: "Banco do Brasil",
          accountNumber: "****1234",
          accountType: "Conta Corrente",
          isActive: true
        }],
        currentStep: 4,
        totalSteps: 4
      };
    case "APPROVED":
      return {
        hasBankAccount: true,
        hasKycCompleted: true,
        hasDocumentsUploaded: true,
        kycStatus: "COMPLETED",
        documentsStatus: "COMPLETED",
        bankAccounts: [{
          id: "1",
          bankName: "Banco do Brasil",
          accountNumber: "****1234",
          accountType: "Conta Corrente",
          isActive: true
        }],
        currentStep: 4,
        totalSteps: 4
      };
    default:
      return {
        hasBankAccount: false,
        hasKycCompleted: false,
        hasDocumentsUploaded: false,
        kycStatus: "NOT_STARTED",
        documentsStatus: "NOT_STARTED",
        bankAccounts: [],
        currentStep: 1,
        totalSteps: 4
      };
  }
};
