"use client";

import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { OrganizationsGrid } from "@saas/organizations/components/OrganizationsGrid";
import { Button } from "@ui/components/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent } from "@ui/components";

export default function UserStart() {
	const t = useTranslations();
	const { user } = useSession();

	return (
		<div className="space-y-6">
			{/* Header with Title and New Organization Button */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">👋 Bem-vindo, {user?.name?.split(' ')[0]}!</h1>
					<p className="text-muted-foreground text-lg mt-1">
						Escolha uma empresa para gerenciar ou crie uma nova
					</p>
				</div>
				{config.organizations.enableUsersToCreateOrganizations && (
					<Button asChild>
						<Link href="/app/new-organization">
							<PlusIcon className="h-4 w-4 mr-2" />
							Nova Empresa
						</Link>
					</Button>
				)}
			</div>

			{/* Organizations Grid */}
			{config.organizations.enable && (
				<Suspense fallback={
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{[1, 2, 3].map((i) => (
							<Card key={i} className="animate-pulse">
								<CardContent className="p-6">
									<div className="flex items-center space-x-4">
										<div className="rounded-full bg-muted h-12 w-12"></div>
										<div className="space-y-2 flex-1">
											<div className="h-4 bg-muted rounded w-3/4"></div>
											<div className="h-3 bg-muted rounded w-1/2"></div>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				}>
					<OrganizationsGrid />
				</Suspense>
			)}
		</div>
	);
}
