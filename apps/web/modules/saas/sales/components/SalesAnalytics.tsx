"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
  DollarSignIcon,
  TrendingUpIcon,
  ShoppingCartIcon,
  CalendarIcon,
  UsersIcon,
  ArrowDownIcon,
  ArrowUpRight
} from "lucide-react";

interface SalesAnalyticsProps {
  organizationId: string;
  productIds?: string[];
}

// Mock data - in real implementation, fetch from API
const mockSalesMetrics = {
  totalOrders: 1247,
  totalRevenue: 8975000, // in cents
  todayRevenue: 289000, // in cents
  averageOrderValue: 720000, // in cents
  conversionRate: 3.2,
  refundRate: 1.8,
  monthlyGrowth: 7.9,
  todayGrowth: -7.4,
  conversionGrowth: 14.3,
  refundGrowth: -21.7, // Lower refund rate is good
};

export function SalesAnalytics({ organizationId, productIds }: SalesAnalyticsProps) {
  const [isLoading, setIsLoading] = useState(false);

  // In real implementation, fetch metrics based on organizationId and productIds
  useEffect(() => {
    // Simulate API call
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [organizationId, productIds]);

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatChange = (change: number) => {
    return `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const salesMetrics = [
    {
      title: "Total de Pedidos",
      value: mockSalesMetrics.totalOrders.toLocaleString('pt-BR'),
      change: formatChange(mockSalesMetrics.monthlyGrowth),
      isPositive: mockSalesMetrics.monthlyGrowth > 0,
      icon: ShoppingCartIcon,
      description: "Pedidos realizados",
      badge: {
        text: "Total",
        variant: "default" as const,
      },
    },
    {
      title: "Receita de Hoje",
      value: formatCurrency(mockSalesMetrics.todayRevenue),
      change: formatChange(mockSalesMetrics.todayGrowth),
      isPositive: mockSalesMetrics.todayGrowth > 0,
      icon: CalendarIcon,
      description: "Receita do dia atual",
      badge: {
        text: "Hoje",
        variant: "secondary" as const,
      },
    },
    {
      title: "Receita Total",
      value: formatCurrency(mockSalesMetrics.totalRevenue),
      change: formatChange(mockSalesMetrics.monthlyGrowth),
      isPositive: mockSalesMetrics.monthlyGrowth > 0,
      icon: DollarSignIcon,
      description: "Receita acumulada",
      badge: {
        text: "Total",
        variant: "outline" as const,
      },
    },
    {
      title: "Ticket Médio",
      value: formatCurrency(mockSalesMetrics.averageOrderValue),
      change: "+1.1%",
      isPositive: true,
      icon: TrendingUpIcon,
      description: "Valor médio por pedido",
      badge: {
        text: "Médio",
        variant: "secondary" as const,
      },
    },
    {
      title: "Taxa de Conversão",
      value: formatPercentage(mockSalesMetrics.conversionRate),
      change: formatChange(mockSalesMetrics.conversionGrowth),
      isPositive: mockSalesMetrics.conversionGrowth > 0,
      icon: UsersIcon,
      description: "Visitantes que compram",
      badge: {
        text: "Performance",
        variant: "default" as const,
      },
    },
    {
      title: "Taxa de Reembolso",
      value: formatPercentage(mockSalesMetrics.refundRate),
      change: formatChange(mockSalesMetrics.refundGrowth),
      isPositive: mockSalesMetrics.refundGrowth < 0, // Lower refund rate is good
      icon: ArrowDownIcon,
      description: "Taxa de reembolsos",
      badge: {
        text: "Baixa",
        variant: "outline" as const,
      },
    },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {salesMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <Card key={index} className="relative overflow-hidden hover:shadow-md transition-all duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {metric.title}
                  </CardTitle>
                  <Badge variant={metric.badge.variant} className="text-xs">
                    {metric.badge.text}
                  </Badge>
                </div>
                <div className="relative">
                  <IconComponent className="h-4 w-4 text-muted-foreground" />
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary/20 rounded-full animate-pulse" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">
                    {metric.value}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {metric.description}
                  </div>
                </div>

                {/* Growth indicator */}
                <div className="mt-3 flex items-center gap-1 text-green-600">
                  <ArrowUpRight className="h-3 w-3" />
                  <span className="text-xs font-medium">{metric.change}</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

