"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import {
  TrendingUp,
  TrendingDown,
  Minus,
  LucideIcon,
} from "lucide-react";

export interface MetricCardProps {
  title: string;
  value: string;
  change?: string;
  isPositive?: boolean;
  icon: LucideIcon;
  isLoading?: boolean;
  description?: string;
  badge?: {
    text: string;
    status: "success" | "info" | "warning" | "error";
  };
  variant?: "default" | "gradient" | "minimal" | "green-gradient";
  className?: string;
}

export function MetricCard({
  title,
  value,
  change,
  isPositive,
  icon: Icon,
  isLoading,
  description,
  badge,
  variant = "default",
  className,
}: MetricCardProps) {
  if (isLoading) {
    return (
      <Card className={cn("relative overflow-hidden", className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </CardContent>
      </Card>
    );
  }

  const getChangeIcon = () => {
    if (!change) return <Minus className="h-3 w-3" />;
    return isPositive ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    );
  };

  const getChangeColor = () => {
    if (!change) return "text-muted-foreground";
    if (variant === "green-gradient") {
      return isPositive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
    }
    return isPositive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "gradient":
        return "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground";
      case "green-gradient":
        return "bg-gradient-to-br from-green-500/30 via-green-400/20 to-green-600/30 text-green-800 dark:text-green-200 border-green-300/50 dark:border-green-600/50";
      case "minimal":
        return "border-0 shadow-none bg-muted/50";
      default:
        return "";
    }
  };

  const getTextColor = () => {
    if (variant === "gradient") {
      return "text-primary-foreground";
    }
    if (variant === "green-gradient") {
      return "text-green-800 dark:text-green-200";
    }
    return "";
  };

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-200 hover:shadow-md",
      getVariantStyles(),
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          <CardTitle className={cn(
            "text-sm font-medium",
            variant === "gradient" ? "text-primary-foreground/80" :
            variant === "green-gradient" ? "text-green-700 dark:text-green-300" : "text-muted-foreground"
          )}>
            {title}
          </CardTitle>
          {badge && (
            <Badge
              status={badge.status}
              className={cn(
                "text-xs",
                variant === "gradient" && "bg-primary-foreground/20 text-primary-foreground border-primary-foreground/30"
              )}
            >
              {badge.text}
            </Badge>
          )}
        </div>
        <div className="relative">
          <Icon className={cn(
            "h-4 w-4",
            variant === "gradient" ? "text-primary-foreground/80" :
            variant === "green-gradient" ? "text-green-600 dark:text-green-400" : "text-muted-foreground"
          )} />
          {variant === "default" && (
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary/20 rounded-full animate-pulse" />
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className={cn(
            "text-2xl font-bold",
            getTextColor()
          )}>
            {value}
          </div>
          {description && (
            <p className={cn(
              "text-xs",
              variant === "gradient" ? "text-primary-foreground/70" :
              variant === "green-gradient" ? "text-green-600 dark:text-green-400" : "text-muted-foreground"
            )}>
              {description}
            </p>
          )}
          {change && (
            <div className={cn(
              "flex items-center gap-1 text-xs",
              getChangeColor()
            )}>
              {getChangeIcon()}
              <span>{change}</span>
              <span className={cn(
                variant === "gradient" ? "text-primary-foreground/60" :
                variant === "green-gradient" ? "text-green-500 dark:text-green-500" : "text-muted-foreground"
              )}>
                vs período anterior
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export interface MetricGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: 2 | 3 | 4 | 6;
}

export function MetricGrid({
  children,
  className,
  columns = 3
}: MetricGridProps) {
  const gridCols = {
    2: "md:grid-cols-2",
    3: "md:grid-cols-2 lg:grid-cols-3",
    4: "md:grid-cols-2 lg:grid-cols-4",
    6: "md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",
  };

  return (
    <div className={cn(
      "grid gap-4",
      gridCols[columns],
      className
    )}>
      {children}
    </div>
  );
}
