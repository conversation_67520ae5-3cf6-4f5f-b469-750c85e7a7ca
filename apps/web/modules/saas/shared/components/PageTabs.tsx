"use client";

import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import type { ReactNode } from "react";

export interface PageTab {
  value: string;
  label: string;
  content: ReactNode;
  badge?: string | number;
}

interface PageTabsProps {
  tabs: PageTab[];
  defaultValue?: string;
  className?: string;
}

export function PageTabs({ tabs, defaultValue, className }: PageTabsProps) {
  return (
    <Tabs defaultValue={defaultValue || tabs[0]?.value} className={className}>
      <TabsList className={`inline-flex mb-6 ${tabs.length === 2 ? 'w-auto' : 'w-auto'}`}>
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value} className="relative">
            {tab.label}
            {tab.badge && (
              <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-primary-foreground bg-primary rounded-full">
                {tab.badge}
              </span>
            )}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value} className="mt-0">
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
}
