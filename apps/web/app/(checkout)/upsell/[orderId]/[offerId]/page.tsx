'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { apiClient } from '@shared/lib/api-client';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Separator } from '@ui/components/separator';
import { CheckCircle, Clock, CreditCard, X } from 'lucide-react';
import { toast } from 'sonner';
import { PaymentForm } from '../../../checkout/components/simple-payment-form';
import { OneClickPurchase } from '../../../components/one-click-purchase';
import { formatCurrency } from '@lib/utils';

interface UpsellPageProps {}

export default function UpsellPage({}: UpsellPageProps) {
  const params = useParams();
  const router = useRouter();
  const orderId = params.orderId as string;
  const offerId = params.offerId as string;

  const [isProcessing, setIsProcessing] = useState(false);
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes countdown

  // Get the offer details
  const { data: offer, isLoading: isOfferLoading } = apiClient.offers.getNextOffer.useQuery(
    { orderId },
    {
      enabled: !!orderId,
      retry: 1,
    }
  );

  // Get the original order to check for stored credit card data
  const { data: originalOrder } = apiClient.orders.getOrder.useQuery(
    { orderId },
    {
      enabled: !!orderId,
      retry: 1,
    }
  );

  // Track that the offer was viewed
  const trackInteractionMutation = apiClient.offers.trackInteraction.useMutation();
  const processOfferPurchaseMutation = apiClient.offers.processOfferPurchase.useMutation();

  // Track view on component mount
  useEffect(() => {
    if (orderId && offerId) {
      trackInteractionMutation.mutate({
        orderId,
        offerId,
        action: 'VIEWED',
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        },
      });
    }
  }, [orderId, offerId]);

  // Countdown timer
  useEffect(() => {
    if (timeLeft <= 0) {
      handleReject();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleReject = async () => {
    try {
      await trackInteractionMutation.mutateAsync({
        orderId,
        offerId,
        action: 'REJECTED',
        metadata: {
          timestamp: new Date().toISOString(),
          timeSpent: 600 - timeLeft,
        },
      });

      // Navigate to success page - let the success page handle downsells
      router.push(`/checkout/success?orderId=${orderId}`);
    } catch (error) {
      console.error('Error rejecting offer:', error);
      router.push(`/checkout/success?orderId=${orderId}`);
    }
  };

  const handlePurchase = async (paymentData: any) => {
    setIsProcessing(true);
    try {
      const result = await processOfferPurchaseMutation.mutateAsync({
        orderId,
        offerId,
        paymentMethod: paymentData.paymentMethod,
        creditCard: paymentData.creditCard,
      });

      toast.success('Oferta adquirida com sucesso!');

      // Navigate to success page - let the success page handle next offers
      router.push(`/checkout/success?orderId=${result.orderId}`);
    } catch (error: any) {
      console.error('Error processing upsell purchase:', error);
      toast.error(error.message || 'Erro ao processar pagamento');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleOneClickPurchase = async (data: { paymentMethod: 'CREDIT_CARD' | 'PIX' | 'BOLETO' }) => {
    setIsProcessing(true);
    try {
      const result = await processOfferPurchaseMutation.mutateAsync({
        orderId,
        offerId,
        paymentMethod: data.paymentMethod,
        // Don't pass creditCard data - let the backend reuse stored data
      });

      toast.success('Oferta adquirida com sucesso!');

      // Navigate to success page - let the success page handle next offers
      router.push(`/checkout/success?orderId=${result.orderId}`);
    } catch (error: any) {
      console.error('Error processing one-click upsell purchase:', error);
      toast.error(error.message || 'Erro ao processar pagamento');
    } finally {
      setIsProcessing(false);
    }
  };

  if (isOfferLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!offer) {
    router.push(`/checkout/success?orderId=${orderId}`);
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header with countdown */}
      <div className="bg-red-600 text-white py-3">
        <div className="container mx-auto px-4 flex items-center justify-center gap-2">
          <Clock className="h-5 w-5" />
          <span className="font-semibold">
            Oferta especial expira em: {formatTime(timeLeft)}
          </span>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Success message */}
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <h2 className="text-xl font-semibold text-green-800">
                    Parabéns! Seu pedido foi confirmado
                  </h2>
                  <p className="text-green-600">
                    Agora você tem acesso a uma oferta especial exclusiva
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Offer Details */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Badge className="mb-2 bg-red-600 text-white">
                    OFERTA ESPECIAL
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReject}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <CardTitle className="text-2xl">{offer.title}</CardTitle>
                <CardDescription className="text-lg">
                  {offer.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {(offer as any).targetProduct && (
                  <div className="mb-6">
                    <h4 className="font-semibold mb-2">O que você vai receber:</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium">{(offer as any).targetProduct.title}</h5>
                      <p className="text-sm text-gray-600 mt-1">
                        {(offer as any).targetProduct.description}
                      </p>
                    </div>
                  </div>
                )}

                <Separator className="my-6" />

                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {formatCurrency(Number(offer.price))}
                  </div>
                  <p className="text-sm text-gray-600">
                    Oferta válida apenas por tempo limitado
                  </p>
                </div>

                <div className="mt-6 space-y-3">
                  <Button
                    onClick={handleReject}
                    variant="outline"
                    className="w-full"
                    disabled={isProcessing}
                  >
                    Não, obrigado
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Payment Form */}
            <div>
              {/* Check if original order has stored credit card data */}
              {(originalOrder?.paymentData as any)?.creditCardData ? (
                <OneClickPurchase
                  amount={Number(offer.price)}
                  onSubmit={handleOneClickPurchase}
                  isLoading={isProcessing}
                  submitButtonText="Garantir Oferta Agora"
                  hasStoredCard={true}
                  showPaymentOptions={true}
                />
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Finalizar Compra
                    </CardTitle>
                    <CardDescription>
                      Complete o pagamento para garantir sua oferta especial
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <PaymentForm
                      amount={Number(offer.price)}
                      onSubmit={handlePurchase}
                      isLoading={isProcessing}
                      submitButtonText="Garantir Oferta Agora"
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
