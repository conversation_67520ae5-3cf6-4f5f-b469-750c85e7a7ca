# Sistema de Checkout Dinâmico

Este documento descreve o sistema de checkout completamente dinâmico implementado, onde todas as configurações feitas na página de configuração do produto são refletidas automaticamente na página de checkout.

## Componentes Implementados

### 1. Componentes de Renderização

#### CheckoutBanner
- **Localização**: `components/CheckoutBanner.tsx`
- **Props**: `settings: { enabled, url, maxHeight, borderRadius, shadow }`
- **Funcionalidade**: Renderiza banner se habilitado e URL disponível, com estilos dinâmicos

#### CheckoutHeader
- **Localização**: `components/checkout-header.tsx`
- **Props**: `settings: { showLogo, logoUrl, companyName }`
- **Funcionalidade**: Mostra logo se habilitado e exibe nome da empresa

#### UrgencyBar
- **Localização**: `components/urgency-bar.tsx`
- **Props**: `settings: { enabled, message, endTime, backgroundColor, textColor, accentColor }`
- **Funcionalidade**: Contador regressivo em tempo real com cores dinâmicas

#### TrustBadges
- **Localização**: `components/trust-badges.tsx`
- **Props**: `settings: { enabled, badges, layout, showDescriptions, backgroundColor, textColor, borderColor }`
- **Funcionalidade**: Renderiza badges habilitados com layout responsivo

#### GuaranteeCards
- **Localização**: `components/guarantee-cards.tsx`
- **Props**: `settings: { enabled, cards, layout, backgroundColor, textColor, borderColor }`
- **Funcionalidade**: Cards ordenados por `order` com suporte a ícones customizados

#### ScarcityIndicator
- **Localização**: `components/scarcity-indicator.tsx`
- **Props**: `settings: { enabled, totalStock, soldCount, message, variant, showIcon, backgroundColor, textColor, borderColor }`
- **Funcionalidade**: Barra de progresso visual com cálculo automático de vagas restantes

#### Testimonials
- **Localização**: `components/testimonials.tsx`
- **Props**: `settings: { enabled, testimonials, maxTestimonials, autoPlay, autoPlayInterval, showControls, showStars, showAvatars, backgroundColor, textColor, borderColor }`
- **Funcionalidade**: Carrossel automático com controles de navegação e avatares opcionais

#### CheckoutSidebar
- **Localização**: `components/checkout-sidebar.tsx`
- **Props**: `settings: { enabled, bannerUrl, title, content, backgroundColor, textColor, borderColor, borderRadius, shadow }`
- **Funcionalidade**: Sidebar com banner opcional e conteúdo personalizado

### 2. Componentes de Upload

#### TestimonialAvatarUpload
- **Localização**: `components/testimonial-avatar-upload.tsx`
- **Hook**: `useFileUpload` com bucket `'testimonialAvatars'`
- **Funcionalidade**: Preview da imagem atual e upload de novos avatares

#### SidebarBannerUpload
- **Localização**: `components/sidebar-banner-upload.tsx`
- **Hook**: `useFileUpload` com bucket `'checkoutBanners'`
- **Funcionalidade**: Preview do banner atual e upload de novos banners

## Estrutura de Dados

### CheckoutSettingsData Interface

```typescript
interface CheckoutSettingsData {
  banner: {
    enabled: boolean;
    url: string | null;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };
  header: {
    showLogo: boolean;
    logoUrl: string | null;
    companyName: string;
  };
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: Date;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  guaranteeCards: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}
```

## Layout da Página de Checkout

A página de checkout agora usa um layout de 3 colunas:

```jsx
<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
  <div className="lg:col-span-2 space-y-6">
    <CheckoutBanner settings={bannerSettings} />
    <TrustBadges settings={trustBadgesSettings} />
    <GuaranteeCards settings={guaranteeCardsSettings} />
    <ScarcityIndicator settings={scarcitySettings} />
    <Testimonials settings={testimonialsSettings} />
    <CheckoutForm {...} />
  </div>
  <div className="lg:col-span-1">
    <CheckoutSidebar settings={sidebarSettings} />
  </div>
</div>
```

## Sistema de Upload

- **Buckets**: `checkoutBanners`, `testimonialAvatars`
- **API**: `/api/uploads/signed-upload-url`
- **Hooks**: `useFileUpload` para upload de arquivos
- **Configuração**: Suporte a PNG, JPG, WebP com limites de tamanho

## Funcionalidades Implementadas

✅ **Banner do Checkout** - Upload e configuração dinâmica
✅ **Header** - Logo e nome da empresa configuráveis
✅ **Urgência** - Contador regressivo com cores personalizáveis
✅ **Trust Badges** - Badges de confiança com layouts responsivos
✅ **Cards de Garantia** - Cards personalizáveis com ícones
✅ **Escassez** - Indicador de estoque com barra de progresso
✅ **Depoimentos** - Carrossel com upload de avatar
✅ **Sidebar** - Conteúdo personalizado com upload de banner
✅ **Sistema de Upload** - Upload de imagens para todos os elementos
✅ **Layout Responsivo** - Design mobile-first
✅ **Fallbacks** - Valores padrão para configurações não definidas

## Como Usar

1. Configure as opções na página de configuração do produto
2. Faça upload das imagens necessárias (banners, avatares)
3. As configurações são automaticamente refletidas no checkout
4. Teste o checkout para verificar se tudo está funcionando corretamente

## Próximos Passos

- Implementar testes automatizados
- Adicionar mais opções de personalização
- Otimizar performance com lazy loading
- Adicionar analytics para conversão
