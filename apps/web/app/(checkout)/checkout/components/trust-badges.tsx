'use client';

import { Shield, Lock, Mail, CheckCircle, Star, Award, Truck, CreditCard } from 'lucide-react';

interface TrustBadge {
  id: string;
  title: string;
  subtitle: string;
  icon: 'shield' | 'lock' | 'mail' | 'check' | 'star' | 'award' | 'truck' | 'credit-card';
  enabled: boolean;
}

interface TrustBadgesProps {
  settings: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

const iconMap = {
  shield: Shield,
  lock: Lock,
  mail: Mail,
  check: CheckCircle,
  star: Star,
  award: Award,
  truck: Truck,
  'credit-card': CreditCard
};

export function TrustBadges({ settings }: TrustBadgesProps) {
  if (!settings.enabled) return null;

  const enabledBadges = settings.badges.filter(badge => badge.enabled);

  if (enabledBadges.length === 0) return null;

  const getLayoutClasses = () => {
    switch (settings.layout) {
      case 'vertical':
        return 'flex flex-col space-y-3';
      case 'grid':
        return 'grid grid-cols-2 gap-3';
      case 'horizontal':
      default:
        return 'flex flex-wrap gap-3 justify-center';
    }
  };

  return (
    <div className="w-full py-4">
      <div className={`${getLayoutClasses()}`}>
        {enabledBadges.map((badge) => {
          const IconComponent = iconMap[badge.icon as keyof typeof iconMap] || Shield;

          return (
            <div
              key={badge.id}
              className={`flex items-center gap-3 ${settings.backgroundColor} ${settings.borderColor} border rounded-lg px-4 py-3 min-w-0 flex-1`}
            >
              <div className={`flex-shrink-0 ${settings.textColor}`}>
                <IconComponent className="h-5 w-5" />
              </div>
              <div className="min-w-0 flex-1">
                <div className={`font-semibold text-sm ${settings.textColor}`}>
                  {badge.title}
                </div>
                {settings.showDescriptions && (
                  <div className={`text-xs ${settings.textColor} opacity-80`}>
                    {badge.subtitle}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
