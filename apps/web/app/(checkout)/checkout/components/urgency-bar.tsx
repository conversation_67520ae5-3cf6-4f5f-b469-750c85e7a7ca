'use client';

import { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

interface UrgencyBarProps {
  settings: {
    enabled: boolean;
    message: string;
    endTime?: string;
    duration?: number;
    unit?: 'minutes' | 'hours' | 'days';
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };
}

export function UrgencyBar({ settings }: UrgencyBarProps) {
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (!settings.enabled) return;

    const updateTimer = () => {
      let targetTime: number;
      
      if (settings.endTime) {
        // Converter string para Date
        targetTime = new Date(settings.endTime).getTime();
      } else if (settings.duration && settings.unit) {
        // Calcular baseado na duração e unidade
        const now = new Date().getTime();
        const durationMs = settings.duration * (
          settings.unit === 'minutes' ? 60 * 1000 :
          settings.unit === 'hours' ? 60 * 60 * 1000 :
          settings.unit === 'days' ? 24 * 60 * 60 * 1000 : 60 * 1000
        );
        targetTime = now + durationMs;
      } else {
        // Fallback: 15 minutos
        const now = new Date().getTime();
        targetTime = now + (15 * 60 * 1000);
      }

      const now = new Date().getTime();
      const difference = targetTime - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        setTimeLeft(formattedTime);
      } else {
        setTimeLeft('00:00:00');
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [settings.enabled, settings.endTime, settings.duration, settings.unit]);

  if (!settings.enabled) return null;

  return (
    <div className={`w-full ${settings.backgroundColor} shadow-sm`}>
      <div className="container max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-center py-3">
          <div className="flex items-center gap-3">
           
            <span className="text-2xl font-bold text-red-600 font-mono">
              {timeLeft}
            </span>
             <Clock className="h-6 w-6 text-red-600" />
            <span className="text-sm font-semibold text-red-600">
              Oferta por tempo limitado
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
