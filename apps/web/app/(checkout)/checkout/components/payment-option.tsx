// components/payment-option.tsx

import { CreditCard } from 'lucide-react';
import { FaBarcode, FaPix } from 'react-icons/fa6';
import { RadioGroupItem } from '@ui/components/radio-group';
import { cn } from '@ui/lib';

interface PaymentOptionProps {
	value: string;
	title: string;
	description?: string;
	icon: 'credit-card' | 'pix' | 'boleto';
	selected: boolean;
}

export function PaymentOption({
	value,
	title,
	description,
	icon,
	selected,
}: PaymentOptionProps) {
	const renderIcon = () => {
		switch (icon) {
			case 'credit-card':
				return <CreditCard className='h-5 w-5' />;
			case 'pix':
				return <FaPix className='h-5 w-5' />;

			case 'boleto':
				return <FaBarcode className='h-5 w-5' />;
			default:
				return <CreditCard className='h-5 w-5' />;
		}
	};

	return (
		<label
			className={cn(
				'relative flex cursor-pointer rounded-lg border p-3 md:p-4 transition-all',
				selected ? 'border-primary bg-primary/5' : 'hover:border-primary/50'
			)}
		>
			<RadioGroupItem value={value} className='sr-only' />

			<div className='flex flex-col md:flex-row items-center gap-3 w-full'>
				<div
					className={cn(
						'flex h-8 md:h-10 w-8 md:w-10 items-center justify-center rounded-full',
						selected ? 'bg-primary text-primary-foreground' : 'bg-muted'
					)}
				>
					{renderIcon()}
				</div>

				<div className='flex-1'>
					<p className='font-medium leading-none text-sm md:text-base	'>
						{title}
					</p>
					{/* {description && (
						<p className='mt-1 text-sm text-muted-foreground'>{description}</p>
					)} */}
				</div>
			</div>
		</label>
	);
}
