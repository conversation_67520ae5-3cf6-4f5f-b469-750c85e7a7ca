// Opções de copywriting para orderbumps
// Baseado em princípios de conversão e psicologia de vendas

export const orderBumpCopywritingOptions = {
	// Opções de títulos principais
	titles: [
		{
			id: 'urgency',
			text: '🔥 Não perca estas ofertas exclusivas:',
			description: 'Foca em urgência e exclusividade',
			psychology: 'FOMO (Fear of Missing Out)'
		},
		{
			id: 'value',
			text: '💰 Aproveite estes descontos incríveis:',
			description: 'Foca no valor e economia',
			psychology: 'Aversão à perda'
		},
		{
			id: 'personal',
			text: '🎯 Produtos selecionados especialmente para você:',
			description: 'Foca na personalização',
			psychology: 'Relevância pessoal'
		},
		{
			id: 'bonus',
			text: '🎁 Bônus exclusivos para sua compra:',
			description: 'Foca em bônus e extras',
			psychology: 'Percepção de valor agregado'
		},
		{
			id: 'limited',
			text: '⏰ Ofertas limitadas - apenas hoje:',
			description: 'Foca em escassez temporal',
			psychology: 'Escassez e urgência'
		},
		{
			id: 'social',
			text: '👥 Mais de 10.000 pessoas já aproveitaram:',
			description: 'Foca em prova social',
			psychology: 'Prova social e conformidade'
		},
		{
			id: 'simple',
			text: 'Adicione estes produtos ao seu pedido:',
			description: 'Simples e direto',
			psychology: 'Clareza e simplicidade'
		}
	],

	// Opções de subtítulos
	subtitles: [
		{
			id: 'value',
			text: 'Produtos selecionados especialmente para você com desconto especial',
			description: 'Foca na personalização e desconto'
		},
		{
			id: 'urgency',
			text: 'Estas ofertas não estarão mais disponíveis após o pagamento',
			description: 'Cria urgência temporal'
		},
		{
			id: 'benefit',
			text: 'Aumente o valor do seu pedido com produtos complementares',
			description: 'Foca no benefício para o cliente'
		},
		{
			id: 'exclusive',
			text: 'Ofertas exclusivas disponíveis apenas durante o checkout',
			description: 'Enfatiza exclusividade'
		},
		{
			id: 'savings',
			text: 'Economize até 90% comprando junto com seu pedido principal',
			description: 'Foca na economia'
		},
		{
			id: 'recommendation',
			text: 'Recomendamos estes produtos baseado no que você está comprando',
			description: 'Foca na recomendação personalizada'
		}
	],

	// Opções para textos dos cards
	cardTexts: {
		// Headers dos cards
		headers: [
			'SIM, QUERO ESTA OFERTA ESPECIAL!',
			'APROVEITE ESTA OPORTUNIDADE!',
			'EU ACEITO ESTA OFERTA!',
			'QUERO ESTE DESCONTO!',
			'ADICIONAR AO MEU PEDIDO!'
		],

		// Textos do checkbox
		checkboxTexts: {
			unchecked: [
				'Clique para adicionar',
				'Adicionar ao pedido',
				'Incluir na compra',
				'Quero este produto',
				'Adicionar agora'
			],
			checked: [
				'✓ Adicionado ao pedido',
				'✓ Produto incluído',
				'✓ Adicionado com sucesso',
				'✓ Incluído na compra',
				'✓ Produto selecionado'
			]
		}
	},

	// Configurações por tipo de produto
	productTypeConfigs: {
		COURSE: {
			title: '🎓 Complete seu aprendizado com estes bônus:',
			subtitle: 'Produtos complementares para maximizar seus resultados',
			psychology: 'Completude e maximização de resultados'
		},
		MENTORING: {
			title: '🚀 Acelere seus resultados com estes extras:',
			subtitle: 'Ferramentas e recursos para potencializar seu sucesso',
			psychology: 'Aceleração e potencialização'
		},
		EBOOK: {
			title: '📚 Amplie seu conhecimento com estes materiais:',
			subtitle: 'Conteúdos adicionais para aprofundar seus estudos',
			psychology: 'Aprofundamento e expansão'
		}
	},

	// Configurações por posição no checkout
	positionConfigs: {
		'before-payment': {
			title: '🔥 Não perca estas ofertas exclusivas:',
			subtitle: 'Produtos selecionados especialmente para você com desconto especial',
			psychology: 'Última chance antes do pagamento'
		},
		'after-payment': {
			title: '⏰ Última chance - ofertas especiais:',
			subtitle: 'Estas ofertas não estarão mais disponíveis após o pagamento',
			psychology: 'Urgência final'
		},
		'sidebar': {
			title: '💡 Recomendamos para você:',
			subtitle: 'Produtos que outros clientes também compraram',
			psychology: 'Recomendação e prova social'
		}
	}
};

// Função para obter copywriting baseado no contexto
export function getCopywritingForContext(
	productType?: string,
	position?: string,
	selectedTitle?: string,
	selectedSubtitle?: string
) {
	const config = orderBumpCopywritingOptions;

	// Se títulos específicos foram fornecidos, use-os
	if (selectedTitle && selectedSubtitle) {
		return {
			title: selectedTitle,
			subtitle: selectedSubtitle
		};
	}

	// Use configuração baseada na posição
	if (position && config.positionConfigs[position as keyof typeof config.positionConfigs]) {
		return config.positionConfigs[position as keyof typeof config.positionConfigs];
	}

	// Use configuração baseada no tipo de produto
	if (productType && config.productTypeConfigs[productType as keyof typeof config.productTypeConfigs]) {
		return config.productTypeConfigs[productType as keyof typeof config.productTypeConfigs];
	}

	// Fallback para configuração padrão
	return {
		title: config.titles[0].text,
		subtitle: config.subtitles[0].text
	};
}

// Função para obter texto aleatório do array
export function getRandomText(textArray: string[]): string {
	return textArray[Math.floor(Math.random() * textArray.length)];
}

// Função para personalizar textos baseado no comportamento do usuário
export function getPersonalizedCopywriting(
	userBehavior: 'first-time' | 'returning' | 'high-value',
	timeOfDay: 'morning' | 'afternoon' | 'evening'
) {
	const config = orderBumpCopywritingOptions;

	switch (userBehavior) {
		case 'first-time':
			return {
				title: '🎁 Bem-vindo! Aproveite estas ofertas especiais:',
				subtitle: 'Ofertas exclusivas para novos clientes'
			};
		case 'returning':
			return {
				title: '👋 Que bom te ver novamente! Confira estas ofertas:',
				subtitle: 'Produtos selecionados baseado no seu histórico'
			};
		case 'high-value':
			return {
				title: '💎 Ofertas premium especialmente para você:',
				subtitle: 'Produtos de alto valor com desconto exclusivo'
			};
		default:
			return {
				title: config.titles[0].text,
				subtitle: config.subtitles[0].text
			};
	}
}
