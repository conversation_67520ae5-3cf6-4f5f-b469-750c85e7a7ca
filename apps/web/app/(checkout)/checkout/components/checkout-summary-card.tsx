import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { formatCurrency } from '@lib/utils';
import { Clock, Package, Star, Tag, Users, Zap, ChevronDown, FileText } from 'lucide-react';
import Image from 'next/image';
// Type import moved to local definition to avoid database bundling

import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from '@ui/components/collapsible';
import { cn } from '@ui/lib';
import { useState } from 'react';
import { CouponForm } from './coupon-form';

interface ProductSummaryProps {
	product: any; // Usando any para evitar problemas de tipagem
	selectedBumps: string[];
	onBumpChange?: (ids: string[]) => void;
	isMobile: boolean;
	appliedCoupon?: {
		code: string;
		discountAmount: number;
		finalPrice: number;
	} | null;
	onCouponApplied?: (coupon: {
		code: string;
		discountAmount: number;
		finalPrice: number;
	}) => void;
}

export function ProductSummaryCard({
	product,
	selectedBumps,
	onBumpChange,
	isMobile,
	appliedCoupon,
	onCouponApplied,
}: ProductSummaryProps) {
	const [isOpen, setIsOpen] = useState(false);

	const calculateTotal = () => {
		// Se tiver cupom aplicado, usar o preço final do cupom
		if (appliedCoupon) {
			return appliedCoupon.finalPrice;
		}

		// Caso contrário, calcular normalmente
		let total = product.price;
		if (selectedBumps.length > 0 && product.offers) {
			total += product.offers
				.filter((o) => selectedBumps.includes(o.id))
				.reduce((acc, offer) => acc + offer.price, 0);
		}
		return total;
	};

	const MobileHeader = () => (
		<div className='flex items-center justify-between w-full  '>
			<div className='flex items-center gap-3'>
				{product.thumbnail ? (
					<Image
						src={product.thumbnail}
						alt={product.title}
						width={55}
						height={55}
						className='w-10 h-10  object-cover'
					/>
				) : (
					<div className='h-12 w-12 rounded-md bg-muted flex items-center justify-center'>
						<FileText className='h-6 w-6 text-muted-foreground' />
					</div>
				)}
				<div className='py-3 mt-2 text-left  '>
					<h3 className='font-medium text-sm line-clamp-1'>{product.title}</h3>
					<div className='flex items-center gap-2'>
						{product.regularPrice && product.regularPrice > product.price && (
							<>
								<span className='text-sm text-muted-foreground line-through'>
									R$ {product.regularPrice.toFixed(2)}
								</span>
								<span className='px-1.5 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded'>
									{Math.round(((product.regularPrice - product.price) / product.regularPrice) * 100)}% OFF
								</span>
							</>
						)}
						<div className='text-lg font-bold text-primary'>
							R$ {calculateTotal().toFixed(2)}
						</div>
					</div>
				</div>
			</div>
			<ChevronDown
				className={cn('h-5 w-5 text-muted-foreground transition-transform', {
					'transform rotate-180': isOpen,
				})}
			/>
		</div>
	);

	const Content = () => (
		<div className='space-y-4'>
			{!isMobile && (
				<div className='flex items-center gap-4'>
					{product.thumbnail ? (
						<Image
							src={product.thumbnail}
							alt={product.title}
							width={84}
							height={84}
							className='rounded-sm object-cover'
						/>
					) : (
						<div className='h-16 w-16 rounded-lg bg-muted flex items-center justify-center'>
							<FileText className='h-8 w-8 text-muted-foreground' />
						</div>
					)}
					<div>
						<h3 className='font-medium text-base'>{product.title}</h3>
						{product.regularPrice && product.regularPrice > product.price && (
							<div className='flex items-center gap-2 mt-1'>
								<span className='text-sm text-muted-foreground line-through'>
									R$ {product.regularPrice.toFixed(2)}
								</span>
								<span className='px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded'>
									{Math.round(((product.regularPrice - product.price) / product.regularPrice) * 100)}% OFF
								</span>
							</div>
						)}
					</div>
				</div>
			)}

			<div className='rounded-lg bg-muted p-4 text-sm text-muted-foreground'>
				{product.type === 'COURSE' && (
					<p>Acesso imediato após a confirmação do pagamento</p>
				)}
				{product.type === 'MENTORING' && (
					<p>Você receberá as instruções de agendamento por email</p>
				)}
				{product.type === 'EBOOK' && (
					<p>Download disponível após a confirmação do pagamento</p>
				)}
			</div>

			<div className='space-y-4'>
				{/* Formulário de Cupom */}
				<div className='mb-4'>
					<CouponForm
						productId={product.id}
						onCouponApplied={onCouponApplied}
					/>
				</div>

				<Separator />

				{/* Mostrar preço original e desconto se houver cupom aplicado */}
				{appliedCoupon && appliedCoupon.discountAmount > 0 && (
					<>
						<div className='flex justify-between text-sm text-muted-foreground'>
							<span>Subtotal</span>
							<span>
								R${' '}
								{(calculateTotal() + appliedCoupon.discountAmount).toFixed(2)}
							</span>
						</div>
						<div className='flex justify-between text-sm text-green-600'>
							<span>Desconto ({appliedCoupon.code})</span>
							<span>- R$ {appliedCoupon.discountAmount.toFixed(2)}</span>
						</div>
					</>
				)}

				<div className='flex justify-between text-lg font-medium'>
					<span>Total</span>
					<span>R$ {calculateTotal().toFixed(2)}</span>
				</div>
			</div>
		</div>
	);

	if (isMobile) {
		return (
			<Card className='bg-white shadow-sm  '>
				<Collapsible open={isOpen} onOpenChange={setIsOpen}>
					<CollapsibleTrigger asChild>
						<div className='w-full px-3   rounded-none cursor-pointer'>
							<MobileHeader />
						</div>
					</CollapsibleTrigger>
					<CollapsibleContent>
						<CardContent className='px-4 pb-4 pt-0'>
							<Content />
						</CardContent>
					</CollapsibleContent>
				</Collapsible>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader className='p-6'>
				<CardTitle>Resumo do Pedido</CardTitle>
			</CardHeader>
			<CardContent className='p-6 pt-0'>
				<Content />
			</CardContent>
		</Card>
	);
}
