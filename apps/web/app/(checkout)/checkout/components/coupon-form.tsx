'use client';

import { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Ticket, Loader2, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { FormField } from './form-field';
import { CheckoutFormData } from './types';

interface CouponFormProps {
	productId: string;
	onCouponApplied?: (discount: {
		code: string;
		discountAmount: number;
		finalPrice: number;
	}) => void;
}

export function CouponForm({ productId, onCouponApplied }: CouponFormProps) {
	const [couponStatus, setCouponStatus] = useState<
		'idle' | 'valid' | 'invalid'
	>('idle');
	const [couponMessage, setCouponMessage] = useState('');
	const [appliedCoupon, setAppliedCoupon] = useState<{
		code: string;
		discountAmount: number;
		finalPrice: number;
	} | null>(null);

	const { control, setValue, getValues } = useFormContext<CheckoutFormData>();
	const [couponCode, setCouponCode] = useState('');

	const [isValidating, setIsValidating] = useState(false);

	const validateCoupon = async () => {
		const currentCouponCode = getValues('couponCode') || couponCode;
		if (!currentCouponCode || typeof currentCouponCode !== 'string' || !currentCouponCode.trim()) {
			setCouponStatus('invalid');
			setCouponMessage('Digite um código de cupom');
			return;
		}

		setIsValidating(true);
		setCouponStatus('idle');
		setCouponMessage('');

		try {
			// Usar o endpoint de API normal do Next.js
			const response = await fetch('/api/validate-coupon', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					code: currentCouponCode,
					productId
				}),
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error('Error response:', errorText);
				throw new Error(`Erro HTTP: ${response.status}`);
			}

			let result;
			try {
				result = await response.json();
				console.log('Coupon validation result:', result);
			} catch (parseError) {
				const responseText = await response.text();
				console.error('Failed to parse JSON response:', responseText);
				throw new Error('Resposta inválida do servidor');
			}

			// Verificar se há erro na resposta
			if (result.error) {
				setCouponStatus('invalid');
				setCouponMessage(result.error);
				setAppliedCoupon(null);
				return;
			}

			if (result.isValid && result.discount) {
				setCouponStatus('valid');
				setCouponMessage(
					`Cupom aplicado! ${
						result.discount.type === 'PERCENTAGE'
							? `${result.discount.value}% de desconto`
							: `R$ ${result.discount.value.toFixed(2)} de desconto`
					}`
				);
				setAppliedCoupon({
					code: result.discount.code,
					discountAmount: result.discount.discountAmount,
					finalPrice: result.discount.finalPrice,
				});

				// Notify parent component
				if (onCouponApplied) {
					onCouponApplied({
						code: result.discount.code,
						discountAmount: result.discount.discountAmount,
						finalPrice: result.discount.finalPrice,
					});
				}
			} else {
				setCouponStatus('invalid');
				setCouponMessage(result.message || 'Cupom inválido');
				setAppliedCoupon(null);
			}
		} catch (error: any) {
			console.error('Error validating coupon:', error);
			setCouponStatus('invalid');

			// Melhor tratamento de erro
			let errorMessage = 'Erro ao validar cupom';
			if (error?.message?.includes('404')) {
				errorMessage = 'Serviço de validação indisponível. Tente novamente.';
			} else if (error?.message) {
				errorMessage = error.message;
			}

			setCouponMessage(errorMessage);
			setAppliedCoupon(null);
		} finally {
			setIsValidating(false);
		}
	};

	const removeCoupon = () => {
		setValue('couponCode', '');
		setCouponCode('');
		setCouponStatus('idle');
		setCouponMessage('');
		setAppliedCoupon(null);

		// Notify parent component
		if (onCouponApplied) {
			onCouponApplied({
				code: '',
				discountAmount: 0,
				finalPrice: 0,
			});
		}
	};

	return (
		<div className='space-y-2'>
			<div className='flex items-center gap-2'>
				<Controller
					name='couponCode'
					control={control}
					render={({ field }) => (
						<FormField
							label='Cupom de desconto'
							name='couponCode'
							error=''
							required={false}
						>
							<div className='flex gap-2'>
								<div className='relative flex-1'>
									<Input
										{...field}
										onChange={(e) => {
											field.onChange(e);
											setCouponCode(e.target.value);
										}}
										placeholder='Digite seu cupom'
										className='pr-10 text-sm'
										disabled={couponStatus === 'valid'}
									/>
									{couponStatus === 'valid' && (
										<CheckCircle className='absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-green-500' />
									)}
									{couponStatus === 'invalid' && (
										<XCircle className='absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-red-500' />
									)}
								</div>
								{couponStatus !== 'valid' ? (
									<Button
										type='button'
										variant='outline'
										onClick={validateCoupon}
										disabled={isValidating}
									>
										{isValidating ? (
											<Loader2 className='h-4 w-4 animate-spin mr-2' />
										) : (
											<Ticket className='h-4 w-4 mr-2' />
										)}
										Aplicar
									</Button>
								) : (
									<Button
										type='button'
										variant='outline'
										onClick={removeCoupon}
									>
										<XCircle className='h-4 w-4 mr-2' />
										Remover
									</Button>
								)}
							</div>
						</FormField>
					)}
				/>
			</div>
			{couponMessage && (
				<p
					className={`text-sm ${
						couponStatus === 'valid' ? 'text-green-600' : 'text-red-500'
					}`}
				>
					{couponMessage}
				</p>
			)}
		</div>
	);
}
