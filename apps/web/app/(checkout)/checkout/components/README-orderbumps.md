# Orderbumps - Documentação

## Visão Geral

Os orderbumps são ofertas adicionais apresentadas durante o processo de checkout para aumentar o valor médio do pedido (AOV - Average Order Value). Este sistema foi desenvolvido baseado em análises de UX e padrões de conversão comprovados.

## Componentes Disponíveis

### 1. EnhancedOrderBumpCard

Componente principal que renderiza um card de orderbump individual com design moderno e atrativo.

**Características:**
- Design responsivo com hover effects
- 3 variantes: `default`, `premium`, `social`
- Animações suaves e feedback visual
- Preços com desconto destacado
- Checkbox interativo

**Props:**
```typescript
interface EnhancedOrderBumpCardProps {
  offer: Offer;
  checked: boolean;
  onToggle: (checked: boolean) => void;
  variant?: 'default' | 'premium' | 'social';
}
```

### 2. EnhancedOrderBumpList

Lista que gerencia múltiplos orderbumps com título e subtítulo personalizáveis.

**Características:**
- Seleção múltipla de produtos
- Resumo dos itens selecionados
- Título e subtítulo customizáveis
- Detecção automática de variantes baseada no conteúdo

**Props:**
```typescript
interface EnhancedOrderBumpListProps {
  offers: Offer[];
  selected: string[];
  onSelect: (selected: string[]) => void;
  title?: string;
  subtitle?: string;
}
```

### 3. OrderbumpPositioningDemo

Componente para demonstrar diferentes posicionamentos dos orderbumps no checkout.

**Posições disponíveis:**
- `before-payment`: Antes dos métodos de pagamento
- `after-payment`: Após os métodos de pagamento
- `sidebar`: Na sidebar direita
- `floating`: Popup flutuante

## Dados Mock

### mockOrderBumps

Array com 5 ofertas de exemplo para testes:
1. Acesso Permanente + Atualizações (R$ 9,90)
2. Estratégia para Crescer Seguidores (R$ 11,00)
3. Curso de Marketing Digital (R$ 47,00)
4. Consultoria Personalizada (R$ 97,00)
5. Kit de Templates (R$ 27,00)

### generateOrderBumpsForProduct

Função que gera orderbumps específicos baseados no tipo de produto:
- **COURSE**: Oferece acesso vitalício, curso adicional e templates
- **MENTORING**: Oferece acesso vitalício, consultoria e estratégia social
- **EBOOK**: Oferece curso, templates e acesso vitalício

## Integração no Checkout

### Uso Básico

```tsx
import { EnhancedOrderBumpList } from './enhanced-order-bump-list';
import { generateOrderBumpsForProduct } from './mock-orderbumps';

// No componente de checkout
<EnhancedOrderBumpList
  offers={product.offers || generateOrderBumpsForProduct(product.id, product.type)}
  selected={selectedBumps}
  onSelect={setSelectedBumps}
  title="Aproveite e compre junto:"
  subtitle="Ofertas especiais disponíveis apenas durante o checkout"
/>
```

### Posicionamento Estratégico

1. **Antes do Pagamento** (Recomendado)
   - Maior taxa de conversão
   - Cliente ainda está engajado
   - Não interrompe o fluxo de pagamento

2. **Na Sidebar**
   - Sempre visível durante o scroll
   - Não interfere no fluxo principal
   - Ideal para ofertas complementares

3. **Após o Pagamento**
   - Última chance antes da finalização
   - Menor taxa de conversão
   - Use apenas para ofertas muito atrativas

4. **Flutuante**
   - Chama atenção sem interromper
   - Pode ser fechado pelo usuário
   - Use com moderação

## Melhores Práticas

### Design
- Use cores contrastantes para destacar descontos
- Mantenha o texto conciso e direto
- Inclua elementos de urgência (desconto, tempo limitado)
- Use ícones e badges para chamar atenção

### Conteúdo
- Foque no benefício, não na característica
- Use números específicos (ex: "90% OFF", "300 seguidores")
- Inclua prova social quando possível
- Seja claro sobre o que o cliente está comprando

### UX
- Pre-selecione ofertas muito atrativas
- Mostre o valor total atualizado
- Permita fácil remoção de itens
- Mantenha o processo simples

### Posicionamento
- Teste diferentes posições
- Monitore métricas de conversão
- Ajuste baseado no comportamento do usuário
- Considere o tipo de produto

## Métricas Importantes

- **Taxa de Aceitação**: % de usuários que adicionam orderbumps
- **AOV Impact**: Aumento no valor médio do pedido
- **Revenue Lift**: Aumento total na receita
- **Abandonment Rate**: Taxa de abandono após adicionar orderbumps

## Próximos Passos

1. **Integração com API**: Substituir dados mock por dados reais
2. **A/B Testing**: Testar diferentes designs e posicionamentos
3. **Personalização**: Orderbumps baseados no perfil do usuário
4. **Analytics**: Implementar tracking detalhado
5. **Mobile Optimization**: Otimizar para dispositivos móveis

## Exemplo de Uso Completo

```tsx
'use client';

import { useState } from 'react';
import { EnhancedOrderBumpList } from './enhanced-order-bump-list';
import { generateOrderBumpsForProduct } from './mock-orderbumps';

export function CheckoutWithOrderbumps({ product }) {
  const [selectedBumps, setSelectedBumps] = useState<string[]>([]);

  const offers = product.offers?.length > 0
    ? product.offers
    : generateOrderBumpsForProduct(product.id, product.type);

  return (
    <div className="checkout-container">
      {/* Formulário de checkout */}

      {/* Orderbumps */}
      <EnhancedOrderBumpList
        offers={offers}
        selected={selectedBumps}
        onSelect={setSelectedBumps}
        title="Aproveite e compre junto:"
        subtitle="Ofertas especiais disponíveis apenas durante o checkout"
      />

      {/* Botão de pagamento */}
    </div>
  );
}
```
