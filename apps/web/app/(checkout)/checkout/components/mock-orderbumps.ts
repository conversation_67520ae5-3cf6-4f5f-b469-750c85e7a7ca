import { Offer } from './types';

export const mockOrderBumps: Offer[] = [
	{
		id: 'orderbump-1',
		title: 'Acesso Permanente + Atualizações Futuras',
		description: 'Tenha acesso para sempre ao P24H + suas atualizações',
		price: 9.90,
		type: 'ORDER_BUMP',
		thumbnail: '/images/products/lifetime-access.png',
	},
	{
		id: 'orderbump-2',
		title: 'Estratégia para Crescer Seguidores',
		description: 'Adicionar a compra a Estratégia para crescer seguidores toda semana, sem viralizar, sem trends e sem precisar postar conteúdo todo dia.',
		price: 11.00,
		type: 'ORDER_BUMP',
		thumbnail: '/images/products/social-growth.png',
	},
	{
		id: 'orderbump-3',
		title: 'Curso de Marketing Digital Avançado',
		description: 'Aprenda as melhores estratégias de marketing digital para impulsionar seus negócios online.',
		price: 47.00,
		type: 'ORDER_BUMP',
		thumbnail: '/images/products/marketing-course.png',
	},
	{
		id: 'orderbump-4',
		title: 'Consultoria Personalizada 1:1',
		description: 'Sessão individual de 60 minutos para tirar suas dúvidas e acelerar seus resultados.',
		price: 97.00,
		type: 'ORDER_BUMP',
		thumbnail: '/images/products/consulting.png',
	},
	{
		id: 'orderbump-5',
		title: 'Kit de Templates Profissionais',
		description: 'Mais de 50 templates prontos para usar em suas campanhas de marketing.',
		price: 27.00,
		type: 'ORDER_BUMP',
		thumbnail: '/images/products/templates.png',
	},
];

// Função para gerar orderbumps dinâmicos baseados no produto principal
export function generateOrderBumpsForProduct(productId: string, productType: string): Offer[] {
	const baseOffers = [...mockOrderBumps];

	// Personalizar ofertas baseadas no tipo de produto
	switch (productType) {
		case 'COURSE':
			return baseOffers.filter(offer =>
				offer.id === 'orderbump-1' ||
				offer.id === 'orderbump-3' ||
				offer.id === 'orderbump-5'
			);
		case 'MENTORING':
			return baseOffers.filter(offer =>
				offer.id === 'orderbump-1' ||
				offer.id === 'orderbump-4' ||
				offer.id === 'orderbump-2'
			);
		case 'EBOOK':
			return baseOffers.filter(offer =>
				offer.id === 'orderbump-3' ||
				offer.id === 'orderbump-5' ||
				offer.id === 'orderbump-1'
			);
		default:
			return baseOffers.slice(0, 3); // Retorna os 3 primeiros por padrão
	}
}

// Configurações de teste para diferentes cenários
export const orderBumpTestConfigs = {
	premium: {
		title: 'SIM, QUERO O P24H + ATUALIZAÇÕES PRA SEMPRE',
		badge: 'ACESSO VITALÍCIO',
		discount: 90,
		originalPrice: 97.00,
		finalPrice: 9.90,
	},
	social: {
		title: 'SIM, EU ACEITO ESSA OFERTA ESPECIAL!',
		badge: 'CRESCIMENTO GARANTIDO',
		discount: 81,
		originalPrice: 297.00,
		finalPrice: 11.00,
	},
	default: {
		title: 'APROVEITE ESTA OFERTA ESPECIAL!',
		badge: 'OFERTA LIMITADA',
		discount: 50,
		originalPrice: 94.00,
		finalPrice: 47.00,
	},
};
