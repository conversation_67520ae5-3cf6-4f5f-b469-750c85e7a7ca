'use client';

import {
  Shield,
  CheckCircle,
  Mail,
  Award,
  Lock,
  Truck,
  Heart,
  Star,
  Zap,
  Clock
} from 'lucide-react';
import { cn } from '@ui/lib';

interface SecurityCard {
  id: string;
  title: string;
  description: string;
  icon: 'shield' | 'check' | 'mail' | 'award' | 'lock' | 'truck' | 'heart' | 'star' | 'zap' | 'clock';
  enabled: boolean;
  order: number;
}

interface SecurityGuaranteeCardsProps {
  cards: SecurityCard[];
  layout?: 'vertical' | 'horizontal' | 'grid';
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  className?: string;
}

const iconMap = {
  shield: Shield,
  check: CheckCircle,
  mail: Mail,
  award: Award,
  lock: Lock,
  truck: Truck,
  heart: Heart,
  star: Star,
  zap: Zap,
  clock: Clock,
};

export function SecurityGuaranteeCards({
  cards = [],
  layout = 'vertical',
  backgroundColor = 'bg-white',
  textColor = 'text-blue-900',
  borderColor = 'border-blue-200',
  className = ''
}: SecurityGuaranteeCardsProps) {
  const enabledCards = cards
    .filter(card => card.enabled)
    .sort((a, b) => a.order - b.order);

  if (enabledCards.length === 0) {
    return null;
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-4';
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
      default:
        return 'space-y-4';
    }
  };

  return (
    <div className={cn('w-full', getLayoutClasses(), className)}>
      {enabledCards.map((card) => {
        const IconComponent = iconMap[card.icon] || Shield;

        return (
          <div
            key={card.id}
            className={cn(
              'flex items-start gap-3 p-4 rounded-lg border-2',
              backgroundColor,
              textColor,
              borderColor,
              'hover:shadow-sm transition-shadow'
            )}
          >
            {/* Ícone */}
            <div className="flex-shrink-0">
              <IconComponent className="h-6 w-6 text-blue-600" />
            </div>

            {/* Conteúdo */}
            <div className="flex-1 min-w-0">
              <h3 className="font-bold text-sm leading-tight mb-1">
                {card.title}
              </h3>
              <p className="text-xs italic opacity-80 leading-relaxed">
                {card.description}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
}
