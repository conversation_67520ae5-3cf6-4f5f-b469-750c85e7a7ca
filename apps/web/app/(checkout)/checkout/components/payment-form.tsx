// components/payment-form.tsx
import { CreditCard, Loader2, Lock } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';

import { Button } from '@ui/components/button';
import { UrgentPurchaseButton, PixButton, BoletoButton } from '@ui/components/conversion';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { RadioGroup } from '@ui/components/radio-group';
import { PaymentFormPixInfo } from './payment-form-pix-info';
import type { CheckoutFormData } from './types';

import { PaymentOption } from './payment-option';

import { CreditCardForm } from './checkout-credit-card-form';
import { CheckoutFooter } from './checkout-footer';
import { OrderBumpList } from './order-bump-list';
import type { Offer } from './types';
import { PaymentFormBoletoInfo } from './payment-form-boleto-info';

interface PaymentFormProps {
	loading: boolean;
	totalAmount: number;
	installmentsLimit: number;
	enableInstallments?: boolean;
	acceptedPayments?: string[];
	offers?: Offer[]; // Use o tipo Offer
	selectedBumps: string[];
	onBumpChange: (ids: string[]) => void;
}

// Definição padrão de métodos de pagamento para referência
const PAYMENT_METHODS = {
	CREDIT_CARD: {
		value: 'CREDIT_CARD',
		title: 'Cartão',
		description: '',
		icon: 'credit-card',
	},
	PIX: {
		value: 'PIX',
		title: 'Pix',
		description: 'Pagamento instantâneo',
		icon: 'pix',
	},
	BOLETO: {
		value: 'BOLETO',
		title: 'Boleto',
		description: 'Confirmação em até 24 horas',
		icon: 'boleto',
	},
};

const defaultAcceptedPayments = ['CREDIT_CARD', 'PIX', 'BOLETO'];
const brands = ['visa', 'mastercard', 'amex', 'elo'];

export function PaymentForm({
	loading,
	totalAmount,
	installmentsLimit,
	enableInstallments = false,
	acceptedPayments = defaultAcceptedPayments,
	offers = [],
	selectedBumps,
	onBumpChange,
}: PaymentFormProps) {
	const form = useFormContext<CheckoutFormData>();
	const paymentMethod = form.watch('paymentMethod');

	// Filtrar apenas métodos de pagamento permitidos pelo produto
	const allowedPaymentMethods = Object.values(PAYMENT_METHODS).filter(
		(method) => acceptedPayments.includes(method.value)
	);

	const renderPaymentMethod = () => {
		switch (paymentMethod) {
			case 'CREDIT_CARD':
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
			case 'PIX':
				return <PaymentFormPixInfo />;
			case 'BOLETO':
				return <PaymentFormBoletoInfo />;
			default:
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
		}
	};

	return (
		<Card className='bg-white shadow-sm'>
			<CardHeader className='p-6 space-y-1 '>
				<div className='flex items-center justify-between'>
					<div>
						<div className='flex items-center gap-2'>
							<CreditCard className='h-5 w-5 text-muted-foreground' />
							<CardTitle className='text-lg'>Pagamento</CardTitle>
						</div>
						<p className='text-sm text-muted-foreground hidden'>
							Escolha a forma de pagamento
						</p>
					</div>

					<div className='brand-icons  flex  items-center gap-1 md:gap-3    '>
						{brands.map((brand) => (
							<img
								key={brand}
								src={`/images/payments/card/${brand}.svg`}
								alt={brand}
								className='h-4 md:h-6 w-auto border rounded-[3px]'
							/>
						))}
					</div>
				</div>
			</CardHeader>

			<CardContent className='p-6 pt-0 space-y-6'>
				{allowedPaymentMethods.length > 0 && (
					<Controller
						name='paymentMethod'
						control={form.control}
						render={({ field }) => (
							<RadioGroup
								value={field.value}
								onValueChange={(value: 'CREDIT_CARD' | 'PIX' | 'BOLETO') => {
									field.onChange(value);
									if (value === 'PIX' || value === 'BOLETO') {
										form.setValue('creditCard', undefined);
									}
								}}
								className='grid grid-cols-3 gap-4'
							>
								{allowedPaymentMethods.map((method) => (
									<PaymentOption
										key={method.value}
										value={method.value}
										title={method.title}
										description={method.description}
										icon={method.icon as 'credit-card' | 'pix' | 'boleto'}
										selected={paymentMethod === method.value}
									/>
								))}
							</RadioGroup>
						)}
					/>
				)}

				{renderPaymentMethod()}

				{/* Order Bumps */}
				{offers && offers.length > 0 && (
					<div className='mt-6'>
						<h3 className='text-lg font-medium mb-4'>
							Aproveite e compre junto:
						</h3>
						<OrderBumpList
							offers={offers}
							selected={selectedBumps}
							onSelect={onBumpChange}
						/>
					</div>
				)}

				{/* Payment Action Button */}
				{paymentMethod === 'PIX' ? (
					<PixButton
						onClick={(e) => {
							console.log('=== PIX BUTTON CLICKED ===');
							console.log('Event target:', e.target);
							console.log('Event currentTarget:', e.currentTarget);
							console.log('Form values:', form.getValues());

							// Garantir que o evento seja propagado para o form
							if (e.target !== e.currentTarget) {
								console.log('Event target is not button, forcing form submit...');
								const form = e.currentTarget.closest('form');
								if (form) {
									console.log('Found form, triggering submit...');
									form.requestSubmit();
								}
							}
						}}
						disabled={loading}
						loading={loading}
					/>
				) : paymentMethod === 'BOLETO' ? (
					<BoletoButton
						onClick={(e) => {
							console.log('=== BOLETO BUTTON CLICKED ===');
							console.log('Event target:', e.target);
							console.log('Event currentTarget:', e.currentTarget);
							console.log('Form values:', form.getValues());

							// Garantir que o evento seja propagado para o form
							if (e.target !== e.currentTarget) {
								console.log('Event target is not button, forcing form submit...');
								const form = e.currentTarget.closest('form');
								if (form) {
									console.log('Found form, triggering submit...');
									form.requestSubmit();
								}
							}
						}}
						disabled={loading}
						loading={loading}
					/>
				) : (
					<UrgentPurchaseButton
						discount={33}
						onClick={(e) => {
							console.log('=== URGENT BUTTON CLICKED ===');
							console.log('Event target:', e.target);
							console.log('Event currentTarget:', e.currentTarget);
							console.log('Form values:', form.getValues());

							// Garantir que o evento seja propagado para o form
							if (e.target !== e.currentTarget) {
								console.log('Event target is not button, forcing form submit...');
								const form = e.currentTarget.closest('form');
								if (form) {
									console.log('Found form, triggering submit...');
									form.requestSubmit();
								}
							}
						}}
						disabled={loading}
						loading={loading}
					/>
				)}

				<CheckoutFooter />
			</CardContent>
		</Card>
	);
}
