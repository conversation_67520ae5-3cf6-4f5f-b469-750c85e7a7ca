// apps/web/(checkout)/checkout/components/cpf-cnpj-input.tsx
import { Input, InputProps } from '@ui/components/input';
import { cn } from '@ui/lib';
import { forwardRef, useState, useEffect } from 'react';

interface CPFCNPJInputProps extends Omit<InputProps, 'value' | 'onChange'> {
	error?: string;
	value?: string;
	onChange?: (value: string) => void;
	onDocumentTypeChange?: (type: 'CPF' | 'CNPJ') => void;
}

// Função para validar CPF
function validateCPF(cpf: string): boolean {
	const cleanCPF = cpf.replace(/\D/g, '');

	if (cleanCPF.length !== 11) return false;
	if (/^(\d)\1{10}$/.test(cleanCPF)) return false; // Todos os dígitos iguais

	let sum = 0;
	for (let i = 0; i < 9; i++) {
		sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
	}
	let remainder = (sum * 10) % 11;
	if (remainder === 10 || remainder === 11) remainder = 0;
	if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

	sum = 0;
	for (let i = 0; i < 10; i++) {
		sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
	}
	remainder = (sum * 10) % 11;
	if (remainder === 10 || remainder === 11) remainder = 0;
	if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

	return true;
}

// Função para validar CNPJ
function validateCNPJ(cnpj: string): boolean {
	const cleanCNPJ = cnpj.replace(/\D/g, '');

	if (cleanCNPJ.length !== 14) return false;
	if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false; // Todos os dígitos iguais

	let sum = 0;
	let weight = 2;
	for (let i = 11; i >= 0; i--) {
		sum += parseInt(cleanCNPJ.charAt(i)) * weight;
		weight = weight === 9 ? 2 : weight + 1;
	}
	let remainder = sum % 11;
	const firstDigit = remainder < 2 ? 0 : 11 - remainder;
	if (firstDigit !== parseInt(cleanCNPJ.charAt(12))) return false;

	sum = 0;
	weight = 2;
	for (let i = 12; i >= 0; i--) {
		sum += parseInt(cleanCNPJ.charAt(i)) * weight;
		weight = weight === 9 ? 2 : weight + 1;
	}
	remainder = sum % 11;
	const secondDigit = remainder < 2 ? 0 : 11 - remainder;
	if (secondDigit !== parseInt(cleanCNPJ.charAt(13))) return false;

	return true;
}

// Função para formatar CPF
function formatCPF(value: string): string {
	const numbers = value.replace(/\D/g, '');
	if (numbers.length <= 3) return numbers;
	if (numbers.length <= 6) return `${numbers.slice(0, 3)}.${numbers.slice(3)}`;
	if (numbers.length <= 9) return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6)}`;
	return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9, 11)}`;
}

// Função para formatar CNPJ
function formatCNPJ(value: string): string {
	const numbers = value.replace(/\D/g, '');
	if (numbers.length <= 2) return numbers;
	if (numbers.length <= 5) return `${numbers.slice(0, 2)}.${numbers.slice(2)}`;
	if (numbers.length <= 8) return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5)}`;
	if (numbers.length <= 12) return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5, 8)}/${numbers.slice(8)}`;
	return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5, 8)}/${numbers.slice(8, 12)}-${numbers.slice(12, 14)}`;
}

export const CPFCNPJInput = forwardRef<HTMLInputElement, CPFCNPJInputProps>(
	({ className, error, value = '', onChange, onDocumentTypeChange, ...props }, ref) => {
		const [documentType, setDocumentType] = useState<'CPF' | 'CNPJ' | null>(null);
		const [isValid, setIsValid] = useState<boolean | null>(null);

		// Detectar tipo de documento baseado no comprimento
		useEffect(() => {
			const cleanValue = value.replace(/\D/g, '');

			if (cleanValue.length <= 11) {
				if (documentType !== 'CPF') {
					setDocumentType('CPF');
					onDocumentTypeChange?.('CPF');
				}
			} else if (cleanValue.length > 11) {
				if (documentType !== 'CNPJ') {
					setDocumentType('CNPJ');
					onDocumentTypeChange?.('CNPJ');
				}
			}
		}, [value, documentType, onDocumentTypeChange]);

		// Validar documento quando o valor muda
		useEffect(() => {
			const cleanValue = value.replace(/\D/g, '');

			if (cleanValue.length === 0) {
				setIsValid(null);
				return;
			}

			if (cleanValue.length === 11) {
				setIsValid(validateCPF(value));
			} else if (cleanValue.length === 14) {
				setIsValid(validateCNPJ(value));
			} else {
				setIsValid(false);
			}
		}, [value]);

		// Função para formatar o valor
		const formatValue = (val: string): string => {
			const cleanValue = val.replace(/\D/g, '');

			if (cleanValue.length <= 11) {
				return formatCPF(val);
			} else {
				return formatCNPJ(val);
			}
		};

		// Handler para mudanças no input
		const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const newValue = e.target.value;
			const cleanValue = newValue.replace(/\D/g, '');

			// Limitar o tamanho máximo baseado no tipo de documento
			if (cleanValue.length > 14) return;

			const formattedValue = formatValue(newValue);
			onChange?.(formattedValue);
		};

		// Determinar o placeholder baseado no tipo de documento
		const getPlaceholder = (): string => {
			if (documentType === 'CNPJ') {
				return '00.000.000/0000-00';
			}
			return '000.000.000-00';
		};

		// Determinar o comprimento máximo
		const getMaxLength = (): number => {
			return documentType === 'CNPJ' ? 18 : 14; // 14 para CPF, 18 para CNPJ
		};

		// Label fixo para o campo
		const getLabel = (): string => {
			return 'CPF/CNPJ';
		};

		// Determinar a cor da borda baseada na validação (mantendo padrão dos outros inputs)
		const getBorderColor = (): string => {
			if (error) return 'border-destructive';
			return ''; // Mantém o padrão dos outros inputs
		};

		return (
			<Input
				ref={ref}
				value={formatValue(value)}
				onChange={handleChange}
				maxLength={getMaxLength()}
				inputMode='numeric'
				placeholder={getPlaceholder()}
				className={cn(
					getBorderColor(),
					className
				)}
				{...props}
			/>
		);
	}
);

CPFCNPJInput.displayName = 'CPFCNPJInput';
