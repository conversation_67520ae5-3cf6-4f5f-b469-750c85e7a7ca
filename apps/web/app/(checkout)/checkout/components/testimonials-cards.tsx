'use client';

import { useState, useEffect } from 'react';
import { Star, CheckCircle } from 'lucide-react';
import { FaTiktok, FaWhatsapp, FaInstagram, FaFacebook, FaYoutube, FaEnvelope, FaGlobe } from 'react-icons/fa';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Badge } from '@ui/components/badge';
import { cn } from '@ui/lib';

interface Testimonial {
  id: string;
  customerName: string;
  customerPhoto?: string;
  customerRole?: string;
  customerLocation?: string;
  content: string;
  rating: number;
  source: 'WHATSAPP' | 'FACEBOOK' | 'TIKTOK' | 'INSTAGRAM' | 'EMAIL' | 'MANUAL' | 'YOUTUBE' | 'WEBSITE';
  sourceUrl?: string;
  isApproved: boolean;
  isFeatured?: boolean;
}

interface TestimonialsCardsProps {
  productId?: string;
  maxTestimonials?: number;
  showStars?: boolean;
  showAvatars?: boolean;
  showSource?: boolean;
  showVerified?: boolean;
  className?: string;
}

// Ícones das redes sociais
const sourceIcons = {
  TIKTOK: FaTiktok,
  WHATSAPP: FaWhatsapp,
  INSTAGRAM: FaInstagram,
  FACEBOOK: FaFacebook,
  YOUTUBE: FaYoutube,
  EMAIL: FaEnvelope,
  WEBSITE: FaGlobe,
  MANUAL: FaGlobe,
};

// Labels das redes sociais
const sourceLabels = {
  TIKTOK: 'TikTok',
  WHATSAPP: 'WhatsApp',
  INSTAGRAM: 'Instagram',
  FACEBOOK: 'Facebook',
  YOUTUBE: 'YouTube',
  EMAIL: 'Email',
  WEBSITE: 'Website',
  MANUAL: 'Manual',
};

// Cores das redes sociais
const sourceColors = {
  TIKTOK: 'text-black bg-white',
  WHATSAPP: 'text-green-600 bg-green-50',
  INSTAGRAM: 'text-pink-600 bg-pink-50',
  FACEBOOK: 'text-blue-600 bg-blue-50',
  YOUTUBE: 'text-red-600 bg-red-50',
  EMAIL: 'text-gray-600 bg-gray-50',
  WEBSITE: 'text-gray-600 bg-gray-50',
  MANUAL: 'text-gray-600 bg-gray-50',
};

export function TestimonialsCards({
  productId,
  maxTestimonials = 6,
  showStars = true,
  showAvatars = true,
  showSource = true,
  showVerified = true,
  className = ''
}: TestimonialsCardsProps) {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch testimonials from API
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setLoading(true);
        
        let url = '/api/testimonials/public';
        const params = new URLSearchParams();
        params.append('limit', maxTestimonials.toString());
        params.append('featured', 'true');
        
        if (productId) {
          url = `/api/testimonials/public/${productId}`;
        }

        const response = await fetch(`${url}?${params.toString()}`);
        
        if (response.ok) {
          const data = await response.json();
          const testimonialsData = data.testimonials || data;
          setTestimonials(Array.isArray(testimonialsData) ? testimonialsData : []);
        } else {
          // Fallback to mock data
          setTestimonials(getMockTestimonials());
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        // Fallback to mock data
        setTestimonials(getMockTestimonials());
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, [productId, maxTestimonials]);

  const getMockTestimonials = (): Testimonial[] => [
    {
      id: '1',
      customerName: 'Roberto Santos',
      customerRole: 'Founder',
      customerLocation: 'Belo Horizonte, MG',
      content: 'Como startup, precisávamos de uma solução confiável e escalável. O gateway nos permitiu focar no nosso produto principal enquanto eles cuidam de toda a complexidade dos pagamentos.',
      rating: 5,
      source: 'INSTAGRAM',
      isApproved: true,
      isFeatured: true,
    },
    {
      id: '2',
      customerName: 'Maria Silva',
      customerRole: 'CEO',
      customerLocation: 'São Paulo, SP',
      content: 'A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX com total segurança.',
      rating: 5,
      source: 'WHATSAPP',
      isApproved: true,
      isFeatured: true,
    },
    {
      id: '3',
      customerName: 'João Costa',
      customerRole: 'Desenvolvedor',
      customerLocation: 'Rio de Janeiro, RJ',
      content: 'Suporte excepcional e plataforma confiável. Recomendo para qualquer negócio digital que busca eficiência.',
      rating: 5,
      source: 'TIKTOK',
      isApproved: true,
      isFeatured: true,
    },
    {
      id: '4',
      customerName: 'Ana Rodrigues',
      customerRole: 'Gerente de Vendas',
      customerLocation: 'Brasília, DF',
      content: 'Finalmente uma solução que funciona de verdade. Nossos clientes adoraram a facilidade do PIX.',
      rating: 5,
      source: 'FACEBOOK',
      isApproved: true,
      isFeatured: true,
    },
    {
      id: '5',
      customerName: 'Carlos Lima',
      customerRole: 'Empresário',
      customerLocation: 'Salvador, BA',
      content: 'Excelente qualidade e entrega rápida. Já comprei várias vezes e sempre fico satisfeito.',
      rating: 5,
      source: 'YOUTUBE',
      isApproved: true,
      isFeatured: true,
    },
    {
      id: '6',
      customerName: 'Fernanda Oliveira',
      customerRole: 'Estudante',
      customerLocation: 'Porto Alegre, RS',
      content: 'Transformou minha vida! Não me arrependo da compra. Recomendo para todos.',
      rating: 5,
      source: 'EMAIL',
      isApproved: true,
      isFeatured: true,
    },
  ];


  if (loading) {
    return (
      <div className={cn('w-full space-y-4', className)}>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 animate-pulse">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
            <div className="h-16 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          </div>
        ))}
      </div>
    );
  }

  if (testimonials.length === 0) {
    return null;
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <div className="text-2xl text-blue-600">"</div>
        <h3 className="text-lg font-semibold text-gray-900">
          O que nossos clientes dizem
        </h3>
        {showVerified && (
          <Badge className="bg-green-50 text-green-600 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            VERIFICADO
          </Badge>
        )}
      </div>

      {/* Testimonials Cards */}
      <div className="space-y-4">
        {testimonials.map((testimonial) => {
          const SourceIcon = sourceIcons[testimonial.source] || FaGlobe;
          
          return (
            <div
              key={testimonial.id}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              {/* Header do Card */}
              <div className="flex items-center gap-3 mb-4">
                {showAvatars && (
                  <Avatar className="h-10 w-10">
                    <AvatarImage 
                      src={testimonial.customerPhoto} 
                      alt={testimonial.customerName}
                    />
                    <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                      {testimonial.customerName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold text-gray-900 text-sm truncate">
                      {testimonial.customerName}
                    </h4>
                    {showVerified && testimonial.isApproved && (
                      <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                    )}
                  </div>
                  
                  {testimonial.customerRole && (
                    <p className="text-xs text-gray-600 truncate">
                      {testimonial.customerRole}
                      {testimonial.customerLocation && ` • ${testimonial.customerLocation}`}
                    </p>
                  )}
                </div>
              </div>

              {/* Rating */}
              {showStars && (
                <div className="flex gap-1 mb-3">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        'h-4 w-4',
                        i < testimonial.rating 
                          ? 'text-yellow-400 fill-current' 
                          : 'text-gray-300'
                      )}
                    />
                  ))}
                </div>
              )}

              {/* Content */}
              <blockquote className="text-sm text-gray-700 leading-relaxed mb-4">
                "{testimonial.content}"
              </blockquote>

              {/* Source */}
              {showSource && (
                <div className="flex items-center justify-between">
                  <Badge 
                    variant="outline" 
                    className={cn(
                      'text-xs px-2 py-1',
                      sourceColors[testimonial.source]
                    )}
                  >
                    <SourceIcon className="h-3 w-3 mr-1" />
                    {sourceLabels[testimonial.source]}
                  </Badge>
                  
                  <div className="text-xs text-gray-500">
                    4.9/5 avaliação
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Footer - Rating Summary */}
      <div className="flex items-center justify-center gap-6 mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Star className="h-4 w-4 text-yellow-400 fill-current" />
          <span>4.9/5 avaliação</span>
        </div>
      </div>
    </div>
  );
}
