'use client';

import { useState } from 'react';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { Button } from '@ui/components/button';
import { useFileUpload } from '@saas/products/hooks/useFileUpload';

interface SidebarBannerUploadProps {
  currentBanner?: string;
  onBannerChange: (url: string | null) => void;
  showLabel?: boolean;
}

export function SidebarBannerUpload({ 
  currentBanner, 
  onBannerChange, 
  showLabel = true 
}: SidebarBannerUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentBanner || null);

  const { uploadFile, isUploading } = useFileUpload({
    bucket: 'checkoutBanners',
    onSuccess: (url) => {
      setPreviewUrl(url);
      onBannerChange(url);
    },
    onError: (error) => {
      console.error('Upload error:', error);
    },
  });

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Por favor, selecione apenas arquivos de imagem.');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('O arquivo deve ter no máximo 10MB.');
      return;
    }

    await uploadFile(file);
  };

  const handleRemoveBanner = () => {
    setPreviewUrl(null);
    onBannerChange(null);
  };

  // Convert R2 URL to CDN URL if needed
  const displayUrl = previewUrl && previewUrl.includes('r2.cloudflarestorage.com')
    ? previewUrl.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : previewUrl;

  return (
    <div className="space-y-4">
      {showLabel && (
        <label className="block text-sm font-medium text-gray-700">
          Banner da Sidebar
        </label>
      )}
      
      {/* Banner Preview */}
      {displayUrl && (
        <div className="relative">
          <div className="w-full max-w-sm rounded-lg overflow-hidden border-2 border-gray-200">
            <img
              src={displayUrl}
              alt="Banner preview"
              className="w-full h-32 object-cover"
            />
          </div>
          
          {/* Remove Button */}
          <button
            onClick={handleRemoveBanner}
            className="absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
            disabled={isUploading}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Upload Area */}
      {!displayUrl && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-4">
            Nenhum banner selecionado
          </p>
        </div>
      )}

      {/* Upload Controls */}
      <div>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
          id="sidebar-banner-upload"
          disabled={isUploading}
        />
        <label htmlFor="sidebar-banner-upload">
          <Button
            type="button"
            variant="outline"
            disabled={isUploading}
            className="cursor-pointer"
            asChild
          >
            <span>
              <Upload className="h-4 w-4 mr-2" />
              {isUploading ? 'Enviando...' : displayUrl ? 'Alterar Banner' : 'Escolher Banner'}
            </span>
          </Button>
        </label>
        
        <p className="text-xs text-gray-500 mt-2">
          PNG, JPG ou WebP. Máximo 10MB. Recomendado: 400x200px
        </p>
      </div>
    </div>
  );
}
