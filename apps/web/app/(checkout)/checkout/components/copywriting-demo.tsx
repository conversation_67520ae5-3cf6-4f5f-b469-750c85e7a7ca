'use client';

import { useState } from 'react';
import { EnhancedOrderBumpList } from './enhanced-order-bump-list';
import { mockOrderBumps } from './mock-orderbumps';
import { orderBumpCopywritingOptions, getCopywritingForContext } from './copywriting-options';

export function CopywritingDemo() {
	const [selectedBumps, setSelectedBumps] = useState<string[]>([]);
	const [selectedTitle, setSelectedTitle] = useState('urgency');
	const [selectedSubtitle, setSelectedSubtitle] = useState('value');

	const currentCopywriting = getCopywritingForContext(
		undefined,
		undefined,
		orderBumpCopywritingOptions.titles.find(t => t.id === selectedTitle)?.text,
		orderBumpCopywritingOptions.subtitles.find(s => s.id === selectedSubtitle)?.text
	);

	return (
		<div className="space-y-8">
			<div className="text-center">
				<h2 className="text-2xl font-bold mb-2">Demonstração de Copywriting</h2>
				<p className="text-gray-600">
					Teste diferentes opções de copywriting para maximizar conversões
				</p>
			</div>

			{/* Controles de seleção */}
			<div className="bg-gray-50 p-6 rounded-lg">
				<h3 className="text-lg font-semibold mb-4">Escolha o Copywriting:</h3>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{/* Seleção de título */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">
							Título Principal:
						</label>
						<select
							value={selectedTitle}
							onChange={(e) => setSelectedTitle(e.target.value)}
							className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						>
							{orderBumpCopywritingOptions.titles.map((title) => (
								<option key={title.id} value={title.id}>
									{title.text} - {title.description}
								</option>
							))}
						</select>
					</div>

					{/* Seleção de subtítulo */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">
							Subtítulo:
						</label>
						<select
							value={selectedSubtitle}
							onChange={(e) => setSelectedSubtitle(e.target.value)}
							className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						>
							{orderBumpCopywritingOptions.subtitles.map((subtitle) => (
								<option key={subtitle.id} value={subtitle.id}>
									{subtitle.text}
								</option>
							))}
						</select>
					</div>
				</div>

				{/* Informações sobre a psicologia */}
				<div className="mt-4 p-3 bg-blue-50 rounded-lg">
					<h4 className="font-medium text-blue-800 mb-1">Psicologia Aplicada:</h4>
					<p className="text-sm text-blue-700">
						<strong>Título:</strong> {orderBumpCopywritingOptions.titles.find(t => t.id === selectedTitle)?.psychology}
					</p>
					<p className="text-sm text-blue-700">
						<strong>Subtítulo:</strong> {orderBumpCopywritingOptions.subtitles.find(s => s.id === selectedSubtitle)?.description}
					</p>
				</div>
			</div>

			{/* Preview do orderbump */}
			<div className="bg-white border border-gray-200 rounded-lg p-6">
				<h3 className="text-lg font-semibold mb-4">Preview do Orderbump:</h3>
				<EnhancedOrderBumpList
					offers={mockOrderBumps.slice(0, 2)}
					selected={selectedBumps}
					onSelect={setSelectedBumps}
					title={currentCopywriting.title}
					subtitle={currentCopywriting.subtitle}
				/>
			</div>

			{/* Estatísticas de conversão (simuladas) */}
			<div className="bg-green-50 border border-green-200 rounded-lg p-6">
				<h3 className="text-lg font-semibold text-green-800 mb-4">
					📊 Métricas de Conversão (Simuladas)
				</h3>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="text-center">
						<div className="text-2xl font-bold text-green-600">
							{selectedTitle === 'urgency' ? '23.5%' :
							 selectedTitle === 'value' ? '19.2%' :
							 selectedTitle === 'personal' ? '21.8%' : '18.7%'}%
						</div>
						<div className="text-sm text-green-700">Taxa de Aceitação</div>
					</div>
					<div className="text-center">
						<div className="text-2xl font-bold text-green-600">
							R$ {selectedTitle === 'urgency' ? '47.30' :
							 selectedTitle === 'value' ? '42.80' :
							 selectedTitle === 'personal' ? '45.20' : '41.50'}
						</div>
						<div className="text-sm text-green-700">AOV Médio</div>
					</div>
					<div className="text-center">
						<div className="text-2xl font-bold text-green-600">
							+{selectedTitle === 'urgency' ? '34%' :
							 selectedTitle === 'value' ? '28%' :
							 selectedTitle === 'personal' ? '31%' : '25%'}%
						</div>
						<div className="text-sm text-green-700">Aumento na Receita</div>
					</div>
				</div>
			</div>

			{/* Dicas de otimização */}
			<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
				<h3 className="text-lg font-semibold text-yellow-800 mb-4">
					💡 Dicas de Otimização
				</h3>
				<div className="space-y-3 text-sm text-yellow-700">
					<div>
						<strong>Urgência:</strong> Use quando há escassez real ou tempo limitado
					</div>
					<div>
						<strong>Valor:</strong> Funciona bem com produtos de preço médio-alto
					</div>
					<div>
						<strong>Personalização:</strong> Ideal para clientes recorrentes ou com histórico
					</div>
					<div>
						<strong>Prova Social:</strong> Use números específicos e credíveis
					</div>
					<div>
						<strong>Teste A/B:</strong> Sempre teste diferentes variações para seu público
					</div>
				</div>
			</div>
		</div>
	);
}
