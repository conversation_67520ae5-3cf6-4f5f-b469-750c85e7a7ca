'use client';

import { TestimonialsCards } from './testimonials-cards';

interface TestimonialsExampleProps {
  productId?: string;
  maxTestimonials?: number;
}

export function TestimonialsExample({ 
  productId,
  maxTestimonials = 6
}: TestimonialsExampleProps) {
  return (
    <TestimonialsCards
      productId={productId}
      maxTestimonials={maxTestimonials}
      showStars={true}
      showAvatars={true}
      showSource={true}
      showVerified={true}
      className="my-8"
    />
  );
}
