'use client';

import { Shield, RefreshCw, Clock, Award, CheckCircle, Star } from 'lucide-react';

interface GuaranteeCardsProps {
  settings: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: 'shield' | 'check' | 'mail' | 'award' | 'lock' | 'truck' | 'heart' | 'star' | 'zap' | 'clock' | 'custom';
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };
}

const iconMap = {
  shield: Shield,
  refresh: RefreshCw,
  clock: Clock,
  award: Award,
  check: CheckCircle,
  star: Star,
};

export function GuaranteeCards({ settings }: GuaranteeCardsProps) {
  if (!settings.enabled) return null;

  const enabledCards = settings.cards
    .filter(card => card.enabled)
    .sort((a, b) => a.order - b.order);
  
  if (enabledCards.length === 0) return null;

  const getLayoutClasses = () => {
    switch (settings.layout) {
      case 'vertical':
        return 'flex flex-col gap-4';
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
      case 'horizontal':
      default:
        return 'flex flex-wrap gap-4 justify-center';
    }
  };

  const renderCard = (card: typeof enabledCards[0]) => {
    const IconComponent = iconMap[card.icon as keyof typeof iconMap] || Shield;
    
    return (
      <div
        key={card.id}
        className={`flex flex-col items-center text-center p-6 rounded-lg border ${settings.backgroundColor} ${settings.borderColor} ${settings.textColor}`}
      >
        <div className="flex-shrink-0 mb-4">
          {card.customIcon ? (
            <img 
              src={card.customIcon} 
              alt={card.title}
              className="h-12 w-12"
            />
          ) : (
            <IconComponent className="h-12 w-12" />
          )}
        </div>
        <div className="flex-1">
          <h3 className="font-bold text-lg mb-2">
            {card.title}
          </h3>
          <p className="text-sm opacity-90">
            {card.description}
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full mb-6">
      <div className={getLayoutClasses()}>
        {enabledCards.map(renderCard)}
      </div>
    </div>
  );
}
