'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@ui/components/button';
import { Card, CardContent } from '@ui/components/card';
import { RadioGroup, RadioGroupItem } from '@ui/components/radio-group';
import { Label } from '@ui/components/label';
import { Input } from '@ui/components/input';
import { CreditCard, Loader2, Lock } from 'lucide-react';
import { formatCurrency } from '@lib/utils';

const simplePaymentSchema = z.object({
  paymentMethod: z.enum(['CREDIT_CARD', 'PIX', 'BOLETO']),
  creditCard: z.object({
    cardNumber: z.string().min(16, 'Número do cartão inválido'),
    cardHolder: z.string().min(2, 'Nome do portador obrigatório'),
    cardExpiry: z.string().regex(/^\d{2}\/\d{2}$/, 'Data inválida (MM/AA)'),
    cardCvv: z.string().min(3, 'CVV inválido'),
    installments: z.number().min(1).max(12).default(1),
  }).optional(),
});

type SimplePaymentFormData = z.infer<typeof simplePaymentSchema>;

interface SimplePaymentFormProps {
  amount: number;
  onSubmit: (data: SimplePaymentFormData) => Promise<void>;
  isLoading?: boolean;
  submitButtonText?: string;
}

export function PaymentForm({
  amount,
  onSubmit,
  isLoading = false,
  submitButtonText = 'Finalizar Compra',
}: SimplePaymentFormProps) {
  const [selectedMethod, setSelectedMethod] = useState<'CREDIT_CARD' | 'PIX' | 'BOLETO'>('CREDIT_CARD');

  const form = useForm<SimplePaymentFormData>({
    resolver: zodResolver(simplePaymentSchema),
    defaultValues: {
      paymentMethod: 'CREDIT_CARD',
      creditCard: {
        installments: 1,
      },
    },
  });

  const handleSubmit = async (data: SimplePaymentFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Payment error:', error);
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {/* Payment Method Selection */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Método de Pagamento</Label>
        <RadioGroup
          value={selectedMethod}
          onValueChange={(value: 'CREDIT_CARD' | 'PIX' | 'BOLETO') => {
            setSelectedMethod(value);
            form.setValue('paymentMethod', value);
          }}
          className="grid grid-cols-3 gap-3"
        >
          <div className="flex items-center space-x-2 border rounded-lg p-3">
            <RadioGroupItem value="CREDIT_CARD" id="credit-card" />
            <Label htmlFor="credit-card" className="flex-1 cursor-pointer">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                <span className="text-sm">Cartão</span>
              </div>
            </Label>
          </div>
          <div className="flex items-center space-x-2 border rounded-lg p-3">
            <RadioGroupItem value="PIX" id="pix" />
            <Label htmlFor="pix" className="flex-1 cursor-pointer">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded-sm"></div>
                <span className="text-sm">PIX</span>
              </div>
            </Label>
          </div>
          <div className="flex items-center space-x-2 border rounded-lg p-3">
            <RadioGroupItem value="BOLETO" id="boleto" />
            <Label htmlFor="boleto" className="flex-1 cursor-pointer">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-orange-500 rounded-sm"></div>
                <span className="text-sm">Boleto</span>
              </div>
            </Label>
          </div>
        </RadioGroup>
      </div>

      {/* Credit Card Form */}
      {selectedMethod === 'CREDIT_CARD' && (
        <Card>
          <CardContent className="pt-6 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <Label htmlFor="cardNumber">Número do Cartão</Label>
                <Input
                  id="cardNumber"
                  placeholder="0000 0000 0000 0000"
                  maxLength={19}
                  {...form.register('creditCard.cardNumber')}
                  onChange={(e) => {
                    const formatted = formatCardNumber(e.target.value);
                    form.setValue('creditCard.cardNumber', formatted.replace(/\s/g, ''));
                    e.target.value = formatted;
                  }}
                />
                {form.formState.errors.creditCard?.cardNumber && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.creditCard.cardNumber.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="cardExpiry">Validade</Label>
                <Input
                  id="cardExpiry"
                  placeholder="MM/AA"
                  maxLength={5}
                  {...form.register('creditCard.cardExpiry')}
                  onChange={(e) => {
                    const formatted = formatExpiry(e.target.value);
                    form.setValue('creditCard.cardExpiry', formatted);
                    e.target.value = formatted;
                  }}
                />
                {form.formState.errors.creditCard?.cardExpiry && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.creditCard.cardExpiry.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="cardCvv">CVV</Label>
                <Input
                  id="cardCvv"
                  placeholder="123"
                  maxLength={4}
                  {...form.register('creditCard.cardCvv')}
                />
                {form.formState.errors.creditCard?.cardCvv && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.creditCard.cardCvv.message}
                  </p>
                )}
              </div>
              <div className="col-span-2">
                <Label htmlFor="cardHolder">Nome no Cartão</Label>
                <Input
                  id="cardHolder"
                  placeholder="Nome como está no cartão"
                  {...form.register('creditCard.cardHolder')}
                />
                {form.formState.errors.creditCard?.cardHolder && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.creditCard.cardHolder.message}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* PIX Info */}
      {selectedMethod === 'PIX' && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <div className="w-6 h-6 bg-green-500 rounded-sm"></div>
              </div>
              <h3 className="font-medium">Pagamento via PIX</h3>
              <p className="text-sm text-gray-600">
                Após confirmar, você receberá o QR Code para pagamento instantâneo
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Boleto Info */}
      {selectedMethod === 'BOLETO' && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                <div className="w-6 h-6 bg-orange-500 rounded-sm"></div>
              </div>
              <h3 className="font-medium">Pagamento via Boleto</h3>
              <p className="text-sm text-gray-600">
                Confirmação em até 24 horas após o pagamento
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Total and Submit */}
      <div className="space-y-4">
        <div className="flex justify-between items-center text-lg font-semibold">
          <span>Total:</span>
          <span>{formatCurrency(amount)}</span>
        </div>

        <Button type="submit" className="w-full h-12" disabled={isLoading}>
          {isLoading ? (
            <span className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Processando...
            </span>
          ) : (
            <span className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              {submitButtonText}
            </span>
          )}
        </Button>
      </div>
    </form>
  );
}
