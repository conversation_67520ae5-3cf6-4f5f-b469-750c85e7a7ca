'use client';

import { useState } from 'react';

interface CheckoutSidebarProps {
  settings: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}

export function CheckoutSidebar({ settings }: CheckoutSidebarProps) {
  const [imageError, setImageError] = useState(false);

  if (!settings.enabled) return null;

  // Convert R2 URL to CDN URL if needed
  const displayBannerUrl = settings.bannerUrl && settings.bannerUrl.includes('r2.cloudflarestorage.com')
    ? settings.bannerUrl.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : settings.bannerUrl;

  return (
    <div className={`w-full p-6 ${settings.backgroundColor} ${settings.borderColor} border ${settings.borderRadius} ${settings.shadow ? 'shadow-lg' : ''} ${settings.textColor}`}>
      {/* Banner */}
      {displayBannerUrl && !imageError && (
        <div className="mb-4">
          <img
            src={displayBannerUrl}
            alt="Sidebar banner"
            className={`w-full ${settings.borderRadius} object-cover`}
            onError={() => setImageError(true)}
            style={{ maxHeight: '200px' }}
          />
        </div>
      )}

      {/* Title */}
      {settings.title && (
        <h3 className="text-xl font-bold mb-4">
          {settings.title}
        </h3>
      )}

      {/* Content */}
      {settings.content && (
        <div 
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: settings.content }}
        />
      )}
    </div>
  );
}
