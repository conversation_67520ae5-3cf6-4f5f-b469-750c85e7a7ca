// components/payment-form-boleto-info.tsx
import { Receipt, Clock, InfoIcon } from 'lucide-react';

export function PaymentFormBoletoInfo() {
	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
				<div className='rounded-lg border bg-card p-4'>
					<div className='flex flex-col items-center space-y-2 text-center'>
						<Receipt className='h-8 w-8 text-muted-foreground' />
						<h3 className='font-medium'>Como funciona?</h3>
						<p className='text-sm text-muted-foreground'>
							Após clicar em <b>"Gerar Boleto"</b>, você será redirecionado para
							uma página segura onde poderá visualizar e baixar seu boleto para
							pagamento.
						</p>
					</div>
				</div>

				<div className='rounded-lg border bg-card p-4'>
					<div className='flex flex-col items-center space-y-2 text-center'>
						<Clock className='h-8 w-8 text-muted-foreground' />
						<h3 className='font-medium'>Prazo de Pagamento</h3>
						<p className='text-sm text-muted-foreground'>
							O boleto tem vencimento em 5 dias. Após o pagamento, pode levar
							até 24 horas para a confirmação bancária.
						</p>
					</div>
				</div>
			</div>

			<div className='rounded-lg border bg-muted/50 p-4'>
				<div className='flex items-center gap-2 text-sm text-muted-foreground'>
					<InfoIcon className='h-4 w-4 flex-shrink-0' />
					<p>
						Por questões de segurança, reembolsos de pagamentos feitos via
						boleto serão realizados apenas via transferência bancária após
						análise.
					</p>
				</div>
			</div>
		</div>
	);
}
