'use client';

import { useState } from 'react';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Tag, Check, X, Ticket } from 'lucide-react';

interface CouponSectionProps {
  onCouponApply?: (coupon: string) => void;
  onCouponRemove?: () => void;
  appliedCoupon?: string | null;
  discount?: number;
  className?: string;
}

export function CouponSection({
  onCouponApply,
  onCouponRemove,
  appliedCoupon,
  discount = 0,
  className = ''
}: CouponSectionProps) {
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [isApplying, setIsApplying] = useState(false);

  const handleApplyCoupon = async () => {
    if (!couponCode.trim() || !onCouponApply) return;

    setIsApplying(true);
    try {
      await onCouponApply(couponCode.trim());
      setCouponCode('');
      setShowCouponInput(false);
    } catch (error) {
      console.error('Erro ao aplicar cupom:', error);
    } finally {
      setIsApplying(false);
    }
  };

  const handleRemoveCoupon = () => {
    if (onCouponRemove) {
      onCouponRemove();
    }
    setShowCouponInput(false);
  };

  if (appliedCoupon) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Label className="text-sm font-medium">Cupom de desconto</Label>
        <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <Tag className="h-4 w-4 text-green-600" />
          <span className="text-sm text-green-800 dark:text-green-200 flex-1">
            Cupom <strong>{appliedCoupon}</strong> aplicado
            {discount > 0 && ` - ${discount}% de desconto`}
          </span>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleRemoveCoupon}
            className="h-6 w-6 p-0 text-green-600 hover:text-green-800"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  if (showCouponInput) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Label className="text-sm font-medium">Cupom de desconto</Label>
        <div className="flex gap-2">
          <Input
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            placeholder="Digite seu cupom"
            className="flex-1 h-10"
            onKeyPress={(e) => e.key === 'Enter' && handleApplyCoupon()}
          />
          <Button
            onClick={handleApplyCoupon}
            disabled={!couponCode.trim() || isApplying}
            className="h-10 px-4"
          >
            <Ticket className="h-4 w-4 mr-2" />
            {isApplying ? 'Aplicando...' : 'Aplicar'}
          </Button>
          <Button
            onClick={() => setShowCouponInput(false)}
            variant="outline"
            className="h-10 px-3"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <Button
        onClick={() => setShowCouponInput(true)}
        variant="outline"
        size="sm"
        className="w-full justify-start text-gray-600 hover:text-gray-800"
      >
        <Tag className="h-4 w-4 mr-2" />
        Tem cupom? Clique aqui para aplicar
      </Button>
    </div>
  );
}
