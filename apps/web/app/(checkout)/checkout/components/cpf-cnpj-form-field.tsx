// apps/web/app/(checkout)/checkout/components/cpf-cnpj-form-field.tsx
import { ReactNode } from 'react';
import { Label } from '@ui/components/label';
import { FormMessage } from '@ui/components/form';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/tooltip';
import { HelpCircle } from 'lucide-react';

interface CPFCNPJFormFieldProps {
	label: string;
	name: string;
	error?: string;
	children: ReactNode;
	required?: boolean;
	helpText?: string;
}

export function CPFCNPJFormField({
	label,
	name,
	error,
	children,
	required,
	helpText,
}: CPFCNPJFormFieldProps) {
	return (
		<div className='space-y-2'>
			<div className="flex items-center gap-2">
				<Label htmlFor={name} className='text-sm font-medium'>
					{label}
					{required && <span className='text-destructive ml-1'>*</span>}
				</Label>

				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<button
								type="button"
								className="text-muted-foreground hover:text-foreground transition-colors"
							>
								<HelpCircle className="h-4 w-4" />
							</button>
						</TooltipTrigger>
						<TooltipContent side="top" className="max-w-xs">
							<div className="space-y-1">
								<p className="text-xs font-medium">Por que pedimos seu documento?</p>
								<p className="text-xs text-muted-foreground">
									Necessário para emissão de notas fiscais e comprovação de identidade.
								</p>
							</div>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>

			{children}

			{helpText && <p className='text-xs text-muted-foreground'>{helpText}</p>}

			{error && <FormMessage>{error}</FormMessage>}
		</div>
	);
}
