'use client';

import { useState } from 'react';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { formatCurrency } from '@lib/utils';
import { Clock, Package, Star, Tag, Users, Zap, ChevronDown, FileText, Shield, Lock, ShieldCheck } from 'lucide-react';
import Image from 'next/image';

import { TrustBadges } from './trust-badges';
import { TestimonialsCards } from './testimonials-cards';
import { SecurityGuaranteeCards } from './security-guarantee-cards';
import { CouponSection } from './coupon-section';

// Import conversion components
import {
  UrgencyTimer,
  Testimonials,
  Scarcity,
  useUrgencyTimer
} from '@ui/components/conversion';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@ui/components/collapsible';
import { cn } from '@ui/lib';
import { CouponForm } from './coupon-form';
import { useAnalytics } from '@analytics';

interface EnhancedCheckoutSummaryProps {
  product: any;
  selectedBumps: string[];
  onBumpChange?: (ids: string[]) => void;
  isMobile: boolean;
  appliedCoupon?: {
    code: string;
    discountAmount: number;
    finalPrice: number;
  } | null;
  onCouponApplied?: (coupon: {
    code: string;
    discountAmount: number;
    finalPrice: number;
  }) => void;
  showConversionElements?: boolean;
  // Trust Badges props
  trustBadgesEnabled?: boolean;
  trustBadges?: Array<{
    id: string;
    title: string;
    subtitle: string;
    icon: 'shield' | 'lock' | 'mail' | 'check' | 'star' | 'award' | 'truck' | 'credit-card';
    enabled: boolean;
  }>;
  trustBadgesLayout?: 'horizontal' | 'vertical' | 'grid';
  trustBadgesShowDescriptions?: boolean;
  trustBadgesBackgroundColor?: string;
  trustBadgesTextColor?: string;
  trustBadgesBorderColor?: string;
  // Testimonials props
  testimonialsEnabled?: boolean;
  testimonials?: Array<{
    id: string;
    name: string;
    rating: number;
    comment: string;
    avatar?: string;
    location?: string;
    verified?: boolean;
  }>;
  testimonialsMaxTestimonials?: number;
  testimonialsAutoPlay?: boolean;
  testimonialsAutoPlayInterval?: number;
  testimonialsShowControls?: boolean;
  testimonialsShowStars?: boolean;
  testimonialsShowAvatars?: boolean;
  testimonialsBackgroundColor?: string;
  testimonialsTextColor?: string;
  testimonialsBorderColor?: string;
  // Guarantee Cards props
  guaranteeCards?: Array<{
    id: string;
    title: string;
    description: string;
    icon: 'shield' | 'check' | 'mail' | 'award' | 'lock' | 'truck' | 'heart' | 'star' | 'zap' | 'clock';
    enabled: boolean;
    order: number;
  }>;
  guaranteeCardsLayout?: 'horizontal' | 'vertical' | 'grid';
  guaranteeCardsBackgroundColor?: string;
  guaranteeCardsTextColor?: string;
  guaranteeCardsBorderColor?: string;
  // Urgency props
  urgencyEnabled?: boolean;
  urgencyMessage?: string;
  urgencyVariant?: 'warning' | 'danger' | 'default';
  // Scarcity props
  scarcityEnabled?: boolean;
  scarcityTotalStock?: number;
  scarcitySoldCount?: number;
  scarcityMessage?: string;
  scarcityVariant?: 'warning' | 'danger' | 'default';
}

export function EnhancedCheckoutSummary({
  product,
  selectedBumps,
  onBumpChange,
  isMobile,
  appliedCoupon,
  onCouponApplied,
  showConversionElements = true,
  // Trust Badges
  trustBadgesEnabled = true,
  trustBadges,
  trustBadgesLayout = 'horizontal',
  trustBadgesShowDescriptions = true,
  trustBadgesBackgroundColor = 'bg-blue-50',
  trustBadgesTextColor = 'text-blue-800',
  trustBadgesBorderColor = 'border-blue-200',
  // Testimonials
  testimonialsEnabled = true,
  testimonials,
  testimonialsMaxTestimonials = 3,
  testimonialsAutoPlay = true,
  testimonialsAutoPlayInterval = 5000,
  testimonialsShowControls = true,
  testimonialsShowStars = true,
  testimonialsShowAvatars = true,
  testimonialsBackgroundColor = 'bg-gray-50',
  testimonialsTextColor = 'text-gray-800',
  testimonialsBorderColor = 'border-gray-200',
  // Guarantee Cards
  guaranteeCards,
  guaranteeCardsLayout = 'vertical',
  guaranteeCardsBackgroundColor = 'bg-white',
  guaranteeCardsTextColor = 'text-blue-900',
  guaranteeCardsBorderColor = 'border-blue-200',
  // Urgency
  urgencyEnabled = false,
  urgencyMessage,
  urgencyVariant = 'warning',
  // Scarcity
  scarcityEnabled = false,
  scarcityTotalStock,
  scarcitySoldCount,
  scarcityMessage,
  scarcityVariant = 'warning',
}: EnhancedCheckoutSummaryProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { trackEvent } = useAnalytics();

  // Urgency timer (24 hours from now)
  const endTime = useUrgencyTimer(24);

  const calculateTotal = () => {
    if (appliedCoupon) {
      return appliedCoupon.finalPrice;
    }

    let total = product.price || 0;

    // Adicionar bumps selecionados
    if (product.bumps && selectedBumps.length > 0) {
      selectedBumps.forEach(bumpId => {
        const bump = product.bumps.find((b: any) => b.id === bumpId);
        if (bump) {
          total += bump.price || 0;
        }
      });
    }

    return total;
  };

  const originalPrice = product.originalPrice || product.price * 1.5; // Simular preço original
  const discountPercentage = Math.round(((originalPrice - product.price) / originalPrice) * 100);

  // Tracking functions
  const trackConversionElement = (element: string, action: string, data?: any) => {
    trackEvent('conversion_element_interaction', {
      productId: product.id,
      element,
      action,
      ...data,
    });
  };

  const trackTestimonialView = () => {
    trackConversionElement('testimonials', 'view');
  };

  const trackUrgencyView = () => {
    trackConversionElement('urgency_timer', 'view');
  };

  const trackScarcityView = () => {
    trackConversionElement('scarcity', 'view');
  };

  const trackTrustBadgeView = () => {
    trackConversionElement('trust_badges', 'view');
  };

  // Mobile compact header component
  const MobileCompactHeader = () => (
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center gap-3">
        {product.thumbnail && (
          <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
            <Image
              src={product.thumbnail}
              alt={product.title}
              fill
              className="object-cover"
            />
          </div>
        )}
        <div className="min-w-0 flex-1">
          <h3 className="font-semibold text-sm text-gray-900 truncate">
            {product.title}
          </h3>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-lg font-bold text-green-600">
              {formatCurrency(calculateTotal())}
            </span>
            {originalPrice > product.price && (
              <span className="text-xs text-gray-500 line-through">
                {formatCurrency(originalPrice)}
              </span>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Badge className="bg-green-500 text-white text-xs">
          -{discountPercentage}%
        </Badge>
        <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
      </div>
    </div>
  );

  // Product details component
  const ProductDetails = () => (
    <>
      {/* Product Info - Only show on desktop */}
      {!isMobile && (
        <div className="flex gap-4">
          {product.thumbnail && (
            <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
              <Image
                src={product.thumbnail}
                alt={product.title}
                fill
                className="object-cover"
              />
            </div>
          )}

          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 truncate">
              {product.title}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2">
              {product.description}
            </p>

            {/* Price Display */}
            <div className="flex items-center gap-2 mt-2">
              <span className="text-lg font-bold text-green-600">
                {formatCurrency(product.price)}
              </span>
              {originalPrice > product.price && (
                <>
                  <span className="text-sm text-gray-500 line-through">
                    {formatCurrency(originalPrice)}
                  </span>
                  <Badge  className="text-xs">
                    -{discountPercentage}%
                  </Badge>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Mobile: Show only description */}
      {isMobile && (
        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            {product.description}
          </p>
          {originalPrice > product.price && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500 line-through">
                {formatCurrency(originalPrice)}
              </span>
              <Badge className="text-xs">
                -{discountPercentage}%
              </Badge>
            </div>
          )}
        </div>
      )}

      {/* Delivery Info */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Package className="h-4 w-4" />
        <span>Download disponível após a confirmação do pagamento</span>
      </div>

      {/* Bumps Section */}
      {product.bumps && product.bumps.length > 0 && (
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full justify-between">
              <span className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Ofertas Especiais ({product.bumps.length})
              </span>
              <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent className="space-y-3 mt-3">
            {product.bumps.map((bump: any) => (
              <div
                key={bump.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id={`bump-${bump.id}`}
                    checked={selectedBumps.includes(bump.id)}
                    onChange={(e) => {
                      if (onBumpChange) {
                        if (e.target.checked) {
                          onBumpChange([...selectedBumps, bump.id]);
                        } else {
                          onBumpChange(selectedBumps.filter(id => id !== bump.id));
                        }
                      }
                    }}
                    className="h-4 w-4 text-blue-600 rounded border-gray-300"
                  />
                  <div>
                    <label htmlFor={`bump-${bump.id}`} className="font-medium cursor-pointer">
                      {bump.title}
                    </label>
                    <p className="text-sm text-gray-600">{bump.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">
                    {formatCurrency(bump.price)}
                  </div>
                  {bump.originalPrice && bump.originalPrice > bump.price && (
                    <div className="text-xs text-gray-500 line-through">
                      {formatCurrency(bump.originalPrice)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>
      )}

      <Separator />

      {/* Coupon Section */}
      {onCouponApplied && (
        <CouponSection
          onCouponApply={(couponCode: string) => {
            // Simular aplicação de cupom - em produção, isso seria uma chamada à API
            const mockCoupon = {
              code: couponCode,
              discountAmount: 10, // 10% de desconto
              finalPrice: calculateTotal() * 0.9
            };
            onCouponApplied(mockCoupon);
          }}
          onCouponRemove={() => {
            // TODO: Implementar remoção de cupom
            console.log('Remover cupom');
          }}
          appliedCoupon={appliedCoupon?.code}
        />
      )}

      <Separator />

      {/* Total */}
      <div className="flex justify-between items-center text-lg font-semibold">
        <span>Total:</span>
        <span className="text-green-600">{formatCurrency(calculateTotal())}</span>
      </div>

      {/* Money Back Guarantee */}
      {product.guaranteeType && product.guaranteeType !== 'NONE' && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div className="text-sm flex items-center justify-center gap-2 font-medium text-green-800">
            <ShieldCheck className="h-4 w-4" />
            {product.guaranteeType === '7_DAYS' && 'Garantia de 7 dias'}
            {product.guaranteeType === '30_DAYS' && 'Garantia de 30 dias'}
            {product.guaranteeType === '60_DAYS' && 'Garantia de 60 dias'}
            {product.guaranteeType === '90_DAYS' && 'Garantia de 90 dias'}
          </div>
          <div className="text-xs mt-1 text-green-600">
            {product.guaranteeText || 'Se não ficar satisfeito, devolvemos seu dinheiro'}
          </div>
        </div>
      )}
    </>
  );

  return (
    <div className=" space-y-4">
      {/* Main Product Summary Card */}
      <Card className='bg-white shadow-sm'>
        {isMobile ? (
          <Collapsible open={isOpen} onOpenChange={setIsOpen}>
            <CollapsibleTrigger asChild>
              <div className="cursor-pointer">
                <MobileCompactHeader />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className='p-6 pt-0 space-y-4'>
                <ProductDetails />
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        ) : (
          <>
            <CardHeader className='p-6 space-y-1'>
              <div className='flex items-center justify-between'>
                <CardTitle className='text-lg'>Resumo do Pedido</CardTitle>
                {showConversionElements && (
                  <Badge className="bg-red-500 text-white">
                    LIMITADO
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className='p-6 pt-0 space-y-4'>
              <ProductDetails />
            </CardContent>
          </>
        )}
      </Card>

      {/* Testimonials Cards na Sidebar */}
      {testimonialsEnabled && (
        <div className="mb-6">
          <TestimonialsCards
            productId={product.id}
            maxTestimonials={testimonialsMaxTestimonials}
            showStars={true}
            showAvatars={true}
            showSource={true}
            showVerified={true}
          />
        </div>
      )}

      {/* Cards de Confiança Unificados - Baseado nas configurações */}
      {trustBadgesEnabled && trustBadges && trustBadges.length > 0 && (
        <div onMouseEnter={trackTrustBadgeView} className="space-y-3">
          {trustBadges
            .filter(badge => badge.enabled)
            .map((badge) => (
              <div
                key={badge.id}
                className="flex items-start gap-3 p-4 bg-white border border-blue-200 rounded-lg hover:shadow-sm transition-shadow"
              >
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  {badge.icon === 'shield' && (
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  )}
                  {badge.icon === 'check' && (
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                  {badge.icon === 'mail' && (
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-blue-900 text-sm leading-tight">
                    {badge.title}
                  </h3>
                  <p className="text-blue-600 text-xs mt-1 italic leading-relaxed">
                    {badge.subtitle}
                  </p>
                </div>
              </div>
            ))}
        </div>
      )}

      {/* Conversion Elements */}
      {showConversionElements && (
        <div className="space-y-4">
          {/* Urgency Timer - Removido da sidebar, mantido apenas no header */}

          {/* Scarcity */}
          {scarcityEnabled && (
            <div onMouseEnter={trackScarcityView}>
              <Scarcity
                totalStock={scarcityTotalStock || 100}
                soldCount={scarcitySoldCount || Math.floor(Math.random() * 80) + 10}
                message={scarcityMessage || "Apenas {remaining} vagas restantes!"}
                variant={scarcityVariant || "warning"}
              />
            </div>
          )}
        </div>
      )}




    </div>
  );
}
