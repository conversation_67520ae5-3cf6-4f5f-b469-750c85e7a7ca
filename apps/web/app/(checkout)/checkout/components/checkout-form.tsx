// apps/web/(checkout)/checkout/components/checkout-form.tsx

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { CheckoutFormData, checkoutFormSchema } from './types';
import { CustomerForm } from './customer-form';
import { PaymentForm } from './payment-form';
import { SidebarBanner } from './sidebar-banner';
import { TrustBadges } from './trust-badges';

import { useToast } from '@ui/hooks/use-toast';
import { useState, useEffect } from 'react';
import { ProductSummaryCard } from './checkout-summary-card';
import { EnhancedCheckoutSummary } from './enhanced-checkout-summary';
import { CheckoutTestimonialsWrapper } from './checkout-testimonials-wrapper';
import { useAnalytics } from '@analytics';
import { User, CreditCard } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { RadioGroup } from '@ui/components/radio-group';
import { Input } from '@ui/components/input';
import { Separator } from '@ui/components/separator';
import { UrgentPurchaseButton, PixButton, BoletoButton } from '@ui/components/conversion';
import { CPFCNPJInput } from './cpf-cnpj-input';
import { FormField } from './form-field';
import { CPFCNPJFormField } from './cpf-cnpj-form-field';
import { PhoneInputWithFlag } from './phone-input-with-flag';
import { PaymentFormPixInfo } from './payment-form-pix-info';
import { PaymentFormBoletoInfo } from './payment-form-boleto-info';
import { CreditCardForm } from './checkout-credit-card-form';
import { CheckoutFooter } from './checkout-footer';
import { EnhancedOrderBumpList } from './enhanced-order-bump-list';
import { mockOrderBumps, generateOrderBumpsForProduct } from './mock-orderbumps';
import { PaymentOption } from './payment-option';
import type { Offer } from './types';

interface CheckoutFormProps {
	product: {
		id: string;
		title: string;
		description?: string | null;
		type: 'COURSE' | 'MENTORING' | 'EBOOK';
		price: number;
		originalPrice?: number;
		regularPrice?: number | null;
		installmentsLimit: number;
		enableInstallments?: boolean;
		thumbnail?: string | null;
		checkoutBanner?: string | null;
		checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
		acceptedPayments?: string[];
		checkoutSettings?: any;
		customCheckoutUrl?: string | null;
		successUrl?: string | null;
		cancelUrl?: string | null;
		termsUrl?: string | null;
		selectedOffer?: {
			id: string;
			title: string;
			price: number;
			type: string;
		} | null;
		offers?: {
			id: string;
			title: string;
			description?: string | null;
			price: number;
			type: string;
		}[];
	};
	offerId?: string;
	// Conversion settings
	trustBadgesSettings?: any;
	testimonialsSettings?: any;
	sidebarSettings?: any;
	urgencySettings?: any;
	guaranteeCardsSettings?: any;
}

// Definição padrão de métodos de pagamento para referência
const PAYMENT_METHODS = {
	CREDIT_CARD: {
		value: 'CREDIT_CARD',
		title: 'Cartão',
		description: '',
		icon: 'credit-card',
	},
	PIX: {
		value: 'PIX',
		title: 'Pix',
		description: 'Pagamento instantâneo',
		icon: 'pix',
	},
	BOLETO: {
		value: 'BOLETO',
		title: 'Boleto',
		description: 'Confirmação em até 24 horas',
		icon: 'boleto',
	},
};

const defaultAcceptedPayments = ['CREDIT_CARD', 'PIX', 'BOLETO'];
const brands = ['visa', 'mastercard', 'amex', 'elo'];

interface UnifiedCheckoutCardProps {
	loading: boolean;
	totalAmount: number;
	installmentsLimit: number;
	enableInstallments?: boolean;
	acceptedPayments?: string[];
	offers?: Offer[];
	selectedBumps: string[];
	onBumpChange: (ids: string[]) => void;
}

function UnifiedCheckoutCard({
	loading,
	totalAmount,
	installmentsLimit,
	enableInstallments = false,
	acceptedPayments = defaultAcceptedPayments,
	offers = [],
	selectedBumps,
	onBumpChange,
}: UnifiedCheckoutCardProps) {
	const form = useFormContext<CheckoutFormData>();
	const paymentMethod = form.watch('paymentMethod');

	const {
		control,
		formState: { errors },
		setError,
		clearErrors,
	} = form;

	// Função para validar email
	const validateEmail = (email: string) => {
		if (!email.trim()) {
			clearErrors('customerData.email');
			return;
		}

		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			setError('customerData.email', { message: 'Email inválido' });
		} else {
			clearErrors('customerData.email');
		}
	};

	// Função para validar telefone
	const validatePhone = (phone: string) => {
		if (!phone.trim()) {
			clearErrors('customerData.phone');
			return;
		}

		const cleanPhone = phone.replace(/\D/g, '');
		if (cleanPhone.length < 10) {
			setError('customerData.phone', { message: 'Telefone deve ter pelo menos 10 dígitos' });
		} else {
			clearErrors('customerData.phone');
		}
	};

	// Função para validar nome
	const validateName = (name: string) => {
		if (!name.trim()) {
			clearErrors('customerData.name');
			return;
		}

		if (name.trim().length < 3) {
			setError('customerData.name', { message: 'Nome deve ter pelo menos 3 caracteres' });
		} else {
			clearErrors('customerData.name');
		}
	};

	// Função para validar documento (CPF/CNPJ)
	const validateDocument = (document: string) => {
		if (!document.trim()) {
			clearErrors('customerData.cpf');
			return;
		}

		const cleanDocument = document.replace(/\D/g, '');
		if (cleanDocument.length === 11) {
			// Validar CPF
			clearErrors('customerData.cpf');
		} else if (cleanDocument.length === 14) {
			// Validar CNPJ
			clearErrors('customerData.cpf');
		} else {
			setError('customerData.cpf', { message: 'Documento deve ter 11 dígitos (CPF) ou 14 dígitos (CNPJ)' });
		}
	};

	// Filtrar apenas métodos de pagamento permitidos pelo produto
	const allowedPaymentMethods = Object.values(PAYMENT_METHODS).filter(
		(method) => acceptedPayments.includes(method.value)
	);

	const renderPaymentMethod = () => {
		switch (paymentMethod) {
			case 'CREDIT_CARD':
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
			case 'PIX':
				return <PaymentFormPixInfo />;
			case 'BOLETO':
				return <PaymentFormBoletoInfo />;
			default:
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
		}
	};

	return (
		<Card className='bg-white shadow-sm'>
			<CardHeader className='p-6 space-y-1'>
				<div className='flex items-center gap-2'>
					<User className='h-5 w-5 text-muted-foreground' />
					<CardTitle className='text-lg'>Informações</CardTitle>
				</div>
				{/* <p className='text-sm text-muted-foreground'>
					Preencha seus dados para completar a compra
				</p> */}
			</CardHeader>

			<CardContent className='p-6 pt-0 space-y-6'>
				{/* Customer Information Section */}
				<div className='space-y-4'>
					<h3 className='text-base font-medium text-gray-900'>Dados Pessoais</h3>
					<div className='grid gap-4 md:grid-cols-2'>
						<FormField
							name='customerData.name'
							label='Nome completo'
							error={errors.customerData?.name?.message}
						>
							<Controller
								name='customerData.name'
								control={control}
								render={({ field }) => (
									<Input
										{...field}
										placeholder='Digite seu nome completo'
										onBlur={(e) => {
											field.onBlur();
											validateName(e.target.value);
										}}
									/>
								)}
							/>
						</FormField>

						<FormField
							name='customerData.email'
							label='E-mail'
							error={errors.customerData?.email?.message}
						>
							<Controller
								name='customerData.email'
								control={control}
								render={({ field }) => (
									<Input
										{...field}
										type='email'
										placeholder='<EMAIL>'
										onChange={(e) => field.onChange(e.target.value)}
										onBlur={(e) => {
											field.onBlur();
											validateEmail(e.target.value);
										}}
									/>
								)}
							/>
						</FormField>

						<CPFCNPJFormField
							name='customerData.cpf'
							label='CPF/CNPJ'
							error={errors.customerData?.cpf?.message}
						>
							<Controller
								name='customerData.cpf'
								control={control}
								render={({ field }) => (
									<CPFCNPJInput
										value={field.value}
										onChange={field.onChange}
										onBlur={(e) => {
											field.onBlur();
											validateDocument(field.value);
										}}
									/>
								)}
							/>
						</CPFCNPJFormField>

						<FormField
							name='customerData.phone'
							label='Celular'
							error={errors.customerData?.phone?.message}
						>
							<Controller
								name='customerData.phone'
								control={control}
								render={({ field }) => (
									<PhoneInputWithFlag
										value={field.value}
										onChange={field.onChange}
										onBlur={(e) => {
											field.onBlur();
											validatePhone(field.value);
										}}
										error={!!errors.customerData?.phone}
									/>
								)}
							/>
						</FormField>
					</div>
				</div>

				{/* Payment Method Section */}
				<div className='space-y-4 mt-8'>
					<div className='flex items-center justify-between'>
						<div className='flex items-center gap-2'>
							<CreditCard className='h-5 w-5 text-muted-foreground' />
							<h3 className='text-base font-medium text-gray-900'>Pagamento</h3>
						</div>

						<div className='brand-icons flex items-center gap-1 md:gap-3'>
							{brands.map((brand) => (
								<img
									key={brand}
									src={`/images/payments/card/${brand}.svg`}
									alt={brand}
									className='h-4 md:h-6 w-auto border rounded-[3px]'
								/>
							))}
						</div>
					</div>

					{allowedPaymentMethods.length > 0 && (
						<Controller
							name='paymentMethod'
							control={form.control}
							render={({ field }) => (
								<RadioGroup
									value={field.value}
									onValueChange={(value: 'CREDIT_CARD' | 'PIX' | 'BOLETO') => {
										field.onChange(value);
										if (value === 'PIX' || value === 'BOLETO') {
											form.setValue('creditCard', undefined);
										}
									}}
									className='grid grid-cols-3 gap-4'
								>
									{allowedPaymentMethods.map((method) => (
										<PaymentOption
											key={method.value}
											value={method.value}
											title={method.title}
											description={method.description}
											icon={method.icon as 'credit-card' | 'pix' | 'boleto'}
											selected={paymentMethod === method.value}
										/>
									))}
								</RadioGroup>
							)}
						/>
					)}

					{renderPaymentMethod()}
				</div>

				{/* Order Bumps */}
				{offers && offers.length > 0 && (
					<div className='mt-8'>
						<EnhancedOrderBumpList
							offers={offers}
							selected={selectedBumps}
							onSelect={onBumpChange}
							title="Ofertas especiais:"
						/>
					</div>
				)}

				<Separator />

				{/* Payment Action Button */}
				<div className='space-y-4 mt-8'>
					{paymentMethod === 'PIX' ? (
						<PixButton
							onClick={() => {
								console.log('=== PIX BUTTON CLICKED ===');
								console.log('Form values:', form.getValues());
							}}
							disabled={loading}
							loading={loading}
						/>
					) : paymentMethod === 'BOLETO' ? (
						<BoletoButton
							onClick={() => {
								console.log('=== BOLETO BUTTON CLICKED ===');
								console.log('Form values:', form.getValues());
							}}
							disabled={loading}
							loading={loading}
						/>
					) : (
						<UrgentPurchaseButton
							discount={33}
							onClick={() => {
								console.log('=== URGENT BUTTON CLICKED ===');
								console.log('Form values:', form.getValues());
							}}
							disabled={loading}
							loading={loading}
						/>
					)}

					<CheckoutFooter />
				</div>
			</CardContent>
		</Card>
	);
}

export function CheckoutForm({ product, offerId, trustBadgesSettings, testimonialsSettings, sidebarSettings, urgencySettings, guaranteeCardsSettings }: CheckoutFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { toast } = useToast();
	const { trackEvent } = useAnalytics();

	// Estados para controle do checkout
	const [selectedBumps, setSelectedBumps] = useState<string[]>([]);
	const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
	const [formStartTime] = useState(Date.now());

	// Payment processing state
	const [isProcessingPayment, setIsProcessingPayment] = useState(false);

	// Form setup com valores padrão
	const getDefaultValues = (): CheckoutFormData => {
		// Valores dos query params (ex: email pré-preenchido)
		const email = searchParams.get('email');
		return {
			customerData: {
				email: email || '',
				name: '',
				cpf: '',
				phone: '',
			},
			paymentMethod: 'CREDIT_CARD',
			productId: product.id,
			creditCard: {
				cardNumber: '',
				cardHolder: '',
				cardExpiry: '',
				cardCvv: '',
				installments: 1, // Valor padrão sempre 1
			},
		};
	};

	const methods = useForm<CheckoutFormData>({
		resolver: zodResolver(checkoutFormSchema),
		defaultValues: getDefaultValues(),
		mode: 'onBlur',
	});

	// Tracking de início do checkout
	useEffect(() => {
		trackEvent('checkout_started', {
			productId: product.id,
			productTitle: product.title,
			productPrice: product.price,
			timestamp: Date.now(),
		});
	}, []);

	// Função para calcular total
	const calculateTotal = (basePrice: number, bumps: string[], offers?: any[]) => {
		if (!offers || bumps.length === 0) return basePrice;

		const selectedOffers = offers.filter(offer => bumps.includes(offer.id));
		const offersTotal = selectedOffers.reduce((acc, offer) => acc + offer.price, 0);

		return basePrice + offersTotal;
	};

	// Handler para cupom aplicado
	const handleCouponApplied = (coupon: any) => {
		setAppliedCoupon(coupon);
		trackEvent('coupon_applied', {
			productId: product.id,
			couponCode: coupon.code,
			discountAmount: coupon.value,
		});
	};



		// Handler principal de submit
	const onSubmit = async (data: CheckoutFormData) => {
		console.log('=== SUBMIT FUNCTION CALLED ===');
		console.log('Form data submitted:', data);

		setIsProcessingPayment(true);

		try {
			const totalAmount = appliedCoupon?.finalPrice || calculateTotal(product.price, selectedBumps, product.offers);

			const paymentData = {
				...data,
				offerId: offerId,
				orderBumpIds: selectedBumps,
				couponCode: appliedCoupon?.code,
				...(data.paymentMethod === 'CREDIT_CARD' && data.creditCard
					? {
							creditCard: {
								...data.creditCard,
								cardNumber: data.creditCard.cardNumber.replace(/\D/g, ''),
								cardExpiry: data.creditCard.cardExpiry,
							},
						}
					: {}),
			};

			console.log('Calling processPayment API...');
			const response = await fetch('/api/checkout/process-payment', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(paymentData),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Erro ao processar pagamento');
			}

			const result = await response.json();

			trackEvent('payment_success', {
				productId: product.id,
				orderId: result.orderId,
				paymentMethod: data.paymentMethod,
				totalAmount,
				timeToComplete: Date.now() - formStartTime,
			});

			// Redirecionar para o checkout do Stripe
			if (result.checkoutUrl) {
				window.location.href = result.checkoutUrl;
			} else {
				// Fallback para página de sucesso
				router.push(`/checkout/success?orderId=${result.orderId}`);
			}
		} catch (error: any) {
			console.error('Payment failed:', error);

			trackEvent('payment_failed', {
				productId: product.id,
				paymentMethod: data.paymentMethod,
				error: error.message,
				timeSpent: Date.now() - formStartTime,
			});

			// Redirecionamento personalizado em caso de erro
			if (product.cancelUrl) {
				router.push(product.cancelUrl);
				return;
			}

			toast.error(`Erro ao processar pagamento: ${getErrorMessage(error)}`);
		} finally {
			setIsProcessingPayment(false);
		}
	};

	// Função para obter mensagem de erro amigável
	const getErrorMessage = (error: any): string => {
		const message = error?.message || '';

		if (message.includes('network') || message.includes('connection')) {
			return 'Problema de conexão. Verifique sua internet e tente novamente.';
		}

		if (message.includes('card') || message.includes('cartão')) {
			return 'Problema com o cartão. Verifique os dados e tente novamente.';
		}

		if (message.includes('insufficient')) {
			return 'Saldo insuficiente. Tente outro cartão ou método de pagamento.';
		}

		return 'Erro inesperado. Tente novamente ou entre em contato com o suporte.';
	};

	// Apply custom checkout styles from product settings if available
	const checkoutStyles = product.checkoutSettings?.styles || {};

	return (
		<FormProvider {...methods}>
			<form
				onSubmit={methods.handleSubmit(onSubmit)}
				className='container max-w-6xl mx-auto px-0 lg:px-4'
				// style={checkoutStyles.formContainer || {}}
			>
				{/* Mobile Summary - Always visible at top on mobile */}
				<div className='lg:hidden mb-6'>
					<EnhancedCheckoutSummary
						product={product}
						selectedBumps={selectedBumps}
						onBumpChange={setSelectedBumps}
						isMobile={true}
						appliedCoupon={appliedCoupon}
						onCouponApplied={handleCouponApplied}
						showConversionElements={true}
					/>
				</div>

				<div className='grid gap-8 lg:grid-cols-12'>
					{/* Main Form Content */}
					<div className='lg:col-span-8'>
						<UnifiedCheckoutCard
							totalAmount={
								appliedCoupon
									? appliedCoupon.finalPrice
									: calculateTotal(product.price, selectedBumps, product.offers)
							}
							installmentsLimit={product.installmentsLimit}
							enableInstallments={product.enableInstallments}
							acceptedPayments={product.acceptedPayments}
							loading={isProcessingPayment}
							offers={
								product.offers && product.offers.length > 0
									? product.offers.map((offer) => ({
										...offer,
										description: offer.description || null,
										type: offer.type as 'ORDER_BUMP' | 'UPSELL' | 'DOWNSELL',
									}))
									: generateOrderBumpsForProduct(product.id, product.type)
							}
							selectedBumps={selectedBumps}
							onBumpChange={setSelectedBumps}
						/>
					</div>

					{/* Desktop Summary */}
					<div className='hidden lg:block lg:col-span-4'>
						<div className='lg:sticky lg:top-24 space-y-4'>
							


							{/* Resumo da Compra - Condicional baseado na configuração */}
							{sidebarSettings?.showSummary !== false && (
								<CheckoutTestimonialsWrapper
									productId={product.id}
									testimonialsSettings={{
										enabled: testimonialsSettings?.enabled as boolean || true,
										maxTestimonials: testimonialsSettings?.maxTestimonials as number || 3,
										autoPlay: testimonialsSettings?.autoPlay as boolean || true,
										autoPlayInterval: testimonialsSettings?.autoPlayInterval as number || 5,
										showControls: testimonialsSettings?.showControls as boolean || true,
										showStars: testimonialsSettings?.showStars as boolean || true,
										showAvatars: testimonialsSettings?.showAvatars as boolean || true,
										backgroundColor: 'bg-white',
										textColor: 'text-gray-800',
										borderColor: 'border-gray-200',
									}}
								>
									{(apiTestimonials) => (
										<EnhancedCheckoutSummary
											product={product}
											selectedBumps={selectedBumps}
											onBumpChange={setSelectedBumps}
											isMobile={false}
											appliedCoupon={appliedCoupon}
											onCouponApplied={handleCouponApplied}
											showConversionElements={true}
										// Trust Badges
										trustBadgesEnabled={trustBadgesSettings?.enabled as boolean || true}
										trustBadges={trustBadgesSettings?.badges as any[] || undefined}
										trustBadgesLayout={trustBadgesSettings?.layout as 'horizontal' | 'vertical' | 'grid' || 'horizontal'}
										trustBadgesShowDescriptions={trustBadgesSettings?.showDescriptions as boolean || true}
										trustBadgesBackgroundColor={trustBadgesSettings?.backgroundColor as string || 'bg-blue-50'}
										trustBadgesTextColor={trustBadgesSettings?.textColor as string || 'text-blue-800'}
										trustBadgesBorderColor={trustBadgesSettings?.borderColor as string || 'border-blue-200'}
										// Testimonials - Agora da API
										testimonialsEnabled={testimonialsSettings?.enabled as boolean || true}
										testimonials={apiTestimonials}
										testimonialsMaxTestimonials={testimonialsSettings?.maxTestimonials as number || 3}
										testimonialsAutoPlay={testimonialsSettings?.autoPlay as boolean || true}
										testimonialsAutoPlayInterval={testimonialsSettings?.autoPlayInterval as number || 5}
										testimonialsShowControls={testimonialsSettings?.showControls as boolean || true}
										testimonialsShowStars={testimonialsSettings?.showStars as boolean || true}
										testimonialsShowAvatars={testimonialsSettings?.showAvatars as boolean || true}
										// Urgency
										urgencyEnabled={urgencySettings?.enabled as boolean || false}
										urgencyMessage={urgencySettings?.message as string || "Oferta especial termina em:"}
										urgencyVariant={urgencySettings?.variant as 'warning' | 'danger' | 'default' || 'warning'}
										// Guarantee Cards
										guaranteeCards={guaranteeCardsSettings?.cards as any[] || undefined}
										guaranteeCardsLayout={guaranteeCardsSettings?.layout as 'horizontal' | 'vertical' | 'grid' || 'vertical'}
									/>
									)}
								</CheckoutTestimonialsWrapper>
							)}


							{/* Sidebar Banner */}
							{sidebarSettings && (
								<SidebarBanner
									enabled={sidebarSettings.enabled as boolean || false}
									bannerUrl={sidebarSettings.bannerUrl as string || null}
									title={sidebarSettings.title as string || "Informações Importantes"}
									content={sidebarSettings.content as string || "Adicione informações úteis para seus clientes aqui."}
									backgroundColor={sidebarSettings.backgroundColor as string || 'bg-blue-50'}
									textColor={sidebarSettings.textColor as string || 'text-blue-800'}
									borderColor={sidebarSettings.borderColor as string || 'border-blue-200'}
									borderRadius={sidebarSettings.borderRadius as string || 'rounded-lg'}
									shadow={sidebarSettings.shadow as boolean || true}
								/>
							)}

							
						</div>
					</div>
				</div>


			</form>
		</FormProvider>
	);
}
