// apps/web/(checkout)/checkout/types.ts
import { z } from "zod";

// Função para validar CPF
function validateCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, '');

  if (cleanCPF.length !== 11) return false;
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false; // Todos os dígitos iguais

  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

  return true;
}

// Função para validar CNPJ
function validateCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, '');

  if (cleanCNPJ.length !== 14) return false;
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false; // Todos os dígitos iguais

  let sum = 0;
  let weight = 2;
  for (let i = 11; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  let remainder = sum % 11;
  const firstDigit = remainder < 2 ? 0 : 11 - remainder;
  if (firstDigit !== parseInt(cleanCNPJ.charAt(12))) return false;

  sum = 0;
  weight = 2;
  for (let i = 12; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  remainder = sum % 11;
  const secondDigit = remainder < 2 ? 0 : 11 - remainder;
  if (secondDigit !== parseInt(cleanCNPJ.charAt(13))) return false;

  return true;
}

// Schemas de Validação - Permissivo durante digitação
export const customerDataSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").refine((val) => val.trim().length >= 3, "Nome deve ter pelo menos 3 caracteres"),
  email: z.string().min(1, "Email é obrigatório").email("Email inválido"),
  phone: z.string().min(1, "Telefone é obrigatório").refine((val) => val.replace(/\D/g, "").length >= 10, "Telefone deve ter pelo menos 10 dígitos"),
  cpf: z.string().min(1, "Informe um CPF ou CNPJ").refine((val) => {
    const cleanVal = val.replace(/\D/g, '');
    if (cleanVal.length === 11) {
      return validateCPF(val);
    } else if (cleanVal.length === 14) {
      return validateCNPJ(val);
    }
    return false;
  }, "CPF ou CNPJ inválido"),
});

// Schema rigoroso para validação no submit
export const customerDataSubmitSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").refine((val) => val.trim().length >= 3, "Nome deve ter pelo menos 3 caracteres"),
  email: z.string().min(1, "Email é obrigatório").email("Email inválido"),
  phone: z.string().min(1, "Telefone é obrigatório").refine((val) => val.replace(/\D/g, "").length >= 10, "Telefone deve ter pelo menos 10 dígitos"),
  cpf: z.string().min(1, "Informe um CPF ou CNPJ").refine((val) => {
    const cleanVal = val.replace(/\D/g, '');
    if (cleanVal.length === 11) {
      return validateCPF(val);
    } else if (cleanVal.length === 14) {
      return validateCNPJ(val);
    }
    return false;
  }, "CPF ou CNPJ inválido"),
});

export const creditCardSchema = z.object({
  cardNumber: z.string().min(16, "Número do cartão inválido"),
  cardHolder: z.string().min(3, "Nome do titular inválido"),
  cardExpiry: z.string().min(5, "Data de validade inválida"),
  cardCvv: z.string().min(3, "CVV inválido"),
  installments: z.number().min(1).max(12),
});

export const checkoutFormSchema = z.object({
  customerData: customerDataSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: creditCardSchema.optional().superRefine((val, ctx) => {
    // Use type assertion para acessar o parent
    const paymentMethod = (ctx as any).path?.[1]?.input?.paymentMethod;
    if (paymentMethod === "CREDIT_CARD" && !val) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Dados do cartão são obrigatórios para pagamento com cartão de crédito",
      });
    }
  }),
  productId: z.string(),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
});

// Schema para validação no submit
export const checkoutFormSubmitSchema = z.object({
  customerData: customerDataSubmitSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: z.optional(z.object({
    cardNumber: z.string().min(16, "Número do cartão inválido"),
    cardHolder: z.string().min(3, "Nome do titular inválido"),
    cardExpiry: z.string().min(5, "Data de validade inválida"),
    cardCvv: z.string().min(3, "CVV inválido"),
    installments: z.number().min(1).max(12).default(1),
  })),
  productId: z.string().min(1, "ID do produto é obrigatório"),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
}).refine((data) => {
  // Validar cartão de crédito apenas se o método for CREDIT_CARD
  if (data.paymentMethod === "CREDIT_CARD" && !data.creditCard) {
    return false;
  }
  return true;
}, {
  message: "Dados do cartão são obrigatórios para pagamento com cartão de crédito",
  path: ["creditCard"],
});

// Types inferidos dos schemas
export type CustomerData = z.infer<typeof customerDataSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;
export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

// Types específicos para os componentes
export interface CheckoutProduct {
  id: string;
  title: string;
  description?: string | null;
  type: 'COURSE' | 'MENTORING' | 'EBOOK';
  price: number;
  installmentsLimit: number;
  thumbnail?: string | null;
  offers?: {
    id: string;
    title: string;
    description?: string | null;
    price: number;
    type: string;
  }[];
  creator: {
    id: string;
    name: string;
  };
}

export interface Offer {
  id: string;
  title: string;
  description: string | null;
  price: number;
  type: 'ORDER_BUMP' | 'UPSELL' | 'DOWNSELL';
  thumbnail?: string | null;
}

export interface ProductOffer {
  id: string;
  title: string;
  description?: string;
  price: number;
  type: "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  thumbnail?: string;
  originalPrice?: number; // Para mostrar o desconto
}

export interface PaymentOption {
  method: "CREDIT_CARD" | "PIX";
  title: string;
  description: string;
  icon: React.ReactNode;
}

// Types para os estados do pedido
export type OrderStatus =
  | "PENDING"
  | "PROCESSING"
  | "PAID"
  | "FAILED"
  | "REFUNDED"
  | "CANCELLED";

export interface Order {
  id: string;
  status: OrderStatus;
  amount: number;
  productId: string;
  customerData: CustomerData;
  paymentMethod: "CREDIT_CARD" | "PIX";
  paymentId?: string;
  orderBumps?: ProductOffer[];
  createdAt: Date;
  updatedAt: Date;
}

// Types para integrações
export interface AsaasPaymentResponse {
  id: string;
  status: string;
  paymentLink?: string;
  pixQrCode?: {
    encodedImage: string;
    payload: string;
  };
}

export interface PaymentProcessingResult {
  orderId: string;
  status: OrderStatus;
  paymentMethod: "CREDIT_CARD" | "PIX";
  pixCode?: {
    qrCode: string;
    payload: string;
  };
  successUrl?: string;
  errorUrl?: string;
}

// Props interfaces para os componentes
export interface CreditCardDisplayProps {
  number?: string;
  name?: string;
  expiry?: string;
  cvc?: string;
  focused?: "number" | "name" | "expiry" | "cvc" | null;
  className?: string;
}

export interface CreditCardInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  error?: string;
  value?: string;
  onChange?: (value: string) => void;
  onCardTypeChange?: (type: CardType) => void;
}

export type CardType = "visa" | "mastercard" | "amex" | "elo" | "";

export interface FormFieldProps {
  label: string;
  error?: string;
  children: React.ReactNode;
  required?: boolean;
  helpText?: string;
}

export interface OrderBumpListProps {
  offers: ProductOffer[];
  selected: string[];
  onSelect: (selected: string[]) => void;
}

export interface PaymentFormProps {
  loading: boolean;
  price: number;
  installmentsLimit: number;
  onPaymentMethodChange?: (method: "CREDIT_CARD" | "PIX") => void;
}

export interface ProductSummaryProps {
  product: CheckoutProduct;
  selectedBumps?: ProductOffer[];
  couponApplied?: {
    code: string;
    discountAmount: number;
  };
}
