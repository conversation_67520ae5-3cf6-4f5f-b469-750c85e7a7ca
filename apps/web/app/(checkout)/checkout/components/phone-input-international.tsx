// components/phone-input-international.tsx
'use client';

import { cn } from '@ui/lib';
import { forwardRef, useRef, useEffect, useState } from 'react';
import { Input } from '@ui/components/input';
import dynamic from 'next/dynamic';
import 'react-international-phone/style.css';

// Importar o componente PhoneInput dinamicamente para evitar problemas de SSR
const PhoneInput = dynamic(
	() => import('react-international-phone').then((mod) => mod.PhoneInput),
	{
		ssr: false,
	}
);

interface InternationalPhoneInputProps
	extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
	value: string;
	onChange?: (value: string) => void;
	error?: boolean;
	errorMessage?: string;
}

export const InternationalPhoneInput = forwardRef<
	HTMLInputElement,
	InternationalPhoneInputProps
>(({ className, value, onChange, error, errorMessage, ...props }, ref) => {
	// Estado para controlar se o componente está montado no cliente
	const [isMounted, setIsMounted] = useState(false);

	// Efeito para marcar o componente como montado no cliente
	useEffect(() => {
		setIsMounted(true);
	}, []);

	// Usamos um ref para o input
	const phoneInputRef = useRef<any>(null);

	// Sincronizar a ref interna com a ref externa, se fornecida
	useEffect(() => {
		if (phoneInputRef.current && phoneInputRef.current.inputRef) {
			if (ref && typeof ref === 'function') {
				ref(phoneInputRef.current.inputRef);
			} else if (ref) {
				ref.current = phoneInputRef.current.inputRef;
			}
		}
	}, [ref, phoneInputRef.current]);

	return (
		<div className='space-y-1'>
			<div className='relative'>
				{isMounted ? (
					<PhoneInput
						defaultCountry='br'
						value={value}
						placeholder='(00) 00000-0000'
						prefix='+'
						preferredCountries={['br', 'us', 'pt']}
						charAfterDialCode=' '
						forceDialCode={true}
						disableDialCodeAndPrefix={false}
						defaultMask='(..) .....-....'
						onChange={onChange}
						inputProps={{
							...props,
							required: true,
							className: cn(
								'flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:border-primary focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
								error && 'border-destructive',
								className
							),
						}}
						ref={phoneInputRef}
						style={{
							'--phoneInputCountrySelectArrowColor': 'hsl(var(--muted-foreground))',
							'--phoneInputCountrySelectArrowColorHover': 'hsl(var(--foreground))',
							'--phoneInputCountrySelectBackgroundColor': 'transparent',
							'--phoneInputCountrySelectBackgroundColorHover': 'hsl(var(--muted))',
							'--phoneInputCountrySelectBorderColor': 'transparent',
							'--phoneInputCountrySelectBorderColorHover': 'transparent',
							'--phoneInputCountrySelectBorderColorFocus': 'transparent',
							'--phoneInputCountrySelectBorderRadius': '0',
							'--phoneInputCountrySelectFontSize': '0.875rem',
							'--phoneInputCountrySelectFontWeight': '400',
							'--phoneInputCountrySelectHeight': '2.5rem',
							'--phoneInputCountrySelectMinWidth': '3rem',
							'--phoneInputCountrySelectPadding': '0.5rem 0.25rem',
							'--phoneInputInputBackgroundColor': 'transparent',
							'--phoneInputInputBorderColor': 'hsl(var(--border))',
							'--phoneInputInputBorderColorFocus': 'hsl(var(--ring))',
							'--phoneInputInputBorderRadius': '0.375rem',
							'--phoneInputInputFontSize': '1rem',
							'--phoneInputInputHeight': '2.5rem',
							'--phoneInputInputPadding': '0.5rem 0.75rem',
							'--phoneInputInputWidth': '100%',
							'--phoneInputLabelColor': 'hsl(var(--foreground))',
							'--phoneInputLabelFontSize': '0.875rem',
							'--phoneInputLabelFontWeight': '500',
							'--phoneInputLabelMarginBottom': '0.25rem',
							'--phoneInputPhoneInputContainerDisplay': 'flex',
							'--phoneInputPhoneInputContainerFlexDirection': 'row',
							'--phoneInputPhoneInputContainerGap': '0',
							'--phoneInputPhoneInputContainerWidth': '100%',
						} as React.CSSProperties}
					/>
				) : (
					<Input
						placeholder='(00) 00000-0000'
						disabled
						className={cn(
							'flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:border-primary focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
							error && 'border-destructive',
							className
						)}
					/>
				)}
			</div>
			{error && errorMessage && (
				<p className='text-sm font-medium text-destructive'>{errorMessage}</p>
			)}
		</div>
	);
});

InternationalPhoneInput.displayName = 'InternationalPhoneInput';
