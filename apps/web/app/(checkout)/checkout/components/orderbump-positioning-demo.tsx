'use client';

import { EnhancedOrderBumpList } from './enhanced-order-bump-list';
import { mockOrderBumps } from './mock-orderbumps';
import { useState } from 'react';

interface OrderbumpPositioningDemoProps {
	position: 'before-payment' | 'after-payment' | 'sidebar' | 'floating';
	selectedBumps: string[];
	onBumpChange: (bumps: string[]) => void;
}

export function OrderbumpPositioningDemo({
	position,
	selectedBumps,
	onBumpChange,
}: OrderbumpPositioningDemoProps) {
	const [isVisible, setIsVisible] = useState(true);

	if (!isVisible) return null;

	const getPositionStyles = () => {
		switch (position) {
			case 'before-payment':
				return 'mb-6 border-t pt-6';
			case 'after-payment':
				return 'mt-6 border-t pt-6';
			case 'sidebar':
				return 'sticky top-24 bg-white p-4 rounded-lg border shadow-sm';
			case 'floating':
				return 'fixed bottom-4 right-4 w-80 bg-white p-4 rounded-lg border shadow-lg z-50';
			default:
				return '';
		}
	};

	const getTitle = () => {
		switch (position) {
			case 'before-payment':
				return 'Antes de finalizar o pagamento:';
			case 'after-payment':
				return 'Última chance - ofertas especiais:';
			case 'sidebar':
				return 'Ofertas em destaque:';
			case 'floating':
				return 'Não perca!';
			default:
				return 'Aproveite e compre junto:';
		}
	};

	const getSubtitle = () => {
		switch (position) {
			case 'before-payment':
				return 'Adicione estes produtos ao seu pedido com desconto especial';
			case 'after-payment':
				return 'Estas ofertas não estarão mais disponíveis após o pagamento';
			case 'sidebar':
				return 'Produtos recomendados para você';
			case 'floating':
				return 'Ofertas limitadas disponíveis';
			default:
				return 'Ofertas especiais disponíveis apenas durante o checkout';
		}
	};

	return (
		<div className={getPositionStyles()}>
			{/* Botão de fechar para posição flutuante */}
			{position === 'floating' && (
				<div className="flex justify-end mb-2">
					<button
						onClick={() => setIsVisible(false)}
						className="text-gray-400 hover:text-gray-600 text-sm"
					>
						✕
					</button>
				</div>
			)}

			<EnhancedOrderBumpList
				offers={mockOrderBumps.slice(0, 2)} // Mostrar apenas 2 para não sobrecarregar
				selected={selectedBumps}
				onSelect={onBumpChange}
				title={getTitle()}
				subtitle={getSubtitle()}
			/>
		</div>
	);
}

// Componente para demonstrar diferentes posições
export function OrderbumpPositioningShowcase() {
	const [selectedBumps, setSelectedBumps] = useState<string[]>([]);

	return (
		<div className="space-y-8">
			<div className="text-center">
				<h2 className="text-2xl font-bold mb-2">Demonstração de Posicionamento de Orderbumps</h2>
				<p className="text-gray-600">
					Veja como os orderbumps podem ser posicionados em diferentes partes do checkout
				</p>
			</div>

			{/* Posição: Antes do pagamento */}
			<div className="bg-gray-50 p-6 rounded-lg">
				<h3 className="text-lg font-semibold mb-4">1. Antes do Pagamento</h3>
				<p className="text-sm text-gray-600 mb-4">
					Posicionado entre os dados do cliente e os métodos de pagamento
				</p>
				<OrderbumpPositioningDemo
					position="before-payment"
					selectedBumps={selectedBumps}
					onBumpChange={setSelectedBumps}
				/>
			</div>

			{/* Posição: Sidebar */}
			<div className="bg-gray-50 p-6 rounded-lg">
				<h3 className="text-lg font-semibold mb-4">2. Na Sidebar</h3>
				<p className="text-sm text-gray-600 mb-4">
					Posicionado na sidebar direita, sempre visível durante o scroll
				</p>
				<div className="max-w-sm">
					<OrderbumpPositioningDemo
						position="sidebar"
						selectedBumps={selectedBumps}
						onBumpChange={setSelectedBumps}
					/>
				</div>
			</div>

			{/* Posição: Após o pagamento */}
			<div className="bg-gray-50 p-6 rounded-lg">
				<h3 className="text-lg font-semibold mb-4">3. Após o Pagamento</h3>
				<p className="text-sm text-gray-600 mb-4">
					Posicionado após os métodos de pagamento, como última chance
				</p>
				<OrderbumpPositioningDemo
					position="after-payment"
					selectedBumps={selectedBumps}
					onBumpChange={setSelectedBumps}
				/>
			</div>

			{/* Posição: Flutuante */}
			<div className="bg-gray-50 p-6 rounded-lg">
				<h3 className="text-lg font-semibold mb-4">4. Flutuante</h3>
				<p className="text-sm text-gray-600 mb-4">
					Posicionado como popup flutuante no canto da tela
				</p>
				<OrderbumpPositioningDemo
					position="floating"
					selectedBumps={selectedBumps}
					onBumpChange={setSelectedBumps}
				/>
			</div>

			{/* Resumo das seleções */}
			{selectedBumps.length > 0 && (
				<div className="bg-green-50 border border-green-200 rounded-lg p-4">
					<h4 className="font-semibold text-green-800 mb-2">
						Produtos Selecionados ({selectedBumps.length})
					</h4>
					<ul className="text-sm text-green-700 space-y-1">
						{selectedBumps.map((bumpId) => {
							const bump = mockOrderBumps.find(b => b.id === bumpId);
							return bump ? (
								<li key={bumpId}>• {bump.title} - R$ {bump.price.toFixed(2)}</li>
							) : null;
						})}
					</ul>
				</div>
			)}
		</div>
	);
}
