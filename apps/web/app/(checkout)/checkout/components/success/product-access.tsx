'use client';

import { But<PERSON> } from '@ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Alert, AlertDescription, AlertTitle } from '@ui/components/alert';
import {
	ArrowLeft,
	FileText,
	GraduationCap,
	Users,
	Info,
	AlertCircle,
	CheckCircle2,
} from 'lucide-react';
import Link from 'next/link';

interface ProductAccessProps {
	product: {
		id: string;
		title: string;
		type: 'COURSE' | 'MENTORING' | 'EBOOK';
		thumbnail?: string | null;
		course?: {
			id: string;
		} | null;
	};
	isPaid?: boolean;
}

export function ProductAccess({ product, isPaid = true }: ProductAccessProps) {
	const getProductIcon = () => {
		switch (product.type) {
			case 'COURSE':
				return <GraduationCap className='h-5 w-5' />;
			case 'MENTORING':
				return <Users className='h-5 w-5' />;
			default:
				return <FileText className='h-5 w-5' />;
		}
	};

	const getAccessInstructions = () => {
		const courseId = product.course?.id || product.id;

		switch (product.type) {
			case 'COURSE':
				return {
					title: 'Acesse seu curso',
					description: isPaid
						? 'Seu curso já está disponível para você em sua área de membros'
						: 'Seu curso estará disponível assim que confirmarmos seu pagamento',
					buttonText: 'Acessar meus cursos',
					link: `/account`,
					courseLink: `/account/course/${courseId}`,
				};
			case 'MENTORING':
				return {
					title: 'Agende sua mentoria',
					description: isPaid
						? 'Escolha o melhor horário para sua mentoria'
						: 'Você poderá agendar assim que confirmarmos seu pagamento',
					buttonText: 'Agendar mentoria',
					link: `/account/mentoring`,
				};
			default:
				return {
					title: 'Baixe seu e-book',
					description: isPaid
						? 'Seu e-book está pronto para download'
						: 'Seu e-book estará disponível assim que confirmarmos seu pagamento',
					buttonText: 'Acessar meus conteúdos',
					link: `/account`,
				};
		}
	};

	const instructions = getAccessInstructions();

	return (
		<Card className='mb-6'>
			<CardHeader className='pb-3'>
				<div className='flex items-center gap-2'>
					<div className='py-2'>{getProductIcon()}</div>
					<CardTitle>{instructions.title}</CardTitle>
				</div>
			</CardHeader>

			<CardContent>
				<div className='space-y-4'>
					{!isPaid && (
						<Alert variant='default' className='mb-4'>
							<AlertCircle className='h-4 w-4' />
							<AlertTitle>Aguardando confirmação de pagamento</AlertTitle>
							<AlertDescription>
								Estamos processando seu pagamento. Assim que confirmado, você
								receberá um email e poderá acessar seu conteúdo na área de
								membros.
							</AlertDescription>
						</Alert>
					)}

					{isPaid && (
						<Alert variant='success' className='mb-4'>
							<CheckCircle2 className='h-4 w-4' />
							<AlertTitle>Acesso liberado!</AlertTitle>
							<AlertDescription>
								Seu acesso já está liberado e você pode começar agora mesmo.
								Enviamos também todas as informações para seu email.
							</AlertDescription>
						</Alert>
					)}

					<div className='flex items-center gap-4'>
						{product.thumbnail ? (
							<img
								src={product.thumbnail}
								alt={product.title}
								className='h-16 w-16 rounded-lg object-cover'
							/>
						) : (
							<div className='flex h-16 w-16 items-center justify-center rounded-lg bg-muted'>
								{getProductIcon()}
							</div>
						)}
						<div>
							<h3 className='font-medium'>{product.title}</h3>
							<p className='text-sm text-muted-foreground'>
								{instructions.description}
							</p>
						</div>
					</div>

					<Button asChild className='w-full' disabled={!isPaid}>
						<Link href={instructions.link}>{instructions.buttonText}</Link>
					</Button>

					{isPaid && product.type === 'COURSE' && instructions.courseLink && (
						<Button asChild variant='outline' className='w-full mt-2'>
							<Link href={instructions.courseLink}>
								Ir direto para este curso
							</Link>
						</Button>
					)}

					<div className='bg-muted p-4 rounded-lg'>
						<div className='flex gap-2 mb-2'>
							<Info className='h-4 w-4 text-primary' />
							<h4 className='text-sm font-medium'>Como acessar:</h4>
						</div>
						<ol className='text-sm space-y-1 list-decimal list-inside text-muted-foreground'>
							<li>Faça login na plataforma com seu email e senha</li>
							<li>Acesse a área de membros no menu superior</li>
							<li>
								Encontre seu{' '}
								{product.type === 'COURSE'
									? 'curso'
									: product.type === 'MENTORING'
										? 'mentoria'
										: 'e-book'}{' '}
								na lista de produtos adquiridos
							</li>
							<li>
								Clique em "Acessar" para começar a aproveitar seu conteúdo
							</li>
						</ol>
					</div>

					<hr />

					<div className='flex justify-center text-sm'>
						<Link href='/' className='flex items-center gap-2'>
							<ArrowLeft className='h-4 w-4' />
							Voltar para a página inicial
						</Link>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
