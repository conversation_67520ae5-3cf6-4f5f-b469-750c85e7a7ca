'use client';

import { Shield, <PERSON>, BookOpen, UserCheck, FileText } from 'lucide-react';

// Componente para gerar imagens mock baseadas no tipo de produto
export function MockProductImage({
	type,
	className = "w-12 h-12"
}: {
	type: string;
	className?: string;
}) {
	const getIconForType = (productType: string) => {
		switch (productType.toLowerCase()) {
			case 'lifetime':
			case 'vitalício':
			case 'permanente':
				return <Shield className="w-6 h-6 text-orange-500" />;
			case 'social':
			case 'seguidores':
			case 'crescimento':
				return <Users className="w-6 h-6 text-orange-500" />;
			case 'curso':
			case 'marketing':
			case 'digital':
				return <BookOpen className="w-6 h-6 text-orange-500" />;
			case 'consultoria':
			case 'personalizada':
			case '1:1':
				return <UserCheck className="w-6 h-6 text-orange-500" />;
			case 'templates':
			case 'kit':
			case 'profissionais':
				return <FileText className="w-6 h-6 text-orange-500" />;
			default:
				return <Shield className="w-6 h-6 text-orange-500" />;
		}
	};

	return (
		<div className={`${className} rounded-lg bg-gradient-to-br from-orange-100 to-red-100 border border-orange-200 flex items-center justify-center`}>
			{getIconForType(type)}
		</div>
	);
}

// Função para obter o tipo de produto baseado no título
export function getProductTypeFromTitle(title: string): string {
	const titleLower = title.toLowerCase();

	if (titleLower.includes('vitalício') || titleLower.includes('permanente') || titleLower.includes('atualizações')) {
		return 'lifetime';
	}
	if (titleLower.includes('seguidores') || titleLower.includes('social') || titleLower.includes('crescimento')) {
		return 'social';
	}
	if (titleLower.includes('curso') || titleLower.includes('marketing') || titleLower.includes('digital')) {
		return 'curso';
	}
	if (titleLower.includes('consultoria') || titleLower.includes('personalizada') || titleLower.includes('1:1')) {
		return 'consultoria';
	}
	if (titleLower.includes('templates') || titleLower.includes('kit') || titleLower.includes('profissionais')) {
		return 'templates';
	}

	return 'default';
}
