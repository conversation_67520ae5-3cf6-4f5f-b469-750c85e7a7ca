'use client';

import { EnhancedOrderBumpCard } from './enhanced-order-bump-card';
import { Offer } from './types';

interface EnhancedOrderBumpListProps {
	offers: Offer[];
	selected: string[];
	onSelect: (selected: string[]) => void;
	title?: string;
	subtitle?: string;
}

export function EnhancedOrderBumpList({
	offers,
	selected,
	onSelect,
	title = 'Ofertas especiais:',
	subtitle = 'Produtos selecionados especialmente para você',
}: EnhancedOrderBumpListProps) {
	if (!offers?.length) return null;

	const orderBumps = offers.filter((offer) => offer.type === 'ORDER_BUMP');
	if (!orderBumps.length) return null;

	const toggleOffer = (offerId: string) => {
		if (selected.includes(offerId)) {
			onSelect(selected.filter((id) => id !== offerId));
		} else {
			onSelect([...selected, offerId]);
		}
	};

	// Determinar variante baseada no tipo de oferta ou posição
	const getVariant = (index: number, offer: Offer): 'default' | 'premium' | 'social' => {
		// Se for a primeira oferta e tiver palavras-chave específicas, usar variante premium
		if (index === 0 && (
			offer.title?.toLowerCase().includes('vitalício') ||
			offer.title?.toLowerCase().includes('permanente') ||
			offer.title?.toLowerCase().includes('atualizações')
		)) {
			return 'premium';
		}

		// Se for a segunda oferta e tiver palavras-chave de social media, usar variante social
		if (index === 1 && (
			offer.title?.toLowerCase().includes('seguidores') ||
			offer.title?.toLowerCase().includes('social') ||
			offer.title?.toLowerCase().includes('instagram')
		)) {
			return 'social';
		}

		return 'default';
	};

	return (
		<div className="space-y-6">
			{/* Header da seção */}
			<div className="text-left">
				<h3 className="text-lg font-semibold text-gray-900">
					{title}
				</h3>
			</div>

			{/* Lista de order bumps */}
			<div className="space-y-4">
				{orderBumps.map((offer, index) => (
					<EnhancedOrderBumpCard
						key={offer.id}
						offer={offer}
						checked={selected.includes(offer.id)}
						onToggle={() => toggleOffer(offer.id)}
						variant={getVariant(index, offer)}
					/>
				))}
			</div>

		</div>
	);
}
