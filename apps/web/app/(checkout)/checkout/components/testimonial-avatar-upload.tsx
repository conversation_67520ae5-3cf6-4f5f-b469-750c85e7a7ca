'use client';

import { useState } from 'react';
import { Upload, X, User } from 'lucide-react';
import { Button } from '@ui/components/button';
import { useFileUpload } from '@saas/products/hooks/useFileUpload';

interface TestimonialAvatarUploadProps {
  currentAvatar?: string;
  onAvatarChange: (url: string | null) => void;
  showLabel?: boolean;
}

export function TestimonialAvatarUpload({ 
  currentAvatar, 
  onAvatarChange, 
  showLabel = true 
}: TestimonialAvatarUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentAvatar || null);

  const { uploadFile, isUploading } = useFileUpload({
    bucket: 'testimonialAvatars',
    onSuccess: (url) => {
      setPreviewUrl(url);
      onAvatarChange(url);
    },
    onError: (error) => {
      console.error('Upload error:', error);
    },
  });

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Por favor, selecione apenas arquivos de imagem.');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('O arquivo deve ter no máximo 5MB.');
      return;
    }

    await uploadFile(file);
  };

  const handleRemoveAvatar = () => {
    setPreviewUrl(null);
    onAvatarChange(null);
  };

  // Convert R2 URL to CDN URL if needed
  const displayUrl = previewUrl && previewUrl.includes('r2.cloudflarestorage.com')
    ? previewUrl.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : previewUrl;

  return (
    <div className="space-y-4">
      {showLabel && (
        <label className="block text-sm font-medium text-gray-700">
          Avatar do Depoimento
        </label>
      )}
      
      <div className="flex items-center gap-4">
        {/* Avatar Preview */}
        <div className="relative">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200">
            {displayUrl ? (
              <img
                src={displayUrl}
                alt="Avatar preview"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <User className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>
          
          {/* Remove Button */}
          {displayUrl && (
            <button
              onClick={handleRemoveAvatar}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
              disabled={isUploading}
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </div>

        {/* Upload Controls */}
        <div className="flex-1">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
            id="avatar-upload"
            disabled={isUploading}
          />
          <label htmlFor="avatar-upload">
            <Button
              type="button"
              variant="outline"
              size="sm"
              disabled={isUploading}
              className="cursor-pointer"
              asChild
            >
              <span>
                <Upload className="h-4 w-4 mr-2" />
                {isUploading ? 'Enviando...' : 'Escolher Avatar'}
              </span>
            </Button>
          </label>
          
          <p className="text-xs text-gray-500 mt-1">
            PNG, JPG ou WebP. Máximo 5MB.
          </p>
        </div>
      </div>
    </div>
  );
}
