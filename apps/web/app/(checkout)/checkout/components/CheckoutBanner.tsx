"use client";

import { useState } from "react";

interface CheckoutBannerProps {
  settings: {
    enabled: boolean;
    url: string | null;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };
}

export function CheckoutBanner({ settings }: CheckoutBannerProps) {
  const [imageError, setImageError] = useState(false);

  // Debug: Log settings to understand why banner is not showing
  console.log('CheckoutBanner settings:', settings);

  // Don't show anything if disabled or no banner URL
  if (!settings.enabled || !settings.url) {
    // Debug: Show fallback banner for testing
    if (!settings.enabled) {
      console.log('Banner disabled');
    }
    if (!settings.url) {
      console.log('No banner URL provided');
    }
    
    // Temporary fallback for testing
    return (
      <div className="w-full mb-6 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
        <p className="text-gray-500 text-sm">
          Banner não configurado (enabled: {settings.enabled ? 'true' : 'false'}, url: {settings.url ? 'present' : 'null'})
        </p>
      </div>
    );
  }

  // Convert R2 URL to CDN URL if needed
  const displayUrl = settings.url.includes('r2.cloudflarestorage.com')
    ? settings.url.replace(
        'https://5c2abf4808262928f9012763daf2e491.r2.cloudflarestorage.com/supgateway/',
        'https://cdn.nextrusti.com/'
      )
    : settings.url;

  // Don't show anything if image fails to load
  if (imageError) {
    return null;
  }

  return (
    <div className="w-full mb-6">
      <img
        src={displayUrl}
        alt="Checkout banner"
        className={`w-full ${settings.borderRadius} ${settings.shadow ? 'shadow-lg' : ''}`}
        onError={() => setImageError(true)}
        style={{
          width: '100%',
          height: 'auto',
          objectFit: 'contain'
        }}
      />
    </div>
  );
}
