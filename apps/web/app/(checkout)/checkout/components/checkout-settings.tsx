'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Switch } from '@ui/components/switch';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Ta<PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from '@ui/components/tabs';
import { Badge } from '@ui/components/badge';
import { Eye, Save, Settings } from 'lucide-react';

interface CheckoutSettingsProps {
  initialSettings?: CheckoutSettingsData;
  onSave?: (settings: CheckoutSettingsData) => void;
  onPreview?: () => void;
}

interface CheckoutSettingsData {
  // Banner
  banner: {
    enabled: boolean;
    url?: string;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };

  // Header
  header: {
    showLogo: boolean;
    logoUrl?: string;
    companyName: string;
  };

  // Urgência
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: string;
    duration?: number;
    unit?: 'minutes' | 'hours' | 'days';
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };

  // Confiança
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Escassez
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Depoimentos
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Sidebar
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}

const defaultSettings: CheckoutSettingsData = {
  banner: {
    enabled: true,
    maxHeight: '300px',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
  header: {
    showLogo: false,
    companyName: 'SupGateway',
  },
  urgency: {
    enabled: false,
    message: 'Oferta por tempo limitado!',
    duration: 15,
    unit: 'minutes',
    backgroundColor: 'bg-red-50',
    textColor: 'text-red-700',
    accentColor: 'bg-red-600',
  },
  trustBadges: {
    enabled: true,
    badges: [
      {
        id: 'security',
        title: '100% Seguro',
        subtitle: 'Pagamentos protegidos',
        icon: 'shield',
        enabled: true,
      },
      {
        id: 'guarantee',
        title: 'Garantia de 30 dias',
        subtitle: 'Devolução garantida',
        icon: 'check',
        enabled: true,
      },
      {
        id: 'support',
        title: 'Suporte 24/7',
        subtitle: 'Atendimento completo',
        icon: 'mail',
        enabled: true,
      },
    ],
    layout: 'vertical',
    showDescriptions: true,
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
  },
  scarcity: {
    enabled: false,
    totalStock: 100,
    soldCount: 0,
    message: 'Apenas {remaining} vagas restantes!',
    variant: 'warning',
    showIcon: true,
    backgroundColor: 'bg-orange-50',
    textColor: 'text-orange-800',
    borderColor: 'border-orange-200',
  },
  testimonials: {
    enabled: true,
    testimonials: [
      {
        id: '1',
        name: 'Carlos Silva',
        rating: 5,
        comment: 'A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX.',
        location: 'São Paulo, SP',
        verified: true,
      },
      {
        id: '2',
        name: 'Ana Rodrigues',
        rating: 5,
        comment: 'Desde que implementamos a solução, nossa taxa de abandono de carrinho diminuiu drasticamente.',
        location: 'Rio de Janeiro, RJ',
        verified: true,
      },
    ],
    maxTestimonials: 3,
    autoPlay: true,
    autoPlayInterval: 5000,
    showControls: true,
    showStars: true,
    showAvatars: true,
    backgroundColor: 'bg-gray-50',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
  },
  sidebar: {
    enabled: false,
    title: 'Informações Importantes',
    content: 'Adicione informações úteis para seus clientes aqui.',
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
};

export function CheckoutSettings({
  initialSettings = defaultSettings,
  onSave,
  onPreview
}: CheckoutSettingsProps) {
  const [settings, setSettings] = useState<CheckoutSettingsData>(initialSettings);
  const [activeTab, setActiveTab] = useState('banner');

  const updateSetting = (section: keyof CheckoutSettingsData, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
  };

  const handleSave = () => {
    onSave?.(settings);
  };

  const handlePreview = () => {
    onPreview?.();
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Configurações do Checkout</h1>
          <p className="text-gray-600">Personalize a aparência e elementos de conversão do seu checkout</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePreview}>
            <Eye className="h-4 w-4 mr-2" />
            Visualizar
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Salvar
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="banner">Banner</TabsTrigger>
          <TabsTrigger value="header">Header</TabsTrigger>
          <TabsTrigger value="urgency">Urgência</TabsTrigger>
          <TabsTrigger value="trust">Confiança</TabsTrigger>
          <TabsTrigger value="scarcity">Escassez</TabsTrigger>
          <TabsTrigger value="testimonials">Depoimentos</TabsTrigger>
          <TabsTrigger value="sidebar">Sidebar</TabsTrigger>
        </TabsList>

        {/* Banner Settings */}
        <TabsContent value="banner">
          <Card>
            <CardHeader>
              <CardTitle>Configurações do Banner</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="banner-enabled">Exibir Banner</Label>
                <Switch
                  id="banner-enabled"
                  checked={settings.banner.enabled}
                  onCheckedChange={(checked) => updateSetting('banner', 'enabled', checked)}
                />
              </div>

              {settings.banner.enabled && (
                <>
                  <div>
                    <Label htmlFor="banner-url">URL da Imagem</Label>
                    <Input
                      id="banner-url"
                      value={settings.banner.url || ''}
                      onChange={(e) => updateSetting('banner', 'url', e.target.value)}
                      placeholder="https://exemplo.com/banner.jpg"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="banner-height">Altura Máxima</Label>
                      <Input
                        id="banner-height"
                        value={settings.banner.maxHeight}
                        onChange={(e) => updateSetting('banner', 'maxHeight', e.target.value)}
                        placeholder="300px"
                      />
                    </div>
                    <div>
                      <Label htmlFor="banner-radius">Bordas</Label>
                      <Input
                        id="banner-radius"
                        value={settings.banner.borderRadius}
                        onChange={(e) => updateSetting('banner', 'borderRadius', e.target.value)}
                        placeholder="rounded-lg"
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="banner-shadow">Sombra</Label>
                    <Switch
                      id="banner-shadow"
                      checked={settings.banner.shadow}
                      onCheckedChange={(checked) => updateSetting('banner', 'shadow', checked)}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Header Settings */}
        <TabsContent value="header">
          <Card>
            <CardHeader>
              <CardTitle>Configurações do Header</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="header-logo">Exibir Logo</Label>
                <Switch
                  id="header-logo"
                  checked={settings.header.showLogo}
                  onCheckedChange={(checked) => updateSetting('header', 'showLogo', checked)}
                />
              </div>

              {settings.header.showLogo && (
                <div>
                  <Label htmlFor="header-logo-url">URL do Logo</Label>
                  <Input
                    id="header-logo-url"
                    value={settings.header.logoUrl || ''}
                    onChange={(e) => updateSetting('header', 'logoUrl', e.target.value)}
                    placeholder="https://exemplo.com/logo.png"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="header-company">Nome da Empresa</Label>
                <Input
                  id="header-company"
                  value={settings.header.companyName}
                  onChange={(e) => updateSetting('header', 'companyName', e.target.value)}
                  placeholder="Sua Empresa"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Urgency Settings */}
        <TabsContent value="urgency">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Urgência</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="urgency-enabled">Exibir Elementos de Urgência</Label>
                <Switch
                  id="urgency-enabled"
                  checked={settings.urgency.enabled}
                  onCheckedChange={(checked) => updateSetting('urgency', 'enabled', checked)}
                />
              </div>

              {settings.urgency.enabled && (
                <>
                  <div>
                    <Label htmlFor="urgency-message">Mensagem</Label>
                    <Input
                      id="urgency-message"
                      value={settings.urgency.message}
                      onChange={(e) => updateSetting('urgency', 'message', e.target.value)}
                      placeholder="Esta oferta se encerra em:"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="urgency-duration">Duração da Oferta</Label>
                      <Input
                        id="urgency-duration"
                        type="number"
                        min="1"
                        value={settings.urgency.duration || 15}
                        onChange={(e) => updateSetting('urgency', 'duration', parseInt(e.target.value) || 15)}
                        placeholder="15"
                      />
                    </div>
                    <div>
                      <Label htmlFor="urgency-unit">Unidade de Tempo</Label>
                      <select
                        id="urgency-unit"
                        value={settings.urgency.unit || 'minutes'}
                        onChange={(e) => updateSetting('urgency', 'unit', e.target.value)}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="minutes">Minutos</option>
                        <option value="hours">Horas</option>
                        <option value="days">Dias</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="urgency-bg">Cor de Fundo</Label>
                      <Input
                        id="urgency-bg"
                        value={settings.urgency.backgroundColor}
                        onChange={(e) => updateSetting('urgency', 'backgroundColor', e.target.value)}
                        placeholder="bg-red-50"
                      />
                    </div>
                    <div>
                      <Label htmlFor="urgency-text">Cor do Texto</Label>
                      <Input
                        id="urgency-text"
                        value={settings.urgency.textColor}
                        onChange={(e) => updateSetting('urgency', 'textColor', e.target.value)}
                        placeholder="text-white"
                      />
                    </div>
                    <div>
                      <Label htmlFor="urgency-accent">Cor de Destaque</Label>
                      <Input
                        id="urgency-accent"
                        value={settings.urgency.accentColor}
                        onChange={(e) => updateSetting('urgency', 'accentColor', e.target.value)}
                        placeholder="bg-red-600"
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trust Badges Settings */}
        <TabsContent value="trust">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Badges de Confiança</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="trust-enabled">Exibir Selos de Segurança</Label>
                <Switch
                  id="trust-enabled"
                  checked={settings.trustBadges.enabled}
                  onCheckedChange={(checked) => updateSetting('trustBadges', 'enabled', checked)}
                />
              </div>

              {settings.trustBadges.enabled && (
                <>
                  <div className="space-y-3">
                    <Label>Badges Disponíveis</Label>
                    {settings.trustBadges.badges.map((badge, index) => (
                      <div key={badge.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Switch
                            checked={badge.enabled}
                            onCheckedChange={(checked) => {
                              const newBadges = [...settings.trustBadges.badges];
                              newBadges[index].enabled = checked;
                              updateSetting('trustBadges', 'badges', newBadges);
                            }}
                          />
                          <div>
                            <div className="font-medium">{badge.title}</div>
                            <div className="text-sm text-gray-500">{badge.subtitle}</div>
                          </div>
                        </div>
                        <Badge>{badge.icon}</Badge>
                      </div>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="trust-layout">Layout</Label>
                      <select
                        id="trust-layout"
                        value={settings.trustBadges.layout}
                        onChange={(e) => updateSetting('trustBadges', 'layout', e.target.value)}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="horizontal">Horizontal</option>
                        <option value="vertical">Vertical</option>
                        <option value="grid">Grid</option>
                      </select>
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="trust-descriptions">Mostrar Descrições</Label>
                      <Switch
                        id="trust-descriptions"
                        checked={settings.trustBadges.showDescriptions}
                        onCheckedChange={(checked) => updateSetting('trustBadges', 'showDescriptions', checked)}
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scarcity Settings */}
        <TabsContent value="scarcity">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Escassez</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="scarcity-enabled">Exibir Elementos de Escassez</Label>
                <Switch
                  id="scarcity-enabled"
                  checked={settings.scarcity.enabled}
                  onCheckedChange={(checked) => updateSetting('scarcity', 'enabled', checked)}
                />
              </div>

              {settings.scarcity.enabled && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="scarcity-total">Estoque Total</Label>
                      <Input
                        id="scarcity-total"
                        type="number"
                        value={settings.scarcity.totalStock}
                        onChange={(e) => updateSetting('scarcity', 'totalStock', parseInt(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="scarcity-sold">Vendidas</Label>
                      <Input
                        id="scarcity-sold"
                        type="number"
                        value={settings.scarcity.soldCount}
                        onChange={(e) => updateSetting('scarcity', 'soldCount', parseInt(e.target.value))}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="scarcity-message">Mensagem</Label>
                    <Input
                      id="scarcity-message"
                      value={settings.scarcity.message}
                      onChange={(e) => updateSetting('scarcity', 'message', e.target.value)}
                      placeholder="Apenas {remaining} vagas restantes!"
                    />
                  </div>

                  <div>
                    <Label htmlFor="scarcity-variant">Variante</Label>
                    <select
                      id="scarcity-variant"
                      value={settings.scarcity.variant}
                      onChange={(e) => updateSetting('scarcity', 'variant', e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="warning">Aviso</option>
                      <option value="danger">Perigo</option>
                      <option value="info">Informação</option>
                    </select>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Testimonials Settings */}
        <TabsContent value="testimonials">
          <Card>
            <CardHeader>
              <CardTitle>Configurações de Depoimentos</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="testimonials-enabled">Exibir Depoimentos de Clientes</Label>
                <Switch
                  id="testimonials-enabled"
                  checked={settings.testimonials.enabled}
                  onCheckedChange={(checked) => updateSetting('testimonials', 'enabled', checked)}
                />
              </div>

              {settings.testimonials.enabled && (
                <>
                  <div className="space-y-3">
                    <Label>Depoimentos</Label>
                    {settings.testimonials.testimonials.map((testimonial, index) => (
                      <div key={testimonial.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium">{testimonial.name}</div>
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <span key={i} className={i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}>
                                ★
                              </span>
                            ))}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">{testimonial.comment}</p>
                        {testimonial.location && (
                          <p className="text-xs text-gray-500 mt-1">{testimonial.location}</p>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="testimonials-max">Máximo de Depoimentos</Label>
                      <Input
                        id="testimonials-max"
                        type="number"
                        value={settings.testimonials.maxTestimonials}
                        onChange={(e) => updateSetting('testimonials', 'maxTestimonials', parseInt(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="testimonials-interval">Intervalo (ms)</Label>
                      <Input
                        id="testimonials-interval"
                        type="number"
                        value={settings.testimonials.autoPlayInterval}
                        onChange={(e) => updateSetting('testimonials', 'autoPlayInterval', parseInt(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="testimonials-autoplay">Reprodução Automática</Label>
                      <Switch
                        id="testimonials-autoplay"
                        checked={settings.testimonials.autoPlay}
                        onCheckedChange={(checked) => updateSetting('testimonials', 'autoPlay', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="testimonials-stars">Mostrar Estrelas</Label>
                      <Switch
                        id="testimonials-stars"
                        checked={settings.testimonials.showStars}
                        onCheckedChange={(checked) => updateSetting('testimonials', 'showStars', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="testimonials-avatars">Mostrar Avatares</Label>
                      <Switch
                        id="testimonials-avatars"
                        checked={settings.testimonials.showAvatars}
                        onCheckedChange={(checked) => updateSetting('testimonials', 'showAvatars', checked)}
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sidebar Settings */}
        <TabsContent value="sidebar">
          <Card>
            <CardHeader>
              <CardTitle>Configurações da Sidebar</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="sidebar-enabled">Exibir Sidebar</Label>
                <Switch
                  id="sidebar-enabled"
                  checked={settings.sidebar.enabled}
                  onCheckedChange={(checked) => updateSetting('sidebar', 'enabled', checked)}
                />
              </div>

              {settings.sidebar.enabled && (
                <>
                  <div>
                    <Label htmlFor="sidebar-banner">URL do Banner</Label>
                    <Input
                      id="sidebar-banner"
                      value={settings.sidebar.bannerUrl || ''}
                      onChange={(e) => updateSetting('sidebar', 'bannerUrl', e.target.value)}
                      placeholder="https://exemplo.com/sidebar-banner.jpg"
                    />
                  </div>

                  <div>
                    <Label htmlFor="sidebar-title">Título</Label>
                    <Input
                      id="sidebar-title"
                      value={settings.sidebar.title}
                      onChange={(e) => updateSetting('sidebar', 'title', e.target.value)}
                      placeholder="Informações Importantes"
                    />
                  </div>

                  <div>
                    <Label htmlFor="sidebar-content">Conteúdo</Label>
                    <Textarea
                      id="sidebar-content"
                      value={settings.sidebar.content}
                      onChange={(e) => updateSetting('sidebar', 'content', e.target.value)}
                      placeholder="Adicione informações úteis para seus clientes aqui."
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="sidebar-bg">Cor de Fundo</Label>
                      <Input
                        id="sidebar-bg"
                        value={settings.sidebar.backgroundColor}
                        onChange={(e) => updateSetting('sidebar', 'backgroundColor', e.target.value)}
                        placeholder="bg-blue-50"
                      />
                    </div>
                    <div>
                      <Label htmlFor="sidebar-text">Cor do Texto</Label>
                      <Input
                        id="sidebar-text"
                        value={settings.sidebar.textColor}
                        onChange={(e) => updateSetting('sidebar', 'textColor', e.target.value)}
                        placeholder="text-blue-800"
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
