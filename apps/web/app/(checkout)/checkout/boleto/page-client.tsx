'use client';

import { <PERSON><PERSON>, Loader2, Receipt } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	<PERSON>,
	CardContent,
	<PERSON><PERSON>ooter,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { toast } from 'sonner';

interface SafeOrder {
	id: string;
	amount: number;
	status: string;
	createdAt: Date;
	updatedAt: Date;
	gatewayId: string | null;
	product: {
		title: string;
		description: string | null;
		thumbnail: string | null;
	};
	user: {
		name: string | null;
		email: string;
	};
}

interface BoletoPageProps {
	data: {
		order: SafeOrder;
		bankSlipUrl: string;
		identificationField: string;
		barCode: string;
	};
}

export function BoletoPage({ data }: BoletoPageProps) {
	const [copied, setCopied] = useState(false);
	const [isChecking, setIsChecking] = useState(false);
	const router = useRouter();

	const { order, bankSlipUrl, identificationField, barCode } = data;

	// Verificar periodicamente o status do pagamento
	useEffect(() => {
		let interval: any;
		if (order?.id) {
			interval = setInterval(async () => {
				try {
					setIsChecking(true);
					const response = await fetch(
						`/api/payments/status?orderId=${order.id}`
					);
					const data = await response.json();

					console.log('data', data);

					if (data.status === 'PAID') {
						clearInterval(interval);
						toast({
							title: 'Pagamento confirmado!',
							description:
								'Você receberá um email com as instruções de acesso ao produto',
							variant: 'success',
						});
						router.push(`/checkout/success?orderId=${order.id}`);
					}
				} catch (error) {
					console.error('Error checking payment:', error);
				} finally {
					setIsChecking(false);
				}
			}, 30000); // Verificar a cada 30 segundos (boletos levam mais tempo)
		}
		return () => interval && clearInterval(interval);
	}, [order?.id]);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(identificationField);
			setCopied(true);

			toast({
				title: 'Código de barras copiado!',
				description:
					'Você pode usar este código para pagar no app do seu banco',
				variant: 'success',
			});
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			toast({
				title: 'Erro ao copiar código',
				description: 'Tente copiar o código manualmente',
				variant: 'error',
			});
		}
	};

	const handleOpenBoleto = () => {
		window.open(bankSlipUrl, '_blank');
	};

	return (
		<Card>
			<CardHeader className='text-center'>
				<div className='mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-primary/10'>
					<Receipt className='size-6 text-primary' />
				</div>
				<CardTitle className='text-2xl'>Pagamento via Boleto</CardTitle>
				<p className='mt-2 text-muted-foreground'>
					Realize o pagamento para confirmar sua compra
				</p>
			</CardHeader>

			<CardContent className='space-y-6'>
				{/* Status */}
				<div className='flex items-center justify-center'>
					<Badge className='flex items-center gap-2 bg-slate-950 px-4 text-white'>
						{isChecking ? (
							<Loader2 className='size-3 animate-spin' />
						) : (
							<div className='size-3 animate-pulse rounded-full bg-amber-500'></div>
						)}
						Aguardando pagamento
					</Badge>
				</div>

				{/* Boleto e Instruções */}
				<div className='space-y-6'>
					<div className='rounded-lg border bg-white p-6 flex flex-col items-center space-y-4'>
						<h4 className='font-medium'>Boleto Bancário</h4>
						<Button
							onClick={handleOpenBoleto}
							className='w-full justify-center'
						>
							Abrir Boleto
						</Button>
						<span className='text-sm text-muted-foreground text-center'>
							Clique no botão acima para abrir o boleto em uma nova janela
						</span>
					</div>

					<div className='rounded-lg border p-4'>
						<h4 className='mb-2 font-medium'>Código de Barras</h4>
						<p className='mb-4 text-sm text-muted-foreground'>
							Copie o código e utilize no app do seu banco ou casa lotérica
						</p>

						<div className='p-3 bg-muted/20 rounded-md mb-4 text-sm font-mono overflow-x-auto'>
							{identificationField}
						</div>

						<Button
							variant='outline'
							onClick={handleCopy}
							className='w-full justify-center gap-2'
						>
							<Copy className='size-4' />
							{copied ? 'Copiado!' : 'Copiar código de barras'}
						</Button>
					</div>

					<div className='rounded-lg bg-muted p-4 text-sm'>
						<h4 className='font-medium mb-2'>Instruções:</h4>
						<ol className='list-decimal space-y-2 pl-4 text-muted-foreground'>
							<li>Abra seu app bancário ou internet banking</li>
							<li>Escolha a opção "Pagar boleto" ou "Pagar contas"</li>
							<li>
								Use a câmera para ler o código de barras ou cole o código
								copiado
							</li>
							<li>Confirme os dados e finalize o pagamento</li>
						</ol>
					</div>
				</div>

				{/* Detalhes */}
				<div className='rounded-lg border p-4'>
					<h3 className='mb-4 font-medium'>Detalhes do Pedido</h3>
					<div className='space-y-2 text-sm'>
						<div className='flex justify-between'>
							<span className='text-muted-foreground'>Produto</span>
							<span className='font-medium'>{order?.product?.title}</span>
						</div>
						<div className='flex justify-between'>
							<span className='text-muted-foreground'>Vencimento</span>
							<span className='font-medium'>5 dias úteis</span>
						</div>
						<Separator className='my-2' />
						<div className='flex justify-between text-lg font-medium'>
							<span>Total</span>
							<span>R$ {Number(order?.amount).toFixed(2)}</span>
						</div>
					</div>
				</div>
			</CardContent>

			<CardFooter className='flex-col space-y-4 text-center'>
				<div className='rounded-lg bg-amber-50 p-4 text-sm text-amber-700'>
					<p>
						<strong>Atenção:</strong> O pagamento via boleto pode levar até 3
						dias úteis para ser processado.
					</p>
					<p className='mt-2'>
						Após a confirmação do pagamento, você receberá um email com as
						instruções de acesso ao produto
					</p>
				</div>
				<Button
					variant='link'
					className='text-muted-foreground'
					onClick={() => router.push('/')}
				>
					Voltar para a página inicial
				</Button>
			</CardFooter>
		</Card>
	);
}
