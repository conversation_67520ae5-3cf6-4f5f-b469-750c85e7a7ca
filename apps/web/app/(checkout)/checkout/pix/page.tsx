import "server-only";
import { AsaasClient } from '@repo/payments';
import { db } from '@repo/database';
import { redirect } from 'next/navigation';
import { CheckoutHeader } from '../components/checkout-header';
import { CheckoutBanner } from '../components/CheckoutBanner';
import { PixPage } from './page-client';

interface PixPageProps {
	searchParams: Promise<{
		orderId: string;
	}>;
}

export default async function CheckoutPixPage({ searchParams }: PixPageProps) {
	// Aguardar os parâmetros antes de acessá-los
	const resolvedSearchParams = await searchParams;
	console.log('searchParams', resolvedSearchParams);

	if (!resolvedSearchParams.orderId) {
		redirect('/');
	}

	try {
		// Buscar dados do pedido
		const order = await db.order.findUnique({
			where: { id: resolvedSearchParams.orderId },
			include: {
				product: true,
				user: true,
			},
		});

		console.log('order', order);

		if (!order) {
			redirect('/');
		}

		// Buscar QR Code do PIX
		const asaas = new AsaasClient();
		const pixData = await asaas.getPixQRCode(order.gatewayId || '');

		return (
			<div className='min-h-screen bg-gray-50/50'>
				<CheckoutHeader />

				<div className='container max-w-5xl py-8 lg:py-12'>
					{/* Display banner if available */}
					<CheckoutBanner
						bannerUrl={
							order.product.settings && typeof order.product.settings === 'object'
								? (order.product.settings as Record<string, unknown>).banner as string | null
								: null
						}
					/>

					<PixPage
						data={{
							order,
							pixPayload: pixData.payload,
							qrCode: pixData.encodedImage,
						}}
					/>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading PIX page:', error);
		redirect('/');
	}
}
