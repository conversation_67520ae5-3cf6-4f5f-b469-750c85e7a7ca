'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>2, QrC<PERSON> } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	<PERSON>,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { useMediaQuery } from '@ui/hooks/use-media-query';
import { toast } from 'sonner';
import { PixQRCode } from '../components/pix-qrcode';

interface SafeOrder {
	id: string;
	amount: number;
	status: string;
	createdAt: Date;
	updatedAt: Date;
	gatewayId: string | null;
	product: {
		title: string;
		description: string | null;
		thumbnail: string | null;
	};
	user: {
		name: string | null;
		email: string;
	};
}

interface PixPageProps {
	data: {
		order: SafeOrder;
		pixPayload: string;
		qrCode: string;
	};
}

export function PixPage({ data }: PixPageProps) {
	const [copied, setCopied] = useState(false);
	const [isChecking, setIsChecking] = useState(false);
	const router = useRouter();
	const { isMobile } = useMediaQuery();

	const { order, pixPayload, qrCode } = data;

	useEffect(() => {
		let interval: any;
		if (order?.id) {
			interval = setInterval(async () => {
				try {
					setIsChecking(true);
					const response = await fetch(
						`/api/payments/status?orderId=${order.id}`
					);
					const data = await response.json();

					console.log('data', data);

					if (data.status === 'PAID') {
						clearInterval(interval);
						toast({
							title: 'Pagamento confirmado!',
							description:
								'Você receberá um email com as instruções de acesso ao produto',
							variant: 'success',
						});
						router.push(`/checkout/success?orderId=${order.id}`);
					}
				} catch (error) {
					console.error('Error checking payment:', error);
				} finally {
					setIsChecking(false);
				}
			}, 15000);
		}
		return () => interval && clearInterval(interval);
	}, [order?.id]);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(pixPayload);
			setCopied(true);
			// toast.success('Código PIX copiado!');

			toast({
				title: 'Código PIX copiado!',
				description:
					'Você pode usar este código para pagar no app do seu banco',
				variant: 'success',
			});
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			// toast.error('Erro ao copiar código');
			toast({
				title: 'Erro ao copiar código',
				description: 'Tente copiar o código manualmente',
				variant: 'error',
			});
		}
	};

	return (
		<Card>
			<CardHeader className='text-center'>
				<div className='mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-primary/10'>
					<QrCode className='size-6 text-primary' />
				</div>
				<CardTitle className='text-2xl'>Pagamento via PIX</CardTitle>
				<p className='mt-2 text-muted-foreground'>
					Realize o pagamento para confirmar sua compra
				</p>
			</CardHeader>

			<CardContent className='space-y-6'>
				{/* Status */}
				<div className='flex items-center justify-center'>
					<Badge className='flex items-center gap-2 bg-slate-950 px-4 text-white'>
						{<Loader2 className='size-3 animate-spin' />}
						Aguardando pagamento
					</Badge>
				</div>

				{/* QR Code e Instruções */}
				<div className='grid gap-6 md:grid-cols-2'>
					{!isMobile && qrCode && (
						<div className='flex flex-col items-center space-y-4 rounded-xl border bg-white p-6'>
							<PixQRCode qrCodeBase64={qrCode} size={200} />
							<span className='text-sm text-muted-foreground'>
								Escaneie o QR Code
							</span>
						</div>
					)}

					<div className='space-y-4'>
						<div className='rounded-lg border p-4'>
							<h4 className='mb-2 font-medium'>Código PIX</h4>
							<p className='mb-4 text-sm text-muted-foreground'>
								Copie o código e pague no app do seu banco
							</p>

							<Button
								variant='outline'
								onClick={handleCopy}
								className='w-full justify-center gap-2'
							>
								<Copy className='size-4' />
								{copied ? 'Copiado!' : 'Copiar código'}
							</Button>
						</div>

						<div className='rounded-lg bg-muted p-4 text-sm'>
							<ol className='list-decimal space-y-2 pl-4 text-muted-foreground'>
								<li>Abra o app do seu banco</li>
								<li>Escolha pagar via PIX</li>
								{isMobile ? (
									<li>Cole o código copiado</li>
								) : (
									<li>Escaneie o QR code ou cole o código</li>
								)}
								<li>Confirme o pagamento</li>
							</ol>
						</div>
					</div>
				</div>

				{/* Detalhes */}
				<div className='rounded-lg border p-4'>
					<h3 className='mb-4 font-medium'>Detalhes do Pedido</h3>
					<div className='space-y-2 text-sm'>
						<div className='flex justify-between'>
							<span className='text-muted-foreground'>Produto</span>
							<span className='font-medium'>{order?.product?.title}</span>
						</div>
						<Separator className='my-2' />
						<div className='flex justify-between text-lg font-medium'>
							<span>Total</span>
							<span>R$ {Number(order?.amount).toFixed(2)}</span>
						</div>
					</div>
				</div>
			</CardContent>

			<CardFooter className='flex-col space-y-4 text-center'>
				<div className='rounded-lg bg-blue-50 p-4 text-sm text-blue-700'>
					Após a confirmação do pagamento, você receberá um email com as
					instruções de acesso ao produto
				</div>
				<Button
					variant='link'
					className='text-muted-foreground'
					onClick={() => router.push('/')}
				>
					Voltar para a página inicial
				</Button>
			</CardFooter>
		</Card>
	);
}
