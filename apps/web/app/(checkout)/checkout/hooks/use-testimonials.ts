'use client';

import { useState, useEffect } from 'react';

interface Testimonial {
  id: string;
  customerName: string;
  customerEmail?: string;
  customerPhoto?: string;
  customerRole?: string;
  customerLocation?: string;
  title?: string;
  content: string;
  rating: number;
  source: string;
  sourceUrl?: string;
  sourceDate?: string;
  isApproved: boolean;
  isActive: boolean;
  isFeatured: boolean;
  productId?: string;
  createdAt: string;
  updatedAt: string;
}

interface UseTestimonialsOptions {
  productId?: string;
  maxTestimonials?: number;
  onlyFeatured?: boolean;
  onlyApproved?: boolean;
}

interface UseTestimonialsReturn {
  testimonials: Testimonial[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useTestimonials({
  productId,
  maxTestimonials = 10,
  onlyFeatured = false,
  onlyApproved = true,
}: UseTestimonialsOptions = {}): UseTestimonialsReturn {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTestimonials = async () => {
    try {
      setLoading(true);
      setError(null);

      let url = '/api/testimonials/public';
      
      if (productId) {
        url = `/api/testimonials/public/${productId}`;
      }

      const params = new URLSearchParams();
      params.append('limit', maxTestimonials.toString());
      
      if (onlyFeatured) {
        params.append('featured', 'true');
      }
      
      if (onlyApproved) {
        params.append('approved', 'true');
      }

      const response = await fetch(`${url}?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch testimonials: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Handle both paginated and direct array responses
      const testimonialsData = data.testimonials || data;
      
      setTestimonials(Array.isArray(testimonialsData) ? testimonialsData : []);
    } catch (err) {
      console.error('Error fetching testimonials:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch testimonials');
      setTestimonials([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTestimonials();
  }, [productId, maxTestimonials, onlyFeatured, onlyApproved]);

  return {
    testimonials,
    loading,
    error,
    refetch: fetchTestimonials,
  };
}
