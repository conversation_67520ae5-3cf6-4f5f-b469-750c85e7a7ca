import { CommunityMarketplace } from "../../modules/communities";
import { db } from "@repo/database";
import { CommunityMarketplaceClient } from "./components/CommunityMarketplaceClient";

async function getCommunities() {
  const communities = await db.community.findMany({
    where: {
      isPublic: true,
      isActive: true,
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          logo: true,
        },
      },
      _count: {
        select: {
          members: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return communities.map((community) => ({
    ...community,
    memberCount: community._count.members,
  }));
}

export default async function CommunitiesPage() {
  const communities = await getCommunities();

  return (
    <div className="container mx-auto px-4 py-8">
      <CommunityMarketplaceClient
        communities={communities}
        loading={false}
      />
    </div>
  );
}
