import { CommunityDetail } from "../../../modules/communities";
import { db } from "@repo/database";
import { notFound } from "next/navigation";
import { CommunityDetailClient } from "./components/CommunityDetailClient";

interface CommunityPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getCommunity(id: string) {
  const community = await db.community.findUnique({
    where: { id },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          logo: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
        take: 10,
        orderBy: {
          joinedAt: "desc",
        },
      },
      categories: {
        where: {
          isActive: true,
        },
        orderBy: {
          order: "asc",
        },
      },
      _count: {
        select: {
          members: true,
        },
      },
    },
  });

  if (!community) {
    return null;
  }

  return {
    ...community,
    memberCount: community._count.members,
  };
}

export default async function CommunityPage({ params }: CommunityPageProps) {
  const { id } = await params;
  const community = await getCommunity(id);

  if (!community) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <CommunityDetailClient
        community={community}
        isMember={false} // TODO: Check if current user is a member
      />
    </div>
  );
}
