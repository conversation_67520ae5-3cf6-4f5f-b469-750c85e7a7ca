"use client";

import { CommunityDetail } from "@/modules/communities";
import { useRouter } from "next/navigation";

interface CommunityDetailClientProps {
  community: any;
  isMember?: boolean;
}

export function CommunityDetailClient({ community, isMember = false }: CommunityDetailClientProps) {
  const router = useRouter();

  const handleJoin = (communityId: string) => {
    // Redirect to checkout or join flow
    router.push(`/checkout/${communityId}`);
  };

  const handleLeave = (communityId: string) => {
    // Handle leave community
    console.log("Leave community:", communityId);
    // TODO: Implement leave community API call
  };

  return (
    <CommunityDetail
      community={community}
      isMember={isMember}
      onJoin={handleJoin}
      onLeave={handleLeave}
    />
  );
}
