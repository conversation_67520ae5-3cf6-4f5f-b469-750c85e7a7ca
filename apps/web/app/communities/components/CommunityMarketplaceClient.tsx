"use client";

import { CommunityMarketplace } from "@/modules/communities";
import { useRouter } from "next/navigation";

interface CommunityMarketplaceClientProps {
  communities: any[];
  loading?: boolean;
}

export function CommunityMarketplaceClient({ communities, loading = false }: CommunityMarketplaceClientProps) {
  const router = useRouter();

  const handleJoin = (communityId: string) => {
    // Redirect to checkout or join flow
    router.push(`/checkout/${communityId}`);
  };

  return (
    <CommunityMarketplace
      communities={communities}
      loading={loading}
      onJoin={handleJoin}
    />
  );
}
