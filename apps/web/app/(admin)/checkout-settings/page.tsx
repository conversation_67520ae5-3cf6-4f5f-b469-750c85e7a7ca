'use client';

import { useState, useEffect } from 'react';
import { CheckoutSettings } from '../../../app/(checkout)/checkout/components/checkout-settings';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Eye, Save, Settings, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface CheckoutSettingsData {
  // Banner
  banner: {
    enabled: boolean;
    url?: string;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };

  // Header
  header: {
    showLogo: boolean;
    logoUrl?: string;
    companyName: string;
  };

  // Urgência
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: string;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };

  // Confiança
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Escassez
  scarcity: {
    enabled: boolean;
    totalStock: number;
    soldCount: number;
    message: string;
    variant: 'warning' | 'danger' | 'info';
    showIcon: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Depoimentos
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Sidebar
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}

const defaultSettings: CheckoutSettingsData = {
  banner: {
    enabled: true,
    maxHeight: '300px',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
  header: {
    showLogo: false,
    companyName: 'SupGateway',
  },
  urgency: {
    enabled: false,
    message: 'Esta oferta se encerra em:',
    backgroundColor: 'bg-red-50',
    textColor: 'text-white',
    accentColor: 'bg-red-600',
  },
  trustBadges: {
    enabled: true,
    badges: [
      {
        id: 'security',
        title: '100% Seguro',
        subtitle: 'Pagamentos protegidos',
        icon: 'shield',
        enabled: true,
      },
      {
        id: 'guarantee',
        title: 'Garantia de 30 dias',
        subtitle: 'Devolução garantida',
        icon: 'check',
        enabled: true,
      },
      {
        id: 'support',
        title: 'Suporte 24/7',
        subtitle: 'Atendimento completo',
        icon: 'mail',
        enabled: true,
      },
    ],
    layout: 'vertical',
    showDescriptions: true,
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
  },
  scarcity: {
    enabled: false,
    totalStock: 100,
    soldCount: 0,
    message: 'Apenas {remaining} vagas restantes!',
    variant: 'warning',
    showIcon: true,
    backgroundColor: 'bg-orange-50',
    textColor: 'text-orange-800',
    borderColor: 'border-orange-200',
  },
  testimonials: {
    enabled: true,
    testimonials: [
      {
        id: '1',
        name: 'Carlos Silva',
        rating: 5,
        comment: 'A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX.',
        location: 'São Paulo, SP',
        verified: true,
      },
      {
        id: '2',
        name: 'Ana Rodrigues',
        rating: 5,
        comment: 'Desde que implementamos a solução, nossa taxa de abandono de carrinho diminuiu drasticamente.',
        location: 'Rio de Janeiro, RJ',
        verified: true,
      },
    ],
    maxTestimonials: 3,
    autoPlay: true,
    autoPlayInterval: 5000,
    showControls: true,
    showStars: true,
    showAvatars: true,
    backgroundColor: 'bg-gray-50',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
  },
  sidebar: {
    enabled: false,
    title: 'Informações Importantes',
    content: 'Adicione informações úteis para seus clientes aqui.',
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
};

export default function CheckoutSettingsPage() {
  const router = useRouter();
  const [settings, setSettings] = useState<CheckoutSettingsData>(defaultSettings);
  const [isSaving, setIsSaving] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // Load settings from localStorage or API
  useEffect(() => {
    const savedSettings = localStorage.getItem('checkout-settings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  const handleSave = async (newSettings: CheckoutSettingsData) => {
    setIsSaving(true);
    try {
      // Save to localStorage (in real app, save to API)
      localStorage.setItem('checkout-settings', JSON.stringify(newSettings));
      setSettings(newSettings);

      // Show success message
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  const getActiveElementsCount = () => {
    let count = 0;
    if (settings.banner.enabled) count++;
    if (settings.header.showLogo) count++;
    if (settings.urgency.enabled) count++;
    if (settings.trustBadges.enabled) count++;
    if (settings.scarcity.enabled) count++;
    if (settings.testimonials.enabled) count++;
    if (settings.sidebar.enabled) count++;
    return count;
  };

  if (isPreviewMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPreviewMode(false)}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
                <h1 className="text-lg font-semibold">Visualização do Checkout</h1>
              </div>
              <Badge variant="outline" className="bg-green-100 text-green-800">
                Modo Visualização
              </Badge>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto py-8">
          <div className="bg-white rounded-lg shadow-sm border p-8">
            <h2 className="text-2xl font-bold mb-4">Pré-visualização das Configurações</h2>
            <p className="text-gray-600 mb-6">
              Esta é uma pré-visualização de como as configurações aparecerão no checkout.
            </p>

            {/* Preview of active elements */}
            <div className="space-y-4">
              {settings.banner.enabled && (
                <div className="p-4 border rounded-lg bg-gray-50">
                  <h3 className="font-medium text-gray-900">Banner</h3>
                  <p className="text-sm text-gray-600">Banner habilitado</p>
                </div>
              )}

              {settings.urgency.enabled && (
                <div className="p-4 border rounded-lg bg-red-50">
                  <h3 className="font-medium text-red-900">Urgência</h3>
                  <p className="text-sm text-red-700">{settings.urgency.message}</p>
                </div>
              )}

              {settings.trustBadges.enabled && (
                <div className="p-4 border rounded-lg bg-blue-50">
                  <h3 className="font-medium text-blue-900">Badges de Confiança</h3>
                  <p className="text-sm text-blue-700">
                    {settings.trustBadges.badges.filter(b => b.enabled).length} badges ativos
                  </p>
                </div>
              )}

              {settings.scarcity.enabled && (
                <div className="p-4 border rounded-lg bg-orange-50">
                  <h3 className="font-medium text-orange-900">Escassez</h3>
                  <p className="text-sm text-orange-700">{settings.scarcity.message}</p>
                </div>
              )}

              {settings.testimonials.enabled && (
                <div className="p-4 border rounded-lg bg-gray-50">
                  <h3 className="font-medium text-gray-900">Depoimentos</h3>
                  <p className="text-sm text-gray-700">
                    {settings.testimonials.testimonials.length} depoimentos configurados
                  </p>
                </div>
              )}

              {settings.sidebar.enabled && (
                <div className="p-4 border rounded-lg bg-blue-50">
                  <h3 className="font-medium text-blue-900">Sidebar</h3>
                  <p className="text-sm text-blue-700">{settings.sidebar.title}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-lg font-semibold">Configurações do Checkout</h1>
                <p className="text-sm text-gray-600">
                  Personalize a aparência e elementos de conversão
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {getActiveElementsCount()} elementos ativos
              </Badge>
              <Button
                variant="outline"
                onClick={handlePreview}
                disabled={isSaving}
              >
                <Eye className="h-4 w-4 mr-2" />
                Visualizar
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-8">
        <CheckoutSettings
          initialSettings={settings}
          onSave={handleSave}
          onPreview={handlePreview}
        />
      </div>
    </div>
  );
}
