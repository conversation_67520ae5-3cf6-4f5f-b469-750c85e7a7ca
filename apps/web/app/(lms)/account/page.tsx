import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { PlayIcon, BookOpenIcon, DownloadIcon, AwardIcon, UserIcon, CalendarIcon, ClockIcon } from "lucide-react";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";

export default async function AccountPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	// Buscar produtos comprados pelo usuário
	const enrollments = await db.courseEnrollment.findMany({
		where: {
			userId: session.user.id,
		},
		include: {
			product: {
				include: {
					modules: {
						include: {
							lessons: true,
						},
						orderBy: {
							order: 'asc',
						},
					},
				},
			},
			lessonProgress: true,
		},
	});

	// Buscar certificados do usuário
	const certificates = await db.certificate.findMany({
		where: {
			userId: session.user.id,
		},
		include: {
			product: true,
		},
	});

	const formatCurrency = (cents: number, currency: string = 'BRL') => {
		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency,
		}).format(cents / 100);
	};

	const calculateProgress = (enrollment: any) => {
		const totalLessons = enrollment.product.modules.reduce(
			(acc: number, module: any) => acc + module.lessons.length,
			0
		);
		const completedLessons = enrollment.lessonProgress.filter(
			(progress: any) => progress.completed
		).length;
		return totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
	};

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Minha Conta</h1>
				<p className="text-muted-foreground">
					Bem-vindo de volta, {session.user.name || session.user.email}!
				</p>
			</div>

			{/* Welcome Section */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex items-center space-x-4">
						<div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
							<UserIcon className="h-8 w-8 text-primary" />
						</div>
						<div>
							<h2 className="text-xl font-semibold">
								{session.user.name || 'Usuário'}
							</h2>
							<p className="text-muted-foreground">{session.user.email}</p>
							<p className="text-sm text-muted-foreground">
								Membro desde {new Date(session.user.createdAt).toLocaleDateString()}
							</p>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Stats */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Cursos Inscritos</CardTitle>
						<BookOpenIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{enrollments.length}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Certificados</CardTitle>
						<AwardIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{certificates.length}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Horas Estudadas</CardTitle>
						<ClockIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{Math.round(
								enrollments.reduce((acc, enrollment) =>
									acc + (enrollment.product.duration || 0), 0
								) / 60
							)}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Progresso Médio</CardTitle>
						<PlayIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{enrollments.length > 0
								? Math.round(
									enrollments.reduce((acc, enrollment) =>
										acc + calculateProgress(enrollment), 0
									) / enrollments.length
								)
								: 0
							}%
						</div>
					</CardContent>
				</Card>
			</div>

			{/* My Courses */}
			<div>
				<h2 className="text-2xl font-bold mb-4">Meus Cursos</h2>
				<div className="grid gap-4">
					{enrollments.map((enrollment) => {
						const progress = calculateProgress(enrollment);
						return (
							<Card key={enrollment.id}>
								<CardHeader>
									<div className="flex items-center justify-between">
										<div className="flex items-center space-x-4">
											{enrollment.product.thumbnail ? (
												<img
													src={enrollment.product.thumbnail}
													alt={enrollment.product.name}
													className="w-16 h-16 rounded-lg object-cover"
												/>
											) : (
												<div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
													<BookOpenIcon className="h-8 w-8 text-gray-400" />
												</div>
											)}
											<div>
												<CardTitle className="text-lg">{enrollment.product.name}</CardTitle>
												<CardDescription>
													{enrollment.product.description}
												</CardDescription>
											</div>
										</div>
										<div className="flex items-center space-x-2">
											<Badge variant={progress === 100 ? 'default' : 'secondary'}>
												{progress === 100 ? 'Concluído' : `${progress}%`}
											</Badge>
											<Button>
												<PlayIcon className="mr-2 h-4 w-4" />
												Continuar
											</Button>
										</div>
									</div>
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="flex justify-between text-sm">
											<span>Progresso</span>
											<span>{progress}%</span>
										</div>
										<div className="w-full bg-gray-200 rounded-full h-2">
											<div
												className="bg-primary h-2 rounded-full transition-all duration-300"
												style={{ width: `${progress}%` }}
											></div>
										</div>
										<div className="flex justify-between text-xs text-muted-foreground">
											<span>
												{enrollment.lessonProgress.filter((p: any) => p.completed).length} de{' '}
												{enrollment.product.modules.reduce(
													(acc: number, module: any) => acc + module.lessons.length,
													0
												)} aulas concluídas
											</span>
											<span>
												Inscrito em {new Date(enrollment.createdAt).toLocaleDateString()}
											</span>
										</div>
									</div>
								</CardContent>
							</Card>
						);
					})}
				</div>
			</div>

			{/* Certificates */}
			{certificates.length > 0 && (
				<div>
					<h2 className="text-2xl font-bold mb-4">Meus Certificados</h2>
					<div className="grid gap-4 md:grid-cols-2">
						{certificates.map((certificate) => (
							<Card key={certificate.id}>
								<CardHeader>
									<div className="flex items-center justify-between">
										<div className="flex items-center space-x-3">
											<AwardIcon className="h-8 w-8 text-yellow-500" />
											<div>
												<CardTitle className="text-lg">
													{certificate.product.name}
												</CardTitle>
												<CardDescription>
													Certificado de Conclusão
												</CardDescription>
											</div>
										</div>
										<Button variant="outline" size="sm">
											<DownloadIcon className="mr-2 h-4 w-4" />
											Baixar
										</Button>
									</div>
								</CardHeader>
								<CardContent>
									<div className="text-sm text-muted-foreground">
										<p>Emitido em {new Date(certificate.createdAt).toLocaleDateString()}</p>
										<p>ID: {certificate.id}</p>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			)}

			{enrollments.length === 0 && (
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<BookOpenIcon className="h-12 w-12 text-gray-400 mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							Nenhum curso encontrado
						</h3>
						<p className="text-gray-500 text-center mb-6">
							Você ainda não está inscrito em nenhum curso. Explore nossos produtos!
						</p>
						<Button>
							Explorar Cursos
						</Button>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
