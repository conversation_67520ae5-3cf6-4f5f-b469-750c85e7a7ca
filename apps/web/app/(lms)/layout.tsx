import { getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { Button } from "@ui/components/button";
import { UserIcon, BookOpenIcon, AwardIcon, DownloadIcon, SettingsIcon, LogOutIcon } from "lucide-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

export default async function LMSLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	return (
		<div className="min-h-screen bg-background">
			{/* Header */}
			<header className="bg-white border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						{/* Logo */}
						<div className="flex items-center">
							<BookOpenIcon className="h-8 w-8 text-primary mr-2" />
							<span className="text-xl font-bold">SupGateway</span>
						</div>

						{/* Navigation */}
						<nav className="hidden md:flex space-x-8">
							<a
								href="/account"
								className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
							>
								Meus Cursos
							</a>
							<a
								href="/account/certificates"
								className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
							>
								Certificados
							</a>
							<a
								href="/account/downloads"
								className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
							>
								Downloads
							</a>
						</nav>

						{/* User Menu */}
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" className="relative h-8 w-8 rounded-full">
									{session.user.image ? (
										<img
											src={session.user.image}
											alt={session.user.name || "User"}
											className="h-8 w-8 rounded-full"
										/>
									) : (
										<UserIcon className="h-4 w-4" />
									)}
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-56" align="end" forceMount>
								<div className="flex items-center justify-start gap-2 p-2">
									<div className="flex flex-col space-y-1 leading-none">
										{session.user.name && (
											<p className="font-medium">{session.user.name}</p>
										)}
										{session.user.email && (
											<p className="w-[200px] truncate text-sm text-muted-foreground">
												{session.user.email}
											</p>
										)}
									</div>
								</div>
								<DropdownMenuSeparator />
								<DropdownMenuItem>
									<UserIcon className="mr-2 h-4 w-4" />
									<span>Perfil</span>
								</DropdownMenuItem>
								<DropdownMenuItem>
									<SettingsIcon className="mr-2 h-4 w-4" />
									<span>Configurações</span>
								</DropdownMenuItem>
								<DropdownMenuSeparator />
								<DropdownMenuItem>
									<LogOutIcon className="mr-2 h-4 w-4" />
									<span>Sair</span>
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
				{children}
			</main>
		</div>
	);
}
