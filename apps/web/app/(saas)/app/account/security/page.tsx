import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { 
	ShieldIcon, 
	KeyIcon, 
	SmartphoneIcon, 
	AlertTriangleIcon,
	CheckCircleIcon
} from "lucide-react";
import { redirect } from "next/navigation";

export default async function SecurityPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold">Segurança</h1>
				<p className="text-muted-foreground">
					Gerencie suas configurações de segurança e autenticação
				</p>
			</div>

			{/* Autenticação de Dois Fatores */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<ShieldIcon className="h-5 w-5" />
						Autenticação de Dois Fatores
					</CardTitle>
					<CardDescription>
						Adicione uma camada extra de segurança à sua conta
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex items-center justify-between">
						<div className="space-y-0.5">
							<p className="text-sm font-medium">2FA Habilitado</p>
							<p className="text-sm text-muted-foreground">
								{session.user.twoFactorEnabled ? 'Ativo' : 'Desativado'}
							</p>
						</div>
						<Switch 
							checked={session.user.twoFactorEnabled}
							disabled={!session.user.twoFactorEnabled}
						/>
					</div>
					
					{session.user.twoFactorEnabled ? (
						<div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
							<CheckCircleIcon className="h-4 w-4 text-green-600" />
							<p className="text-sm text-green-800">
								Autenticação de dois fatores está ativa
							</p>
						</div>
					) : (
						<div className="space-y-2">
							<Button variant="outline">
								Configurar 2FA
							</Button>
							<p className="text-xs text-muted-foreground">
								Recomendamos ativar para maior segurança
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Alterar Senha */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<KeyIcon className="h-5 w-5" />
						Alterar Senha
					</CardTitle>
					<CardDescription>
						Atualize sua senha para manter sua conta segura
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="current-password">Senha Atual</Label>
						<Input
							id="current-password"
							type="password"
							placeholder="Digite sua senha atual"
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="new-password">Nova Senha</Label>
						<Input
							id="new-password"
							type="password"
							placeholder="Digite sua nova senha"
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="confirm-password">Confirmar Nova Senha</Label>
						<Input
							id="confirm-password"
							type="password"
							placeholder="Confirme sua nova senha"
						/>
					</div>
					<Button className="w-full">
						Alterar Senha
					</Button>
				</CardContent>
			</Card>

			{/* Dispositivos Conectados */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<SmartphoneIcon className="h-5 w-5" />
						Dispositivos Conectados
					</CardTitle>
					<CardDescription>
						Gerencie os dispositivos que têm acesso à sua conta
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-3">
								<SmartphoneIcon className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="text-sm font-medium">Navegador Web</p>
									<p className="text-xs text-muted-foreground">
										Chrome no Windows • Último acesso: hoje
									</p>
								</div>
							</div>
							<Button variant="outline" size="sm">
								Desconectar
							</Button>
						</div>
						
						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-3">
								<SmartphoneIcon className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="text-sm font-medium">Aplicativo Mobile</p>
									<p className="text-xs text-muted-foreground">
										iPhone • Último acesso: ontem
									</p>
								</div>
							</div>
							<Button variant="outline" size="sm">
								Desconectar
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Sessões Ativas */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<AlertTriangleIcon className="h-5 w-5" />
						Sessões Ativas
					</CardTitle>
					<CardDescription>
						Revise e gerencie suas sessões ativas
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div>
								<p className="text-sm font-medium">Sessão Atual</p>
								<p className="text-xs text-muted-foreground">
									IP: *********** • Localização: São Paulo, SP
								</p>
							</div>
							<Badge variant="default">Ativa</Badge>
						</div>
						
						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div>
								<p className="text-sm font-medium">Outra Sessão</p>
								<p className="text-xs text-muted-foreground">
									IP: *********** • Localização: Rio de Janeiro, RJ
								</p>
							</div>
							<Button variant="outline" size="sm">
								Encerrar
							</Button>
						</div>
					</div>
					
					<div className="mt-4">
						<Button variant="destructive" size="sm">
							Encerrar Todas as Outras Sessões
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

