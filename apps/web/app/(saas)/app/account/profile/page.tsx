import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { UserIcon, MailIcon, CalendarIcon } from "lucide-react";
import { redirect } from "next/navigation";

export default async function ProfilePage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold">Perfil Pessoal</h1>
				<p className="text-muted-foreground">
					Gerencie suas informações pessoais e preferências
				</p>
			</div>

			<div className="grid gap-6 md:grid-cols-2">
				{/* Informações Básicas */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<UserIcon className="h-5 w-5" />
							Informações Básicas
						</CardTitle>
						<CardDescription>
							Suas informações pessoais básicas
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="name">Nome Completo</Label>
							<Input
								id="name"
								defaultValue={session.user.name || ""}
								placeholder="Seu nome completo"
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="email">Email</Label>
							<div className="flex items-center gap-2">
								<MailIcon className="h-4 w-4 text-muted-foreground" />
								<Input
									id="email"
									defaultValue={session.user.email}
									disabled
									className="bg-muted"
								/>
							</div>
							<p className="text-xs text-muted-foreground">
								O email não pode ser alterado
							</p>
						</div>
						<div className="space-y-2">
							<Label htmlFor="username">Nome de Usuário</Label>
							<Input
								id="username"
								defaultValue={session.user.username || ""}
								placeholder="@seuusuario"
							/>
						</div>
						<Button className="w-full">
							Salvar Alterações
						</Button>
					</CardContent>
				</Card>

				{/* Avatar e Informações Adicionais */}
				<Card>
					<CardHeader>
						<CardTitle>Avatar</CardTitle>
						<CardDescription>
							Seu avatar será exibido em toda a plataforma
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center space-x-4">
							<div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center">
								<UserIcon className="h-10 w-10 text-primary" />
							</div>
							<div className="space-y-2">
								<Button variant="outline" size="sm">
									Alterar Avatar
								</Button>
								<p className="text-xs text-muted-foreground">
									JPG, PNG ou GIF. Máximo 2MB.
								</p>
							</div>
						</div>
						
						<div className="space-y-2">
							<Label htmlFor="bio">Biografia</Label>
							<textarea
								id="bio"
								className="w-full min-h-[100px] p-3 border rounded-md resize-none"
								placeholder="Conte um pouco sobre você..."
							/>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Informações da Conta */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<CalendarIcon className="h-5 w-5" />
						Informações da Conta
					</CardTitle>
					<CardDescription>
						Detalhes sobre sua conta e atividade
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-3">
						<div className="space-y-1">
							<p className="text-sm font-medium">Membro desde</p>
							<p className="text-sm text-muted-foreground">
								{new Date(session.user.createdAt).toLocaleDateString('pt-BR')}
							</p>
						</div>
						<div className="space-y-1">
							<p className="text-sm font-medium">Função</p>
							<p className="text-sm text-muted-foreground">
								{session.user.role}
							</p>
						</div>
						<div className="space-y-1">
							<p className="text-sm font-medium">Status</p>
							<p className="text-sm text-green-600">
								Ativo
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

