import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { 
	UserIcon, 
	ShieldIcon, 
	SettingsIcon, 
	CreditCardIcon, 
	BuildingIcon,
	ArrowRightIcon,
	BellIcon,
	GlobeIcon
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";

export default async function AccountPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	// Buscar organizações do usuário
	const userOrganizations = await db.member.findMany({
		where: {
			userId: session.user.id,
		},
		include: {
			organization: true,
		},
	});

	// Buscar dados do usuário
	const user = await db.user.findUnique({
		where: { id: session.user.id },
		include: {
			organizationMembers: {
				include: {
					organization: true,
				},
			},
		},
	});

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Minha Conta</h1>
					<p className="text-muted-foreground">
						Gerencie suas configurações pessoais e organizações
					</p>
				</div>
				<Badge variant="outline" className="text-sm">
					{user?.role || 'Usuário'}
				</Badge>
			</div>

			{/* Informações do Usuário */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex items-center space-x-4">
						<div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
							<UserIcon className="h-8 w-8 text-primary" />
						</div>
						<div className="flex-1">
							<h2 className="text-xl font-semibold">
								{user?.name || 'Usuário'}
							</h2>
							<p className="text-muted-foreground">{user?.email}</p>
							<p className="text-sm text-muted-foreground">
								Membro desde {new Date(user?.createdAt || Date.now()).toLocaleDateString('pt-BR')}
							</p>
						</div>
						<Link href="/app/account/profile">
							<Button variant="outline" size="sm">
								Editar Perfil
							</Button>
						</Link>
					</div>
				</CardContent>
			</Card>

			{/* Configurações Pessoais */}
			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Perfil Pessoal</CardTitle>
						<UserIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Gerencie suas informações pessoais e preferências
						</CardDescription>
						<div className="space-y-2">
							<Link href="/app/account/profile">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Perfil
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href="/app/account/security">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Segurança
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href="/app/account/preferences">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Preferências
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Notificações</CardTitle>
						<BellIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Configure como você recebe notificações
						</CardDescription>
						<Link href="/app/account/notifications">
							<Button variant="outline" size="sm" className="w-full justify-between">
								Configurar
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Idioma e Região</CardTitle>
						<GlobeIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Configure idioma, fuso horário e região
						</CardDescription>
						<Link href="/app/account/locale">
							<Button variant="outline" size="sm" className="w-full justify-between">
								Configurar
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>
			</div>

			{/* Organizações */}
			<div className="space-y-4">
				<div className="flex items-center justify-between">
					<h2 className="text-xl font-semibold">Minhas Organizações</h2>
					<Link href="/app/organizations/create">
						<Button size="sm">
							Criar Organização
						</Button>
					</Link>
				</div>

				{userOrganizations.length > 0 ? (
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						{userOrganizations.map((member) => (
							<Card key={member.organization.id}>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">
										{member.organization.name}
									</CardTitle>
									<Badge variant={member.role === 'admin' ? 'default' : 'secondary'}>
										{member.role}
									</Badge>
								</CardHeader>
								<CardContent>
									<CardDescription className="mb-4">
										{member.organization.domain && (
											<p className="text-xs text-muted-foreground">
												{member.organization.domain}
											</p>
										)}
									</CardDescription>
									<div className="space-y-2">
										<Link href={`/app/${member.organization.slug}`}>
											<Button variant="outline" size="sm" className="w-full">
												Acessar Dashboard
											</Button>
										</Link>
										{member.role === 'admin' && (
											<Link href={`/app/${member.organization.slug}/settings`}>
												<Button variant="outline" size="sm" className="w-full">
													Configurações
												</Button>
											</Link>
										)}
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				) : (
					<Card>
						<CardContent className="pt-6">
							<div className="text-center">
								<BuildingIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
								<h3 className="text-lg font-medium mb-2">Nenhuma organização</h3>
								<p className="text-muted-foreground mb-4">
									Você ainda não faz parte de nenhuma organização.
								</p>
								<Link href="/app/organizations/create">
									<Button>
										Criar Primeira Organização
									</Button>
								</Link>
							</div>
						</CardContent>
					</Card>
				)}
			</div>

			{/* Acesso Admin (se aplicável) */}
			{(user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN') && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<ShieldIcon className="h-5 w-5" />
							Acesso Administrativo
						</CardTitle>
						<CardDescription>
							Você tem acesso às configurações administrativas do sistema
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Link href="/app/admin">
							<Button variant="outline" className="w-full justify-between">
								Acessar Painel Admin
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

