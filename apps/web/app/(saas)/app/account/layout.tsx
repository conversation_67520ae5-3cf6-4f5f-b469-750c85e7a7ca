import { getSession } from "@saas/auth/lib/server";
import { SettingsMenu } from "@saas/settings/components/SettingsMenu";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { SidebarContentLayout } from "@saas/shared/components/SidebarContentLayout";
import { UserAvatar } from "@shared/components/UserAvatar";
import { 
	UserIcon, 
	ShieldIcon, 
	SettingsIcon, 
	BellIcon,
	GlobeIcon,
	BuildingIcon,
	CreditCardIcon
} from "lucide-react";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import type { PropsWithChildren } from "react";

export default async function AccountLayout({ children }: PropsWithChildren) {
	const t = await getTranslations();
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	const accountMenuItems = [
		{
			title: "Minha Conta",
			avatar: (
				<UserAvatar
					name={session.user.name ?? ""}
					avatarUrl={session.user.image}
				/>
			),
			items: [
				{
					title: "Visão Geral",
					href: "/app/account",
					icon: <UserIcon className="size-4 opacity-50" />,
				},
				{
					title: "Perfil",
					href: "/app/account/profile",
					icon: <UserIcon className="size-4 opacity-50" />,
				},
				{
					title: "Segurança",
					href: "/app/account/security",
					icon: <ShieldIcon className="size-4 opacity-50" />,
				},
				{
					title: "Preferências",
					href: "/app/account/preferences",
					icon: <SettingsIcon className="size-4 opacity-50" />,
				},
				{
					title: "Notificações",
					href: "/app/account/notifications",
					icon: <BellIcon className="size-4 opacity-50" />,
				},
				{
					title: "Idioma e Região",
					href: "/app/account/locale",
					icon: <GlobeIcon className="size-4 opacity-50" />,
				},
			],
		},
		{
			title: "Organizações",
			avatar: (
				<div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
					<BuildingIcon className="h-4 w-4 text-primary" />
				</div>
			),
			items: [
				{
					title: "Minhas Organizações",
					href: "/app/account/organizations",
					icon: <BuildingIcon className="size-4 opacity-50" />,
				},
			],
		},
	];

	return (
		<>
			<PageHeader
				title="Minha Conta"
				subtitle="Gerencie suas configurações pessoais e organizações"
			/>
			<SidebarContentLayout
				sidebar={<SettingsMenu menuItems={accountMenuItems} />}
			>
				{children}
			</SidebarContentLayout>
		</>
	);
}

