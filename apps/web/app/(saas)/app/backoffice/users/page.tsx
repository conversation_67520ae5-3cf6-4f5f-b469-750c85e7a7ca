import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { SearchIcon, FilterIcon, PlusIcon, MoreHorizontalIcon, UserIcon, MailIcon, CalendarIcon } from "lucide-react";
import { getUsers, countAllUsers } from "@repo/database";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

interface SearchParams {
	query?: string;
	role?: string;
	status?: string;
}

export default async function AdminUsersPage({
	searchParams,
}: {
	searchParams: Promise<SearchParams>;
}) {
	const { query, role, status } = await searchParams;

	const users = await getUsers({
		limit: 50,
		offset: 0,
		query,
	});

	const getRoleBadgeVariant = (role: string) => {
		switch (role) {
			case "SUPER_ADMIN":
				return "destructive";
			case "ADMIN":
				return "default";
			case "SELLER":
				return "secondary";
			case "CUSTOMER":
				return "outline";
			default:
				return "outline";
		}
	};

	const getRoleLabel = (role: string) => {
		switch (role) {
			case "SUPER_ADMIN":
				return "Super Admin";
			case "ADMIN":
				return "Admin";
			case "SELLER":
				return "Vendedor";
			case "CUSTOMER":
				return "Cliente";
			default:
				return role;
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Usuários</h1>
					<p className="text-muted-foreground">
						Gerencie todos os usuários do sistema. Total: {users.length}
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Novo Usuário
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar usuários..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			<div className="grid gap-4">
				{users.map((user) => (
					<Card key={user.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
										{user.image ? (
											<img
												src={user.image}
												alt={user.name || "User"}
												className="w-10 h-10 rounded-full"
											/>
										) : (
											<UserIcon className="h-5 w-5 text-muted-foreground" />
										)}
									</div>
									<div>
										<CardTitle className="text-lg">
											{user.name || "Usuário sem nome"}
										</CardTitle>
										<CardDescription className="flex items-center gap-2">
											<MailIcon className="h-4 w-4" />
											{user.email}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<Badge variant={getRoleBadgeVariant(user.role)}>
										{getRoleLabel(user.role)}
									</Badge>
									<Badge variant={user.emailVerified ? "default" : "secondary"}>
										{user.emailVerified ? "Verificado" : "Não verificado"}
									</Badge>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>Visualizar</DropdownMenuItem>
											<DropdownMenuItem>Editar</DropdownMenuItem>
											<DropdownMenuItem>Alterar Role</DropdownMenuItem>
											<DropdownMenuItem className="text-destructive">
												{user.banned ? "Desbanir" : "Banir"}
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4">
								<div className="flex items-center space-x-2">
									<CalendarIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Criado em</p>
										<p className="text-xs text-muted-foreground">
											{new Date(user.createdAt).toLocaleDateString()}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<UserIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Username</p>
										<p className="text-xs text-muted-foreground">
											{user.username || "Não definido"}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<MailIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Status</p>
										<p className="text-xs text-muted-foreground">
											{user.onboardingComplete ? "Completo" : "Pendente"}
										</p>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
