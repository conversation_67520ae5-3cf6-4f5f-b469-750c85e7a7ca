import { db } from "@repo/database";
import { notFound } from "next/navigation";

interface CommunitiesPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

async function getCommunities(organizationSlug: string) {
  const organization = await db.organization.findUnique({
    where: { slug: organizationSlug },
    select: { id: true },
  });

  if (!organization) {
    return null;
  }

  return await db.community.findMany({
    where: { organizationId: organization.id },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          logo: true,
        },
      },
    },
    orderBy: { createdAt: "desc" },
  });
}

export default async function CommunitiesPage({ params }: CommunitiesPageProps) {
  const { organizationSlug } = await params;
  const communities = await getCommunities(organizationSlug);

  if (!communities) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Minhas Comunidades</h1>
            <p className="text-gray-600">Gerencie suas comunidades e acompanhe o desempenho</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {communities.map((community) => (
            <div key={community.id} className="border rounded-lg p-4">
              <h3 className="font-semibold text-lg">{community.name}</h3>
              <p className="text-gray-600 text-sm">{community.description}</p>
              <div className="mt-2 flex items-center gap-2">
                <span className="text-sm text-gray-500">
                  {community.memberCount} membros
                </span>
                <span className="text-sm text-gray-500">
                  {community.isPublic ? "Pública" : "Privada"}
                </span>
              </div>
            </div>
          ))}
        </div>

        {communities.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Nenhuma comunidade criada
            </h3>
            <p className="text-gray-500">
              Comece criando sua primeira comunidade para conectar pessoas.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}