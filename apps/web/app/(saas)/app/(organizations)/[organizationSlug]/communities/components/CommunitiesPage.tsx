"use client";

import { useState } from "react";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { But<PERSON> } from "@ui/components/button";
import { PlusIcon, UsersIcon, MessageSquareIcon, TrendingUpIcon, Users2Icon, ActivityIcon, HeartIcon } from "lucide-react";
import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import { CommunitiesClient } from "./CommunitiesClient";
import { CreateCommunityModal } from "./CreateCommunityModal";

// Mock data para demonstração
const mockMetrics = {
  totalCommunities: 12,
  communitiesGrowth: 8.5,
  totalMembers: 2847,
  membersGrowth: 15.2,
  engagementRate: 92.1,
  engagementGrowth: 7.3,
  totalPosts: 1247,
  postsGrowth: 22.8
};

const mockCommunities = [
  {
    id: "1",
    name: "Marketing Digital Brasil",
    description: "Comunidade para profissionais de marketing digital trocarem experiências e estratégias",
    category: "Marketing",
    memberCount: 1247,
    postsCount: 342,
    status: "active" as const,
    createdAt: "2024-01-15",
    lastActivity: "2024-01-20",
    features: ["Fórum", "Networking", "Webinars", "Mentorias"],
    thumbnail: "/api/placeholder/400/200",
    isPrivate: false,
    tags: ["marketing", "digital", "estratégia"]
  },
  {
    id: "2", 
    name: "Vendas de Alto Impacto",
    description: "Grupo exclusivo para vendedores que querem aumentar suas vendas",
    category: "Vendas",
    memberCount: 892,
    postsCount: 156,
    status: "active" as const,
    createdAt: "2024-01-10",
    lastActivity: "2024-01-19",
    features: ["Cases", "Técnicas", "Suporte", "Certificação"],
    thumbnail: "/api/placeholder/400/200",
    isPrivate: true,
    tags: ["vendas", "técnicas", "alto-impacto"]
  },
  {
    id: "3",
    name: "Empreendedores Digitais",
    description: "Comunidade para empreendedores que querem escalar seus negócios online",
    category: "Empreendedorismo",
    memberCount: 708,
    postsCount: 89,
    status: "active" as const,
    createdAt: "2024-01-05",
    lastActivity: "2024-01-18",
    features: ["Investidores", "Mentorias", "Workshops", "Networking"],
    thumbnail: "/api/placeholder/400/200",
    isPrivate: false,
    tags: ["empreendedorismo", "negócios", "escala"]
  }
];

interface CommunitiesPageProps {
  organizationSlug: string;
}

export function CommunitiesPage({ organizationSlug }: CommunitiesPageProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Formatadores de valores
  const formatNumber = (value: number) => {
    return value.toLocaleString('pt-BR');
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Comunidades"
        subtitle="Gerencie comunidades e grupos para engajar seus membros e criar networking"
        actions={
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Nova Comunidade
          </Button>
        }
      />

      {/* Métricas Principais */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Visão Geral das Comunidades</h2>
        <MetricGrid columns={4}>
          <MetricCard
            title="Total de Comunidades"
            value={formatNumber(mockMetrics.totalCommunities)}
            change={formatPercentage(mockMetrics.communitiesGrowth)}
            isPositive={mockMetrics.communitiesGrowth >= 0}
            icon={MessageSquareIcon}
            description="Comunidades ativas"
            badge={{
              text: "ATIVAS",
              status: "success"
            }}
            className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
          />

          <MetricCard
            title="Total de Membros"
            value={formatNumber(mockMetrics.totalMembers)}
            change={formatPercentage(mockMetrics.membersGrowth)}
            isPositive={mockMetrics.membersGrowth >= 0}
            icon={UsersIcon}
            description="Membros em todas as comunidades"
            badge={{
              text: "CRESCIMENTO",
              status: "info"
            }}
            className="bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20"
          />

          <MetricCard
            title="Taxa de Engajamento"
            value={`${mockMetrics.engagementRate.toFixed(1)}%`}
            change={formatPercentage(mockMetrics.engagementGrowth)}
            isPositive={mockMetrics.engagementGrowth >= 0}
            icon={TrendingUpIcon}
            description="Membros que participam ativamente"
            badge={{
              text: "ALTA",
              status: "success"
            }}
            className="bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20"
          />

          <MetricCard
            title="Posts e Interações"
            value={formatNumber(mockMetrics.totalPosts)}
            change={formatPercentage(mockMetrics.postsGrowth)}
            isPositive={mockMetrics.postsGrowth >= 0}
            icon={ActivityIcon}
            description="Conteúdo compartilhado"
            badge={{
              text: "CRESCIMENTO",
              status: "info"
            }}
            className="bg-gradient-to-br from-orange-50/50 to-transparent dark:from-orange-950/20"
          />
        </MetricGrid>
      </div>

      {/* Lista de Comunidades */}
      <CommunitiesClient 
        communities={mockCommunities}
        organizationSlug={organizationSlug}
      />

      {/* Modal de Criação */}
      <CreateCommunityModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        organizationSlug={organizationSlug}
      />
    </div>
  );
}
