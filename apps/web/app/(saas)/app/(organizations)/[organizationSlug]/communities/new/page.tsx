"use client";

import { CreateCommunityForm } from "../../../../../../modules/communities";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface NewCommunityPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

export default function NewCommunityPage({ params }: NewCommunityPageProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: any) => {
    setLoading(true);
    try {
      // TODO: Implement API call to create community
      console.log("Creating community:", data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Redirect to communities list
      router.push(`/app/${params.organizationSlug}/communities`);
    } catch (error) {
      console.error("Error creating community:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Criar Nova Comunidade</h1>
        <p className="text-gray-600">
          Configure sua comunidade e comece a conectar pessoas
        </p>
      </div>

      <CreateCommunityForm
        organizationId="temp-org-id" // TODO: Get from params or context
        onSubmit={handleSubmit}
        loading={loading}
      />
    </div>
  );
}
