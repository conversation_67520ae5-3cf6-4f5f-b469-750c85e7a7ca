"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Checkbox } from "@ui/components/checkbox";
import { Badge } from "@ui/components/badge";
import { XIcon, PlusIcon } from "lucide-react";
import { toast } from "sonner";

const createCommunitySchema = z.object({
  name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  description: z.string().min(10, "Descrição deve ter pelo menos 10 caracteres"),
  category: z.string().min(1, "Selecione uma categoria"),
  features: z.array(z.string()).min(1, "Adicione pelo menos um recurso"),
  customFeature: z.string().optional(),
  isPrivate: z.boolean().default(false),
  allowInvites: z.boolean().default(true),
  requireApproval: z.boolean().default(false),
  tags: z.array(z.string()).min(1, "Adicione pelo menos uma tag"),
  customTag: z.string().optional(),
});

type CreateCommunityFormData = z.infer<typeof createCommunitySchema>;

interface CreateCommunityModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationSlug: string;
}

// Mock de categorias para demonstração
const categories = [
  "Marketing",
  "Vendas", 
  "Empreendedorismo",
  "Tecnologia",
  "Educação",
  "Saúde",
  "Finanças",
  "Outros"
];

const defaultFeatures = [
  "Fórum",
  "Networking",
  "Webinars",
  "Mentorias",
  "Cases de sucesso",
  "Workshops",
  "Consultoria",
  "Investidores",
  "Certificação",
  "Suporte",
  "Acesso vitalício",
  "Comunidade ativa",
];

const defaultTags = [
  "marketing",
  "vendas",
  "empreendedorismo",
  "tecnologia",
  "educação",
  "networking",
  "mentoria",
  "negócios",
  "digital",
  "crescimento"
];

export function CreateCommunityModal({ 
  isOpen, 
  onClose, 
  organizationSlug 
}: CreateCommunityModalProps) {
  const [customFeature, setCustomFeature] = useState("");
  const [customTag, setCustomTag] = useState("");
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const form = useForm<CreateCommunityFormData>({
    resolver: zodResolver(createCommunitySchema),
    defaultValues: {
      name: "",
      description: "",
      category: "",
      features: [],
      isPrivate: false,
      allowInvites: true,
      requireApproval: false,
      tags: [],
    },
  });

  const onSubmit = async (data: CreateCommunityFormData) => {
    try {
      // Simular criação da comunidade
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Comunidade criada com sucesso!");
      form.reset();
      setSelectedFeatures([]);
      setSelectedTags([]);
      setCustomFeature("");
      setCustomTag("");
      onClose();
    } catch (error) {
      toast.error("Erro ao criar comunidade");
    }
  };

  const handleFeatureToggle = (feature: string) => {
    const newFeatures = selectedFeatures.includes(feature)
      ? selectedFeatures.filter(f => f !== feature)
      : [...selectedFeatures, feature];
    
    setSelectedFeatures(newFeatures);
    form.setValue("features", newFeatures);
  };

  const handleAddCustomFeature = () => {
    if (customFeature.trim() && !selectedFeatures.includes(customFeature.trim())) {
      const newFeatures = [...selectedFeatures, customFeature.trim()];
      setSelectedFeatures(newFeatures);
      form.setValue("features", newFeatures);
      setCustomFeature("");
    }
  };

  const handleRemoveFeature = (feature: string) => {
    const newFeatures = selectedFeatures.filter(f => f !== feature);
    setSelectedFeatures(newFeatures);
    form.setValue("features", newFeatures);
  };

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    
    setSelectedTags(newTags);
    form.setValue("tags", newTags);
  };

  const handleAddCustomTag = () => {
    if (customTag.trim() && !selectedTags.includes(customTag.trim())) {
      const newTags = [...selectedTags, customTag.trim()];
      setSelectedTags(newTags);
      form.setValue("tags", newTags);
      setCustomTag("");
    }
  };

  const handleRemoveTag = (tag: string) => {
    const newTags = selectedTags.filter(t => t !== tag);
    setSelectedTags(newTags);
    form.setValue("tags", newTags);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Criar Nova Comunidade</DialogTitle>
          <DialogDescription>
            Crie uma comunidade para engajar seus membros e facilitar networking. 
            Configure recursos e permissões para maximizar a participação.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informações Básicas */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Informações Básicas</h3>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Comunidade</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Marketing Digital Brasil" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Descreva o propósito e benefícios desta comunidade..."
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Explique o que os membros encontrarão e como podem se beneficiar
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Recursos e Benefícios */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Recursos e Benefícios</h3>
              
              <div className="space-y-3">
                <FormLabel>Selecione os recursos disponíveis:</FormLabel>
                <div className="grid grid-cols-2 gap-2">
                  {defaultFeatures.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <Checkbox
                        id={feature}
                        checked={selectedFeatures.includes(feature)}
                        onCheckedChange={() => handleFeatureToggle(feature)}
                      />
                      <label 
                        htmlFor={feature}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {feature}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Adicionar recurso personalizado */}
              <div className="space-y-2">
                <FormLabel>Adicionar recurso personalizado:</FormLabel>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Ex: Acesso a investidores"
                    value={customFeature}
                    onChange={(e) => setCustomFeature(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddCustomFeature())}
                  />
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={handleAddCustomFeature}
                    disabled={!customFeature.trim()}
                  >
                    <PlusIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Recursos selecionados */}
              {selectedFeatures.length > 0 && (
                <div className="space-y-2">
                  <FormLabel>Recursos selecionados:</FormLabel>
                  <div className="flex flex-wrap gap-2">
                    {selectedFeatures.map((feature) => (
                      <Badge key={feature} variant="secondary" className="flex items-center gap-1">
                        {feature}
                        <button
                          type="button"
                          onClick={() => handleRemoveFeature(feature)}
                          className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                        >
                          <XIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Tags</h3>
              
              <div className="space-y-3">
                <FormLabel>Selecione tags relevantes:</FormLabel>
                <div className="flex flex-wrap gap-2">
                  {defaultTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => handleTagToggle(tag)}
                    >
                      #{tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Adicionar tag personalizada */}
              <div className="space-y-2">
                <FormLabel>Adicionar tag personalizada:</FormLabel>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Ex: crescimento"
                    value={customTag}
                    onChange={(e) => setCustomTag(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddCustomTag())}
                  />
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={handleAddCustomTag}
                    disabled={!customTag.trim()}
                  >
                    <PlusIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Tags selecionadas */}
              {selectedTags.length > 0 && (
                <div className="space-y-2">
                  <FormLabel>Tags selecionadas:</FormLabel>
                  <div className="flex flex-wrap gap-2">
                    {selectedTags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        #{tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                        >
                          <XIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Configurações de Acesso */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Configurações de Acesso</h3>
              
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="isPrivate"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Comunidade Privada</FormLabel>
                        <FormDescription>
                          Apenas membros aprovados podem ver e participar
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allowInvites"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Permitir Convites</FormLabel>
                        <FormDescription>
                          Membros podem convidar outras pessoas
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requireApproval"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Aprovação Manual</FormLabel>
                        <FormDescription>
                          Novos membros precisam ser aprovados manualmente
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? "Criando..." : "Criar Comunidade"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
