'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Switch } from '@ui/components/switch';
import { Textarea } from '@ui/components/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/tabs';
import { Badge } from '@ui/components/badge';
import { Plus, Trash2, Save, Eye } from 'lucide-react';
import { useToast } from '@ui/hooks/use-toast';

// Schema de validação para configurações do checkout
const checkoutSettingsSchema = z.object({
  // Banner
  bannerEnabled: z.boolean().default(true),
  bannerUrl: z.string().optional(),
  bannerMaxHeight: z.string().default('300px'),
  bannerBorderRadius: z.string().default('rounded-lg'),
  bannerShadow: z.boolean().default(true),

  // Urgency Bar
  urgencyEnabled: z.boolean().default(false),
  urgencyMessage: z.string().default('Esta oferta se encerra em:'),
  urgencyEndTime: z.string().optional(),
  urgencyBackgroundColor: z.string().default('bg-red-50'),
  urgencyTextColor: z.string().default('text-white'),
  urgencyAccentColor: z.string().default('bg-red-600'),

  // Trust Badges
  trustBadgesEnabled: z.boolean().default(true),
  trustBadgesLayout: z.enum(['horizontal', 'vertical', 'grid']).default('horizontal'),
  trustBadgesShowDescriptions: z.boolean().default(true),
  trustBadgesBackgroundColor: z.string().default('bg-blue-50'),
  trustBadgesTextColor: z.string().default('text-blue-800'),
  trustBadgesBorderColor: z.string().default('border-blue-200'),
  trustBadges: z.array(z.object({
    id: z.string(),
    title: z.string(),
    subtitle: z.string(),
    icon: z.enum(['shield', 'lock', 'mail', 'check', 'star', 'award', 'truck', 'credit-card']),
    enabled: z.boolean()
  })).default([]),

  // Scarcity
  scarcityEnabled: z.boolean().default(false),
  scarcityTotalStock: z.number().default(100),
  scarcitySoldCount: z.number().default(0),
  scarcityMessage: z.string().default('Apenas {remaining} vagas restantes!'),
  scarcityVariant: z.enum(['warning', 'danger', 'info']).default('warning'),
  scarcityShowIcon: z.boolean().default(true),
  scarcityBackgroundColor: z.string().optional(),
  scarcityTextColor: z.string().optional(),
  scarcityBorderColor: z.string().optional(),

  // Testimonials
  testimonialsEnabled: z.boolean().default(true),
  testimonialsMaxTestimonials: z.number().default(3),
  testimonialsAutoPlay: z.boolean().default(true),
  testimonialsAutoPlayInterval: z.number().default(5000),
  testimonialsShowControls: z.boolean().default(true),
  testimonialsShowStars: z.boolean().default(true),
  testimonialsShowAvatars: z.boolean().default(true),
  testimonialsBackgroundColor: z.string().default('bg-gray-50'),
  testimonialsTextColor: z.string().default('text-gray-800'),
  testimonialsBorderColor: z.string().default('border-gray-200'),
  testimonials: z.array(z.object({
    id: z.string(),
    name: z.string(),
    rating: z.number().min(1).max(5),
    comment: z.string(),
    avatar: z.string().optional(),
    location: z.string().optional(),
    verified: z.boolean().default(false)
  })).default([]),

  // Header
  headerShowLogo: z.boolean().default(false),
  headerLogoUrl: z.string().optional(),
  headerCompanyName: z.string().default('SupGateway'),

  // Sidebar
  sidebarEnabled: z.boolean().default(false),
  sidebarBannerUrl: z.string().optional(),
  sidebarTitle: z.string().default('Informações Importantes'),
  sidebarContent: z.string().default('Adicione informações úteis para seus clientes aqui.'),
  sidebarBackgroundColor: z.string().default('bg-blue-50'),
  sidebarTextColor: z.string().default('text-blue-800'),
  sidebarBorderColor: z.string().default('border-blue-200'),
  sidebarBorderRadius: z.string().default('rounded-lg'),
  sidebarShadow: z.boolean().default(true)
});

type CheckoutSettingsFormData = z.infer<typeof checkoutSettingsSchema>;

interface CheckoutSettingsFormProps {
  productId: string;
  organizationId: string;
  initialSettings?: any;
  onSave?: (settings: any) => void;
}

const defaultTrustBadges = [
  {
    id: 'privacy',
    title: 'Privacidade',
    subtitle: 'Sua informação 100% segura',
    icon: 'star' as const,
    enabled: true
  },
  {
    id: 'secure-purchase',
    title: 'Compra segura',
    subtitle: 'Ambiente seguro e autenticado',
    icon: 'shield' as const,
    enabled: true
  },
  {
    id: 'email-delivery',
    title: 'Entregue via E-mail',
    subtitle: 'Acesso ao produto entregue por email',
    icon: 'mail' as const,
    enabled: true
  },
  {
    id: 'approved-content',
    title: 'Conteúdo aprovado',
    subtitle: '100% revisado e aprovado',
    icon: 'check' as const,
    enabled: true
  }
];

const defaultTestimonials = [
  {
    id: '1',
    name: 'Maria Silva',
    rating: 5,
    comment: 'Produto excelente! Superou minhas expectativas. Recomendo para todos.',
    location: 'São Paulo, SP',
    verified: true
  },
  {
    id: '2',
    name: 'João Santos',
    rating: 5,
    comment: 'Qualidade incrível e entrega rápida. Vale cada centavo investido.',
    location: 'Rio de Janeiro, RJ',
    verified: true
  },
  {
    id: '3',
    name: 'Ana Costa',
    rating: 5,
    comment: 'Transformou minha vida! Não me arrependo da compra.',
    location: 'Belo Horizonte, MG',
    verified: true
  }
];

export function CheckoutSettingsForm({
  productId,
  organizationId,
  initialSettings = {},
  onSave
}: CheckoutSettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const { toast } = useToast();

  const form = useForm<CheckoutSettingsFormData>({
    resolver: zodResolver(checkoutSettingsSchema),
    defaultValues: {
      bannerEnabled: true,
      bannerUrl: initialSettings.banner || '',
      bannerMaxHeight: '300px',
      bannerBorderRadius: 'rounded-lg',
      bannerShadow: true,

      urgencyEnabled: false,
      urgencyMessage: 'Esta oferta se encerra em:',
      urgencyEndTime: '',
      urgencyBackgroundColor: 'bg-red-50',
      urgencyTextColor: 'text-white',
      urgencyAccentColor: 'bg-red-600',

      trustBadgesEnabled: true,
      trustBadgesLayout: 'horizontal',
      trustBadgesShowDescriptions: true,
      trustBadgesBackgroundColor: 'bg-blue-50',
      trustBadgesTextColor: 'text-blue-800',
      trustBadgesBorderColor: 'border-blue-200',
      trustBadges: initialSettings.trustBadges?.badges || defaultTrustBadges,

      scarcityEnabled: false,
      scarcityTotalStock: 100,
      scarcitySoldCount: 0,
      scarcityMessage: 'Apenas {remaining} vagas restantes!',
      scarcityVariant: 'warning',
      scarcityShowIcon: true,

      testimonialsEnabled: true,
      testimonialsMaxTestimonials: 3,
      testimonialsAutoPlay: true,
      testimonialsAutoPlayInterval: 5000,
      testimonialsShowControls: true,
      testimonialsShowStars: true,
      testimonialsShowAvatars: true,
      testimonialsBackgroundColor: 'bg-gray-50',
      testimonialsTextColor: 'text-gray-800',
      testimonialsBorderColor: 'border-gray-200',
      testimonials: initialSettings.testimonials?.testimonials || defaultTestimonials,

      // Header
      headerShowLogo: initialSettings.header?.showLogo || false,
      headerLogoUrl: initialSettings.header?.logoUrl || '',
      headerCompanyName: initialSettings.header?.companyName || 'SupGateway',

      // Sidebar
      sidebarEnabled: initialSettings.sidebar?.enabled || false,
      sidebarBannerUrl: initialSettings.sidebar?.bannerUrl || '',
      sidebarTitle: initialSettings.sidebar?.title || 'Informações Importantes',
      sidebarContent: initialSettings.sidebar?.content || 'Adicione informações úteis para seus clientes aqui.',
      sidebarBackgroundColor: initialSettings.sidebar?.backgroundColor || 'bg-blue-50',
      sidebarTextColor: initialSettings.sidebar?.textColor || 'text-blue-800',
      sidebarBorderColor: initialSettings.sidebar?.borderColor || 'border-blue-200',
      sidebarBorderRadius: initialSettings.sidebar?.borderRadius || 'rounded-lg',
      sidebarShadow: initialSettings.sidebar?.shadow || true
    }
  });

  const onSubmit = async (data: CheckoutSettingsFormData) => {
    setIsLoading(true);
    try {
      // Aqui você faria a chamada para salvar as configurações
      console.log('Saving checkout settings:', data);

      // Simular delay de salvamento
      await new Promise(resolve => setTimeout(resolve, 1000));

      onSave?.(data);
      toast({
        title: 'Configurações salvas',
        description: 'As configurações do checkout foram atualizadas com sucesso.',
      });
    } catch (error) {
      console.error('Error saving checkout settings:', error);
      toast({
        title: 'Erro ao salvar',
        description: 'Ocorreu um erro ao salvar as configurações. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addTrustBadge = () => {
    const currentBadges = form.getValues('trustBadges');
    const newBadge = {
      id: `badge-${Date.now()}`,
      title: 'Novo Badge',
      subtitle: 'Descrição do badge',
      icon: 'shield' as const,
      enabled: true
    };
    form.setValue('trustBadges', [...currentBadges, newBadge]);
  };

  const removeTrustBadge = (index: number) => {
    const currentBadges = form.getValues('trustBadges');
    form.setValue('trustBadges', currentBadges.filter((_, i) => i !== index));
  };

  const addTestimonial = () => {
    const currentTestimonials = form.getValues('testimonials');
    const newTestimonial = {
      id: `testimonial-${Date.now()}`,
      name: 'Novo Cliente',
      rating: 5,
      comment: 'Comentário do cliente',
      location: 'Cidade, Estado',
      verified: true
    };
    form.setValue('testimonials', [...currentTestimonials, newTestimonial]);
  };

  const removeTestimonial = (index: number) => {
    const currentTestimonials = form.getValues('testimonials');
    form.setValue('testimonials', currentTestimonials.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Configurações do Checkout</h2>
          <p className="text-muted-foreground">
            Personalize a aparência e elementos de conversão do seu checkout
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Editar' : 'Visualizar'}
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs defaultValue="banner" className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="banner">Banner</TabsTrigger>
            <TabsTrigger value="header">Header</TabsTrigger>
            <TabsTrigger value="urgency">Urgência</TabsTrigger>
            <TabsTrigger value="trust">Confiança</TabsTrigger>
            <TabsTrigger value="scarcity">Escassez</TabsTrigger>
            <TabsTrigger value="testimonials">Depoimentos</TabsTrigger>
            <TabsTrigger value="sidebar">Sidebar</TabsTrigger>
          </TabsList>

          {/* Banner Tab */}
          <TabsContent value="banner" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Banner do Checkout</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="bannerEnabled"
                    checked={form.watch('bannerEnabled')}
                    onCheckedChange={(checked) => form.setValue('bannerEnabled', checked)}
                  />
                  <Label htmlFor="bannerEnabled">Exibir banner</Label>
                </div>

                {form.watch('bannerEnabled') && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="bannerUrl">URL da imagem do banner</Label>
                      <Input
                        id="bannerUrl"
                        {...form.register('bannerUrl')}
                        placeholder="https://exemplo.com/banner.jpg"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="bannerMaxHeight">Altura máxima</Label>
                        <Input
                          id="bannerMaxHeight"
                          {...form.register('bannerMaxHeight')}
                          placeholder="300px"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="bannerBorderRadius">Bordas arredondadas</Label>
                        <Select
                          value={form.watch('bannerBorderRadius')}
                          onValueChange={(value) => form.setValue('bannerBorderRadius', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="rounded-none">Nenhuma</SelectItem>
                            <SelectItem value="rounded-sm">Pequena</SelectItem>
                            <SelectItem value="rounded">Média</SelectItem>
                            <SelectItem value="rounded-lg">Grande</SelectItem>
                            <SelectItem value="rounded-xl">Extra Grande</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="bannerShadow"
                        checked={form.watch('bannerShadow')}
                        onCheckedChange={(checked) => form.setValue('bannerShadow', checked)}
                      />
                      <Label htmlFor="bannerShadow">Exibir sombra</Label>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Urgency Tab */}
          <TabsContent value="urgency" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Barra de Urgência</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="urgencyEnabled"
                    checked={form.watch('urgencyEnabled')}
                    onCheckedChange={(checked) => form.setValue('urgencyEnabled', checked)}
                  />
                  <Label htmlFor="urgencyEnabled">Exibir barra de urgência</Label>
                </div>

                {form.watch('urgencyEnabled') && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="urgencyMessage">Mensagem</Label>
                      <Input
                        id="urgencyMessage"
                        {...form.register('urgencyMessage')}
                        placeholder="Esta oferta se encerra em:"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="urgencyEndTime">Data/Hora de término</Label>
                      <Input
                        id="urgencyEndTime"
                        type="datetime-local"
                        {...form.register('urgencyEndTime')}
                      />
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="urgencyBackgroundColor">Cor de fundo</Label>
                        <Select
                          value={form.watch('urgencyBackgroundColor')}
                          onValueChange={(value) => form.setValue('urgencyBackgroundColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-red-50">Vermelho claro</SelectItem>
                            <SelectItem value="bg-orange-50">Laranja claro</SelectItem>
                            <SelectItem value="bg-yellow-50">Amarelo claro</SelectItem>
                            <SelectItem value="bg-blue-50">Azul claro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="urgencyTextColor">Cor do texto</Label>
                        <Select
                          value={form.watch('urgencyTextColor')}
                          onValueChange={(value) => form.setValue('urgencyTextColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text-white">Branco</SelectItem>
                            <SelectItem value="text-black">Preto</SelectItem>
                            <SelectItem value="text-red-800">Vermelho escuro</SelectItem>
                            <SelectItem value="text-orange-800">Laranja escuro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="urgencyAccentColor">Cor de destaque</Label>
                        <Select
                          value={form.watch('urgencyAccentColor')}
                          onValueChange={(value) => form.setValue('urgencyAccentColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-red-600">Vermelho</SelectItem>
                            <SelectItem value="bg-orange-600">Laranja</SelectItem>
                            <SelectItem value="bg-yellow-600">Amarelo</SelectItem>
                            <SelectItem value="bg-blue-600">Azul</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trust Badges Tab */}
          <TabsContent value="trust" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Badges de Confiança</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="trustBadgesEnabled"
                    checked={form.watch('trustBadgesEnabled')}
                    onCheckedChange={(checked) => form.setValue('trustBadgesEnabled', checked)}
                  />
                  <Label htmlFor="trustBadgesEnabled">Exibir badges de confiança</Label>
                </div>

                {form.watch('trustBadgesEnabled') && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="trustBadgesLayout">Layout</Label>
                        <Select
                          value={form.watch('trustBadgesLayout')}
                          onValueChange={(value) => form.setValue('trustBadgesLayout', value as any)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="horizontal">Horizontal</SelectItem>
                            <SelectItem value="vertical">Vertical</SelectItem>
                            <SelectItem value="grid">Grid</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="trustBadgesShowDescriptions"
                          checked={form.watch('trustBadgesShowDescriptions')}
                          onCheckedChange={(checked) => form.setValue('trustBadgesShowDescriptions', checked)}
                        />
                        <Label htmlFor="trustBadgesShowDescriptions">Mostrar descrições</Label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Badges</Label>
                        <Button type="button" onClick={addTrustBadge} size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Adicionar Badge
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {form.watch('trustBadges').map((badge, index) => (
                          <div key={badge.id} className="flex items-center gap-3 p-3 border rounded-lg">
                            <div className="flex-1 grid grid-cols-2 gap-3">
                              <Input
                                placeholder="Título do badge"
                                value={badge.title}
                                onChange={(e) => {
                                  const badges = form.getValues('trustBadges');
                                  badges[index].title = e.target.value;
                                  form.setValue('trustBadges', badges);
                                }}
                              />
                              <Input
                                placeholder="Subtítulo do badge"
                                value={badge.subtitle}
                                onChange={(e) => {
                                  const badges = form.getValues('trustBadges');
                                  badges[index].subtitle = e.target.value;
                                  form.setValue('trustBadges', badges);
                                }}
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <Select
                                value={badge.icon}
                                onValueChange={(value) => {
                                  const badges = form.getValues('trustBadges');
                                  badges[index].icon = value as any;
                                  form.setValue('trustBadges', badges);
                                }}
                              >
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="shield">Escudo</SelectItem>
                                  <SelectItem value="lock">Cadeado</SelectItem>
                                  <SelectItem value="mail">Email</SelectItem>
                                  <SelectItem value="check">Check</SelectItem>
                                  <SelectItem value="star">Estrela</SelectItem>
                                  <SelectItem value="award">Prêmio</SelectItem>
                                  <SelectItem value="truck">Caminhão</SelectItem>
                                  <SelectItem value="credit-card">Cartão</SelectItem>
                                </SelectContent>
                              </Select>
                              <Switch
                                checked={badge.enabled}
                                onCheckedChange={(checked) => {
                                  const badges = form.getValues('trustBadges');
                                  badges[index].enabled = checked;
                                  form.setValue('trustBadges', badges);
                                }}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeTrustBadge(index)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Scarcity Tab */}
          <TabsContent value="scarcity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Indicador de Escassez</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="scarcityEnabled"
                    checked={form.watch('scarcityEnabled')}
                    onCheckedChange={(checked) => form.setValue('scarcityEnabled', checked)}
                  />
                  <Label htmlFor="scarcityEnabled">Exibir indicador de escassez</Label>
                </div>

                {form.watch('scarcityEnabled') && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="scarcityTotalStock">Estoque total</Label>
                        <Input
                          id="scarcityTotalStock"
                          type="number"
                          {...form.register('scarcityTotalStock', { valueAsNumber: true })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="scarcitySoldCount">Vendidos</Label>
                        <Input
                          id="scarcitySoldCount"
                          type="number"
                          {...form.register('scarcitySoldCount', { valueAsNumber: true })}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="scarcityMessage">Mensagem</Label>
                      <Input
                        id="scarcityMessage"
                        {...form.register('scarcityMessage')}
                        placeholder="Apenas {remaining} vagas restantes!"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="scarcityVariant">Variante</Label>
                        <Select
                          value={form.watch('scarcityVariant')}
                          onValueChange={(value) => form.setValue('scarcityVariant', value as any)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="warning">Aviso</SelectItem>
                            <SelectItem value="danger">Perigo</SelectItem>
                            <SelectItem value="info">Informação</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="scarcityShowIcon"
                          checked={form.watch('scarcityShowIcon')}
                          onCheckedChange={(checked) => form.setValue('scarcityShowIcon', checked)}
                        />
                        <Label htmlFor="scarcityShowIcon">Mostrar ícone</Label>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Header Tab */}
          <TabsContent value="header" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Header do Checkout</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="headerShowLogo"
                    checked={form.watch('headerShowLogo')}
                    onCheckedChange={(checked) => form.setValue('headerShowLogo', checked)}
                  />
                  <Label htmlFor="headerShowLogo">Exibir logo da empresa</Label>
                </div>

                {form.watch('headerShowLogo') && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="headerCompanyName">Nome da empresa</Label>
                      <Input
                        id="headerCompanyName"
                        {...form.register('headerCompanyName')}
                        placeholder="Nome da sua empresa"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="headerLogoUrl">URL do logo (opcional)</Label>
                      <Input
                        id="headerLogoUrl"
                        {...form.register('headerLogoUrl')}
                        placeholder="https://exemplo.com/logo.png"
                      />
                      <p className="text-xs text-muted-foreground">
                        Se não informado, será usado o logo padrão do SupGateway
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Testimonials Tab */}
          <TabsContent value="testimonials" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Depoimentos</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="testimonialsEnabled"
                    checked={form.watch('testimonialsEnabled')}
                    onCheckedChange={(checked) => form.setValue('testimonialsEnabled', checked)}
                  />
                  <Label htmlFor="testimonialsEnabled">Exibir depoimentos</Label>
                </div>

                {form.watch('testimonialsEnabled') && (
                  <>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="testimonialsMaxTestimonials">Máximo de depoimentos</Label>
                        <Input
                          id="testimonialsMaxTestimonials"
                          type="number"
                          {...form.register('testimonialsMaxTestimonials', { valueAsNumber: true })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="testimonialsAutoPlayInterval">Intervalo (ms)</Label>
                        <Input
                          id="testimonialsAutoPlayInterval"
                          type="number"
                          {...form.register('testimonialsAutoPlayInterval', { valueAsNumber: true })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Configurações</Label>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="testimonialsAutoPlay"
                              checked={form.watch('testimonialsAutoPlay')}
                              onCheckedChange={(checked) => form.setValue('testimonialsAutoPlay', checked)}
                            />
                            <Label htmlFor="testimonialsAutoPlay" className="text-sm">Auto-play</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="testimonialsShowControls"
                              checked={form.watch('testimonialsShowControls')}
                              onCheckedChange={(checked) => form.setValue('testimonialsShowControls', checked)}
                            />
                            <Label htmlFor="testimonialsShowControls" className="text-sm">Controles</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="testimonialsShowStars"
                              checked={form.watch('testimonialsShowStars')}
                              onCheckedChange={(checked) => form.setValue('testimonialsShowStars', checked)}
                            />
                            <Label htmlFor="testimonialsShowStars" className="text-sm">Estrelas</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="testimonialsShowAvatars"
                              checked={form.watch('testimonialsShowAvatars')}
                              onCheckedChange={(checked) => form.setValue('testimonialsShowAvatars', checked)}
                            />
                            <Label htmlFor="testimonialsShowAvatars" className="text-sm">Avatares</Label>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Depoimentos</Label>
                        <Button type="button" onClick={addTestimonial} size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Adicionar Depoimento
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {form.watch('testimonials').map((testimonial, index) => (
                          <div key={testimonial.id} className="p-4 border rounded-lg space-y-3">
                            <div className="grid grid-cols-2 gap-3">
                              <Input
                                placeholder="Nome do cliente"
                                value={testimonial.name}
                                onChange={(e) => {
                                  const testimonials = form.getValues('testimonials');
                                  testimonials[index].name = e.target.value;
                                  form.setValue('testimonials', testimonials);
                                }}
                              />
                              <Input
                                placeholder="Localização"
                                value={testimonial.location || ''}
                                onChange={(e) => {
                                  const testimonials = form.getValues('testimonials');
                                  testimonials[index].location = e.target.value;
                                  form.setValue('testimonials', testimonials);
                                }}
                              />
                            </div>
                            <Textarea
                              placeholder="Comentário do cliente"
                              value={testimonial.comment}
                              onChange={(e) => {
                                const testimonials = form.getValues('testimonials');
                                testimonials[index].comment = e.target.value;
                                form.setValue('testimonials', testimonials);
                              }}
                            />
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="flex items-center gap-2">
                                  <Label className="text-sm">Avaliação:</Label>
                                  <Select
                                    value={testimonial.rating.toString()}
                                    onValueChange={(value) => {
                                      const testimonials = form.getValues('testimonials');
                                      testimonials[index].rating = parseInt(value);
                                      form.setValue('testimonials', testimonials);
                                    }}
                                  >
                                    <SelectTrigger className="w-20">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="1">1 ⭐</SelectItem>
                                      <SelectItem value="2">2 ⭐</SelectItem>
                                      <SelectItem value="3">3 ⭐</SelectItem>
                                      <SelectItem value="4">4 ⭐</SelectItem>
                                      <SelectItem value="5">5 ⭐</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Switch
                                    checked={testimonial.verified}
                                    onCheckedChange={(checked) => {
                                      const testimonials = form.getValues('testimonials');
                                      testimonials[index].verified = checked;
                                      form.setValue('testimonials', testimonials);
                                    }}
                                  />
                                  <Label className="text-sm">Verificado</Label>
                                </div>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeTestimonial(index)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Sidebar Tab */}
          <TabsContent value="sidebar" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Banner Lateral</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="sidebarEnabled"
                    checked={form.watch('sidebarEnabled')}
                    onCheckedChange={(checked) => form.setValue('sidebarEnabled', checked)}
                  />
                  <Label htmlFor="sidebarEnabled">Exibir banner lateral</Label>
                </div>

                {form.watch('sidebarEnabled') && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="sidebarBannerUrl">URL da imagem (opcional)</Label>
                      <Input
                        id="sidebarBannerUrl"
                        {...form.register('sidebarBannerUrl')}
                        placeholder="https://exemplo.com/banner-lateral.jpg"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sidebarTitle">Título</Label>
                      <Input
                        id="sidebarTitle"
                        {...form.register('sidebarTitle')}
                        placeholder="Informações Importantes"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sidebarContent">Conteúdo</Label>
                      <Textarea
                        id="sidebarContent"
                        {...form.register('sidebarContent')}
                        placeholder="Adicione informações úteis para seus clientes aqui."
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sidebarBackgroundColor">Cor de fundo</Label>
                        <Select
                          value={form.watch('sidebarBackgroundColor')}
                          onValueChange={(value) => form.setValue('sidebarBackgroundColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-blue-50">Azul claro</SelectItem>
                            <SelectItem value="bg-green-50">Verde claro</SelectItem>
                            <SelectItem value="bg-yellow-50">Amarelo claro</SelectItem>
                            <SelectItem value="bg-red-50">Vermelho claro</SelectItem>
                            <SelectItem value="bg-gray-50">Cinza claro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="sidebarTextColor">Cor do texto</Label>
                        <Select
                          value={form.watch('sidebarTextColor')}
                          onValueChange={(value) => form.setValue('sidebarTextColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text-blue-800">Azul escuro</SelectItem>
                            <SelectItem value="text-green-800">Verde escuro</SelectItem>
                            <SelectItem value="text-yellow-800">Amarelo escuro</SelectItem>
                            <SelectItem value="text-red-800">Vermelho escuro</SelectItem>
                            <SelectItem value="text-gray-800">Cinza escuro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sidebarBorderColor">Cor da borda</Label>
                        <Select
                          value={form.watch('sidebarBorderColor')}
                          onValueChange={(value) => form.setValue('sidebarBorderColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="border-blue-200">Azul</SelectItem>
                            <SelectItem value="border-green-200">Verde</SelectItem>
                            <SelectItem value="border-yellow-200">Amarelo</SelectItem>
                            <SelectItem value="border-red-200">Vermelho</SelectItem>
                            <SelectItem value="border-gray-200">Cinza</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="sidebarBorderRadius">Bordas arredondadas</Label>
                        <Select
                          value={form.watch('sidebarBorderRadius')}
                          onValueChange={(value) => form.setValue('sidebarBorderRadius', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="rounded-none">Nenhuma</SelectItem>
                            <SelectItem value="rounded-sm">Pequena</SelectItem>
                            <SelectItem value="rounded">Média</SelectItem>
                            <SelectItem value="rounded-lg">Grande</SelectItem>
                            <SelectItem value="rounded-xl">Extra Grande</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="sidebarShadow"
                        checked={form.watch('sidebarShadow')}
                        onCheckedChange={(checked) => form.setValue('sidebarShadow', checked)}
                      />
                      <Label htmlFor="sidebarShadow">Exibir sombra</Label>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  );
}
