'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Separator } from '@ui/components/separator';
import {
  ArrowLeft,
  Save,
  Eye,
  Smartphone,
  Monitor,
  Palette,
  Settings,
  Zap,
  CheckCircle,
  Clock
} from 'lucide-react';
import { cn } from '@ui/lib';

// Schema simplificado para o builder
const checkoutBuilderSchema = z.object({
  // Template
  template: z.enum(['minimal', 'conversion', 'professional']).default('conversion'),

  // Layout básico
  layout: z.object({
    header: z.object({
      showLogo: z.boolean().default(true),
      logoUrl: z.string().optional(),
      companyName: z.string().default('Sua Empresa'),
    }),
    banner: z.object({
      enabled: z.boolean().default(true),
      url: z.string().optional(),
      height: z.string().default('200px'),
      shadow: z.boolean().default(true),
    }),
    form: z.object({
      style: z.enum(['default', 'minimal', 'modern']).default('default'),
      showProgress: z.boolean().default(true),
    }),
    summary: z.object({
      position: z.enum(['right', 'bottom']).default('right'),
      showConversionElements: z.boolean().default(true),
    }),
  }),

  // Conversion Elements
  conversion: z.object({
    urgency: z.object({
      enabled: z.boolean().default(false),
      message: z.string().default('Esta oferta se encerra em:'),
      endTime: z.string().optional(),
      backgroundColor: z.string().default('#fef2f2'),
      textColor: z.string().default('#991b1b'),
      accentColor: z.string().default('#dc2626'),
    }),
    scarcity: z.object({
      enabled: z.boolean().default(false),
      totalStock: z.number().default(100),
      soldCount: z.number().default(0),
      message: z.string().default('Apenas {remaining} vagas restantes!'),
      variant: z.enum(['warning', 'danger', 'info']).default('warning'),
      showIcon: z.boolean().default(true),
    }),
    trustBadges: z.object({
      enabled: z.boolean().default(true),
      layout: z.enum(['horizontal', 'vertical', 'grid']).default('horizontal'),
      showDescriptions: z.boolean().default(true),
      badges: z.array(z.object({
        id: z.string(),
        title: z.string(),
        subtitle: z.string(),
        icon: z.string(),
        enabled: z.boolean(),
      })).default([]),
    }),
    testimonials: z.object({
      enabled: z.boolean().default(true),
      maxTestimonials: z.number().default(3),
      autoPlay: z.boolean().default(true),
      autoPlayInterval: z.number().default(5000),
      showControls: z.boolean().default(true),
      showStars: z.boolean().default(true),
      showAvatars: z.boolean().default(true),
      testimonials: z.array(z.object({
        id: z.string(),
        name: z.string(),
        rating: z.number().min(1).max(5),
        comment: z.string(),
        avatar: z.string().optional(),
        location: z.string().optional(),
        verified: z.boolean().default(false),
      })).default([]),
    }),
  }),

  // Style
  style: z.object({
    primaryColor: z.string().default('#3b82f6'),
    secondaryColor: z.string().default('#64748b'),
    backgroundColor: z.string().default('#ffffff'),
    textColor: z.string().default('#1f2937'),
    borderRadius: z.string().default('rounded-lg'),
    shadow: z.boolean().default(true),
  }),

  // Advanced
  advanced: z.object({
    customCSS: z.string().optional(),
    customJS: z.string().optional(),
    trackingCode: z.string().optional(),
  }),
});

type CheckoutBuilderData = z.infer<typeof checkoutBuilderSchema>;

interface CheckoutBuilderProps {
  productId: string;
  organizationId: string;
  initialSettings?: any;
  onSave: (settings: any) => Promise<void>;
  onBack: () => void;
}

// Templates pré-configurados
const templates = {
  minimal: {
    name: 'Minimalista',
    description: 'Design limpo e focado na conversão',
    icon: '🎨',
    settings: {
      layout: {
        header: { showLogo: false, companyName: '' },
        banner: { enabled: false },
        form: { style: 'minimal', showProgress: false },
        summary: { position: 'right', showConversionElements: false },
      },
      conversion: {
        urgency: { enabled: false },
        scarcity: { enabled: false },
        trustBadges: { enabled: true, layout: 'vertical' },
        testimonials: { enabled: false },
      },
      style: {
        primaryColor: '#000000',
        secondaryColor: '#6b7280',
        backgroundColor: '#ffffff',
        textColor: '#111827',
        borderRadius: 'rounded-none',
        shadow: false,
      },
    },
  },
  conversion: {
    name: 'Conversão',
    description: 'Otimizado para máxima conversão (Recomendado)',
    icon: '🚀',
    settings: {
      layout: {
        header: { showLogo: true, companyName: 'Sua Empresa' },
        banner: { enabled: true, height: '250px', shadow: true },
        form: { style: 'modern', showProgress: true },
        summary: { position: 'right', showConversionElements: true },
      },
      conversion: {
        urgency: { enabled: true },
        scarcity: { enabled: true },
        trustBadges: { enabled: true, layout: 'horizontal' },
        testimonials: { enabled: true },
      },
      style: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        borderRadius: 'rounded-lg',
        shadow: true,
      },
    },
  },
  professional: {
    name: 'Profissional',
    description: 'Elegante e corporativo',
    icon: '💼',
    settings: {
      layout: {
        header: { showLogo: true, companyName: 'Sua Empresa' },
        banner: { enabled: true, height: '200px', shadow: true },
        form: { style: 'default', showProgress: true },
        summary: { position: 'right', showConversionElements: true },
      },
      conversion: {
        urgency: { enabled: false },
        scarcity: { enabled: false },
        trustBadges: { enabled: true, layout: 'grid' },
        testimonials: { enabled: true },
      },
      style: {
        primaryColor: '#1f2937',
        secondaryColor: '#6b7280',
        backgroundColor: '#ffffff',
        textColor: '#111827',
        borderRadius: 'rounded-lg',
        shadow: true,
      },
    },
  },
};

export function CheckoutBuilder({
  productId,
  organizationId,
  initialSettings = {},
  onSave,
  onBack,
}: CheckoutBuilderProps) {
  const [currentSection, setCurrentSection] = useState('templates');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'mobile'>('desktop');
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const form = useForm<CheckoutBuilderData>({
    resolver: zodResolver(checkoutBuilderSchema),
    defaultValues: {
      template: 'conversion',
      layout: {
        header: {
          showLogo: true,
          logoUrl: '',
          companyName: 'Sua Empresa',
        },
        banner: {
          enabled: true,
          url: '',
          height: '200px',
          shadow: true,
        },
        form: {
          style: 'default',
          showProgress: true,
        },
        summary: {
          position: 'right',
          showConversionElements: true,
        },
      },
      conversion: {
        urgency: {
          enabled: false,
          message: 'Esta oferta se encerra em:',
          endTime: '',
          backgroundColor: '#fef2f2',
          textColor: '#991b1b',
          accentColor: '#dc2626',
        },
        scarcity: {
          enabled: false,
          totalStock: 100,
          soldCount: 0,
          message: 'Apenas {remaining} vagas restantes!',
          variant: 'warning',
          showIcon: true,
        },
        trustBadges: {
          enabled: true,
          layout: 'horizontal',
          showDescriptions: true,
          badges: [
            {
              id: 'privacy',
              title: 'Privacidade',
              subtitle: 'Sua informação 100% segura',
              icon: 'shield',
              enabled: true,
            },
            {
              id: 'secure-purchase',
              title: 'Compra segura',
              subtitle: 'Ambiente seguro e autenticado',
              icon: 'lock',
              enabled: true,
            },
          ],
        },
        testimonials: {
          enabled: true,
          maxTestimonials: 3,
          autoPlay: true,
          autoPlayInterval: 5000,
          showControls: true,
          showStars: true,
          showAvatars: true,
          testimonials: [
            {
              id: '1',
              name: 'Maria Silva',
              rating: 5,
              comment: 'Produto excelente! Superou minhas expectativas.',
              location: 'São Paulo, SP',
              verified: true,
            },
          ],
        },
      },
      style: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        borderRadius: 'rounded-lg',
        shadow: true,
      },
      advanced: {
        customCSS: '',
        customJS: '',
        trackingCode: '',
      },
    },
  });

  // Auto-save functionality
  useEffect(() => {
    const subscription = form.watch((value) => {
      const timeoutId = setTimeout(async () => {
        try {
          await onSave(value);
          setLastSaved(new Date());
        } catch (error) {
          console.error('Auto-save failed:', error);
        }
      }, 2000);

      return () => clearTimeout(timeoutId);
    });

    return () => subscription.unsubscribe();
  }, [form.watch, onSave]);

  const handleTemplateSelect = (templateKey: keyof typeof templates) => {
    const template = templates[templateKey];
    form.reset({
      ...form.getValues(),
      template: templateKey,
      ...template.settings,
    });
    setCurrentSection('layout');
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const data = form.getValues();
      await onSave(data);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Save failed:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const sections = [
    { id: 'templates', name: 'Templates', icon: Zap },
    { id: 'layout', name: 'Layout', icon: Settings },
    { id: 'conversion', name: 'Conversão', icon: CheckCircle },
    { id: 'style', name: 'Estilo', icon: Palette },
    { id: 'advanced', name: 'Avançado', icon: Settings },
  ];

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar para Lista
            </Button>
            <div>
              <h1 className="text-xl font-semibold">Checkout Builder</h1>
              <p className="text-sm text-gray-600">
                Configure seu checkout de forma visual e intuitiva
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {lastSaved && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                Salvo às {lastSaved.toLocaleTimeString()}
              </div>
            )}

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPreviewDevice(previewDevice === 'desktop' ? 'mobile' : 'desktop')}
              >
                {previewDevice === 'desktop' ? (
                  <Monitor className="h-4 w-4" />
                ) : (
                  <Smartphone className="h-4 w-4" />
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {isPreviewMode ? 'Editar' : 'Preview'}
              </Button>

              <Button onClick={handleSave} disabled={isSaving}>
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-semibold text-sm text-gray-900 mb-3">Configurações</h2>
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setCurrentSection(section.id)}
                  className={cn(
                    'w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors',
                    currentSection === section.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50'
                  )}
                >
                  <section.icon className="h-4 w-4" />
                  {section.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="flex-1 p-4 overflow-auto">
            {/* Section Content */}
            {currentSection === 'templates' && (
              <div className="space-y-4">
                <h3 className="font-medium text-sm text-gray-900">Escolha um Template</h3>
                <div className="space-y-3">
                  {Object.entries(templates).map(([key, template]) => (
                    <Card
                      key={key}
                      className={cn(
                        'cursor-pointer transition-all hover:shadow-md',
                        form.watch('template') === key && 'ring-2 ring-blue-500'
                      )}
                      onClick={() => handleTemplateSelect(key as keyof typeof templates)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className="text-2xl">{template.icon}</div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-sm">{template.name}</h4>
                              {form.watch('template') === key && (
                                <Badge variant="secondary" className="text-xs">
                                  Ativo
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-600 mt-1">
                              {template.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Other sections will be implemented in separate components */}
            {currentSection !== 'templates' && (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  {(() => {
                    const section = sections.find(s => s.id === currentSection);
                    const IconComponent = section?.icon;
                    return IconComponent ? <IconComponent className="h-8 w-8 mx-auto" /> : null;
                  })()}
                </div>
                <p className="text-sm text-gray-600">
                  Configurações de {sections.find(s => s.id === currentSection)?.name} em breve
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Configuration Panel */}
          {!isPreviewMode && (
            <div className="flex-1 p-6 overflow-auto">
              <div className="max-w-4xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {(() => {
                        const section = sections.find(s => s.id === currentSection);
                        const IconComponent = section?.icon;
                        return IconComponent ? <IconComponent className="h-5 w-5" /> : null;
                      })()}
                      {sections.find(s => s.id === currentSection)?.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-4">
                        <Settings className="h-12 w-12 mx-auto" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Configurações em Desenvolvimento
                      </h3>
                      <p className="text-gray-600">
                        Esta seção será implementada em breve com controles visuais intuitivos.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Preview Panel */}
          <div className={cn(
            'flex-1 bg-gray-100 p-6 overflow-auto',
            isPreviewMode ? 'block' : 'hidden lg:block'
          )}>
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="p-4 border-b bg-gray-50">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-sm text-gray-900">Preview do Checkout</h3>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {previewDevice === 'desktop' ? 'Desktop' : 'Mobile'}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {form.watch('template')}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className={cn(
                  'p-6',
                  previewDevice === 'mobile' && 'max-w-sm mx-auto'
                )}>
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <Eye className="h-12 w-12 mx-auto" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Preview em Desenvolvimento
                    </h3>
                    <p className="text-gray-600">
                      O preview visual do checkout será implementado em breve.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
