'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Badge } from '@ui/components/badge';
import { Switch } from '@ui/components/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Shield,
  CheckCircle,
  Lock,
  Truck,
  Award,
  Heart,
  Star,
  Zap,
  Clock,
  Mail
} from 'lucide-react';

interface GuaranteeCard {
  id: string;
  title: string;
  description: string;
  icon: 'shield' | 'check' | 'lock' | 'truck' | 'award' | 'heart' | 'star' | 'zap' | 'clock' | 'mail' | 'custom';
  customIcon?: string;
  enabled: boolean;
  order: number;
}

interface GuaranteeCardsEditorProps {
  cards: GuaranteeCard[];
  onCardsChange: (cards: GuaranteeCard[]) => void;
  maxCards?: number;
}

const iconOptions = [
  { value: 'shield', label: 'Escudo', icon: Shield },
  { value: 'check', label: 'Check', icon: CheckCircle },
  { value: 'lock', label: 'Cadeado', icon: Lock },
  { value: 'truck', label: 'Caminhão', icon: Truck },
  { value: 'award', label: 'Prêmio', icon: Award },
  { value: 'heart', label: 'Coração', icon: Heart },
  { value: 'star', label: 'Estrela', icon: Star },
  { value: 'zap', label: 'Raio', icon: Zap },
  { value: 'clock', label: 'Relógio', icon: Clock },
  { value: 'mail', label: 'Email', icon: Mail },
  { value: 'custom', label: 'Personalizado', icon: null },
];

export function GuaranteeCardsEditor({
  cards,
  onCardsChange,
  maxCards = 6
}: GuaranteeCardsEditorProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newCard, setNewCard] = useState<Partial<GuaranteeCard>>({
    title: '',
    description: '',
    icon: 'shield',
    enabled: true,
    order: cards.length
  });

  const addCard = () => {
    if (cards.length >= maxCards) return;

    const card: GuaranteeCard = {
      id: Date.now().toString(),
      title: newCard.title || '',
      description: newCard.description || '',
      icon: newCard.icon || 'shield',
      customIcon: newCard.customIcon,
      enabled: newCard.enabled || true,
      order: newCard.order || cards.length
    };

    onCardsChange([...cards, card]);
    setNewCard({
      title: '',
      description: '',
      icon: 'shield',
      enabled: true,
      order: cards.length + 1
    });
    setIsAddingNew(false);
  };

  const updateCard = (id: string, updates: Partial<GuaranteeCard>) => {
    const updated = cards.map(c =>
      c.id === id ? { ...c, ...updates } : c
    );
    onCardsChange(updated);
  };

  const deleteCard = (id: string) => {
    const updated = cards.filter(c => c.id !== id);
    onCardsChange(updated);
  };

  const startEditing = (card: GuaranteeCard) => {
    setEditingId(card.id);
    setNewCard(card);
  };

  const saveEdit = () => {
    if (editingId) {
      updateCard(editingId, newCard);
      setEditingId(null);
      setNewCard({
        title: '',
        description: '',
        icon: 'shield',
        enabled: true,
        order: cards.length
      });
    }
  };

  const cancelEdit = () => {
    setEditingId(null);
    setIsAddingNew(false);
    setNewCard({
      title: '',
      description: '',
      icon: 'shield',
      enabled: true,
      order: cards.length
    });
  };

  const toggleCardEnabled = (id: string) => {
    const card = cards.find(c => c.id === id);
    if (card) {
      updateCard(id, { enabled: !card.enabled });
    }
  };

  const renderIcon = (iconType: string, customIcon?: string) => {
    if (iconType === 'custom' && customIcon) {
      return <span className="text-2xl">{customIcon}</span>;
    }

    const iconOption = iconOptions.find(opt => opt.value === iconType);
    if (iconOption && iconOption.icon) {
      const IconComponent = iconOption.icon;
      return <IconComponent className="h-6 w-6" />;
    }

    return <Shield className="h-6 w-6" />;
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Cards de Garantia</h3>
          <p className="text-sm text-muted-foreground">
            {cards.filter(c => c.enabled).length} de {maxCards} cards ativos
          </p>
        </div>
        <Button
          onClick={() => setIsAddingNew(true)}
          disabled={cards.length >= maxCards}
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Card
        </Button>
      </div>

      {/* Lista de Cards */}
      <div className="space-y-3">
        {cards.map((card) => (
          <Card key={card.id} className="relative">
            <CardContent className="p-4">
              {editingId === card.id ? (
                // Modo de Edição
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Editando Card</h4>
                    <div className="flex gap-2">
                      <Button size="sm" onClick={saveEdit}>
                        <Save className="h-4 w-4 mr-1" />
                        Salvar
                      </Button>
                      <Button size="sm" variant="outline" onClick={cancelEdit}>
                        <X className="h-4 w-4 mr-1" />
                        Cancelar
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="edit-title">Título</Label>
                      <Input
                        id="edit-title"
                        value={newCard.title || ''}
                        onChange={(e) => setNewCard(prev => ({ ...prev, title: e.target.value }))}
                        placeholder="Ex: 100% Seguro"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-icon">Ícone</Label>
                      <Select
                        value={newCard.icon || 'shield'}
                        onValueChange={(value) => setNewCard(prev => ({ ...prev, icon: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {iconOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                {option.icon ? (
                                  <option.icon className="h-4 w-4" />
                                ) : (
                                  <span>🎨</span>
                                )}
                                {option.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {newCard.icon === 'custom' && (
                    <div>
                      <Label htmlFor="edit-custom-icon">Ícone Personalizado (Emoji)</Label>
                      <Input
                        id="edit-custom-icon"
                        value={newCard.customIcon || ''}
                        onChange={(e) => setNewCard(prev => ({ ...prev, customIcon: e.target.value }))}
                        placeholder="Ex: 🛡️, 🔒, ⭐"
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="edit-description">Descrição</Label>
                    <Textarea
                      id="edit-description"
                      value={newCard.description || ''}
                      onChange={(e) => setNewCard(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Ex: Pagamentos protegidos com criptografia de ponta a ponta"
                      rows={2}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={newCard.enabled || false}
                        onCheckedChange={(checked) => setNewCard(prev => ({ ...prev, enabled: checked }))}
                      />
                      <Label>Ativo</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Label htmlFor="edit-order">Ordem</Label>
                      <Input
                        id="edit-order"
                        type="number"
                        value={newCard.order || 0}
                        onChange={(e) => setNewCard(prev => ({ ...prev, order: parseInt(e.target.value) }))}
                        className="w-20"
                      />
                    </div>
                  </div>
                </div>
              ) : (
                // Modo de Visualização
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        {renderIcon(card.icon, card.customIcon)}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold">{card.title}</h4>
                          {card.enabled ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              Ativo
                            </Badge>
                          ) : (
                            <Badge variant="outline">
                              Inativo
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {card.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => startEditing(card)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteCard(card.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {/* Formulário de Novo Card */}
        {isAddingNew && (
          <Card className="border-dashed">
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Novo Card de Garantia</h4>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={addCard}>
                      <Save className="h-4 w-4 mr-1" />
                      Adicionar
                    </Button>
                    <Button size="sm" variant="outline" onClick={cancelEdit}>
                      <X className="h-4 w-4 mr-1" />
                      Cancelar
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="new-title">Título *</Label>
                    <Input
                      id="new-title"
                      value={newCard.title || ''}
                      onChange={(e) => setNewCard(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Ex: 100% Seguro"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-icon">Ícone</Label>
                    <Select
                      value={newCard.icon || 'shield'}
                      onValueChange={(value) => setNewCard(prev => ({ ...prev, icon: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {iconOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              {option.icon ? (
                                <option.icon className="h-4 w-4" />
                              ) : (
                                <span>🎨</span>
                              )}
                              {option.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {newCard.icon === 'custom' && (
                  <div>
                    <Label htmlFor="new-custom-icon">Ícone Personalizado (Emoji)</Label>
                    <Input
                      id="new-custom-icon"
                      value={newCard.customIcon || ''}
                      onChange={(e) => setNewCard(prev => ({ ...prev, customIcon: e.target.value }))}
                      placeholder="Ex: 🛡️, 🔒, ⭐"
                    />
                  </div>
                )}

                <div>
                  <Label htmlFor="new-description">Descrição *</Label>
                  <Textarea
                    id="new-description"
                    value={newCard.description || ''}
                    onChange={(e) => setNewCard(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Ex: Pagamentos protegidos com criptografia de ponta a ponta"
                    rows={2}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={newCard.enabled || false}
                      onCheckedChange={(checked) => setNewCard(prev => ({ ...prev, enabled: checked }))}
                    />
                    <Label>Ativo</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Label htmlFor="new-order">Ordem</Label>
                    <Input
                      id="new-order"
                      type="number"
                      value={newCard.order || 0}
                      onChange={(e) => setNewCard(prev => ({ ...prev, order: parseInt(e.target.value) }))}
                      className="w-20"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
