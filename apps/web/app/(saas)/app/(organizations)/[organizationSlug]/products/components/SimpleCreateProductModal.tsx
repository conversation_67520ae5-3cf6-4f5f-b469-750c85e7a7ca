"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@ui/components/dialog";
import { Badge } from "@ui/components/badge";
import { Card, CardContent } from "@ui/components/card";
import {
  PlusIcon,
  SparklesIcon,
  GraduationCapIcon,
  BookOpenIcon,
  UsersIcon,
  PlayCircleIcon,
  PackageIcon,
  CheckIcon
} from "lucide-react";
import { toast } from "sonner";
import { useCreateProduct } from "@saas/products/hooks/useProductsApi";
import { cn } from "@ui/lib";

interface CreateProductModalProps {
  organizationId: string;
  organizationSlug?: string;
  trigger?: React.ReactNode;
}

export function SimpleCreateProductModal({
  organizationId,
  organizationSlug,
  trigger
}: CreateProductModalProps) {
  const [open, setOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const router = useRouter();
  const createProduct = useCreateProduct();

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    type: "COURSE" as const,
  });

  // Gerar slug automaticamente baseado no nome
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "") // Remove acentos
      .replace(/[^a-z0-9\s-]/g, "") // Remove caracteres especiais
      .replace(/\s+/g, "-") // Substitui espaços por hífens
      .replace(/-+/g, "-") // Remove hífens duplicados
      .trim();
  };

  const productTypes = [
    {
      value: "COURSE",
      label: "Curso Online",
      icon: GraduationCapIcon,
      description: "Conteúdo educacional estruturado com módulos e lições",
      features: ["Módulos organizados", "Certificado de conclusão", "Acesso vitalício"],
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-950/20",
      borderColor: "border-blue-200 dark:border-blue-800"
    },
    {
      value: "EBOOK",
      label: "E-book Digital",
      icon: BookOpenIcon,
      description: "Livro digital para download e leitura offline",
      features: ["Download instantâneo", "Formato PDF", "Acesso permanente"],
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50 dark:bg-green-950/20",
      borderColor: "border-green-200 dark:border-green-800"
    },
    {
      value: "MENTORSHIP",
      label: "Mentoria",
      icon: UsersIcon,
      description: "Acompanhamento personalizado e orientação individual",
      features: ["Sessões 1:1", "Feedback personalizado", "Suporte direto"],
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50 dark:bg-purple-950/20",
      borderColor: "border-purple-200 dark:border-purple-800"
    },
    {
      value: "SUBSCRIPTION",
      label: "Assinatura",
      icon: PlayCircleIcon,
      description: "Acesso recorrente a conteúdo exclusivo e atualizações",
      features: ["Conteúdo mensal", "Atualizações regulares", "Cancelamento flexível"],
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-50 dark:bg-orange-950/20",
      borderColor: "border-orange-200 dark:border-orange-800"
    },
    {
      value: "BUNDLE",
      label: "Pacote Completo",
      icon: PackageIcon,
      description: "Combinação de múltiplos produtos com desconto especial",
      features: ["Múltiplos produtos", "Desconto especial", "Valor agregado"],
      color: "from-pink-500 to-pink-600",
      bgColor: "bg-pink-50 dark:bg-pink-950/20",
      borderColor: "border-pink-200 dark:border-pink-800"
    },
  ];

  const selectedType = productTypes.find(type => type.value === formData.type);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Nome do produto é obrigatório");
      return;
    }

    setIsCreating(true);

    try {
      const result = await createProduct.mutateAsync({
        organizationId,
        name: formData.name.trim(),
        slug: generateSlug(formData.name.trim()),
        description: formData.description.trim(),
        type: formData.type,
        priceCents: 0, // Começar gratuito
        currency: "BRL",
        language: "pt-BR",
        status: "DRAFT",
        visibility: "PRIVATE",
        shortDescription: formData.description.trim().substring(0, 100),
        gallery: [],
        tags: [],
        features: [],
        requirements: [],
        certificate: false,
        downloadable: false,
        checkoutType: "DEFAULT",
        settings: {},
      });

      toast.success("Produto criado com sucesso!");
      setOpen(false);

      // Redirecionar para a página do produto
      const productUrl = organizationSlug
        ? `/app/${organizationSlug}/products/${result.product.id}`
        : `/app/products/${result.product.id}`;

      router.push(productUrl);
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error("Erro ao criar produto. Tente novamente.");
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      type: "COURSE",
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      resetForm();
    }
  };


  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="shadow-sm shadow-primary/20 hover:shadow-primary/30 transition-all duration-200 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Produto
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary to-primary/80 rounded-xl flex items-center justify-center">
              <SparklesIcon className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold">Criar Novo Produto</DialogTitle>
              <DialogDescription className="text-base">
                Escolha o tipo de produto e configure as informações básicas
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          {/* Product Type Selection - Compact Cards */}
          <div className="space-y-4">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Qual tipo de produto você quer criar?</h3>
              <p className="text-muted-foreground">Escolha o formato que melhor se adequa ao seu conteúdo</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {productTypes.map((type) => {
                const Icon = type.icon;
                const isSelected = formData.type === type.value;

                return (
                  <Card
                    key={type.value}
                    className={cn(
                      "cursor-pointer transition-all duration-200 hover:shadow-md group",
                      isSelected
                        ? `border-2 ${type.borderColor} ${type.bgColor} shadow-md`
                        : "border hover:border-primary/50"
                    )}
                    onClick={() => setFormData(prev => ({ ...prev, type: type.value as any }))}
                  >
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200",
                            isSelected
                              ? `bg-gradient-to-r ${type.color} text-white shadow-lg`
                              : "bg-muted text-muted-foreground group-hover:bg-primary/10"
                          )}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-sm truncate">{type.label}</h4>
                            <p className="text-xs text-muted-foreground line-clamp-2">{type.description}</p>
                          </div>
                          {isSelected && (
                            <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                              <CheckIcon className="h-3 w-3 text-primary-foreground" />
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Nome do Produto *
              </Label>
              <Input
                id="name"
                placeholder="Ex: Curso de React Avançado"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                maxLength={60}
                required
                className="h-11 text-base"
              />
              <p className="text-xs text-muted-foreground">
                {formData.name.length}/60 caracteres
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Descrição
              </Label>
              <Textarea
                id="description"
                placeholder="Descreva brevemente seu produto e seus benefícios..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                maxLength={200}
                rows={3}
                className="text-base"
              />
              <p className="text-xs text-muted-foreground">
                {formData.description.length}/200 caracteres
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="flex items-center gap-2"
            >
              Cancelar
            </Button>

            <Button
              type="submit"
              disabled={!formData.name.trim() || isCreating}
              className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
            >
              {isCreating ? (
                "Criando..."
              ) : (
                <>
                  <SparklesIcon className="h-4 w-4" />
                  Criar Produto
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
