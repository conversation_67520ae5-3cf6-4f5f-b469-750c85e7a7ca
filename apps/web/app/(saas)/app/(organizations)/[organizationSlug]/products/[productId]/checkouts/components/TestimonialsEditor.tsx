'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Badge } from '@ui/components/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Switch } from '@ui/components/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import {
  Plus,
  Edit,
  Trash2,
  Star,
  Upload,
  X,
  CheckCircle,
  Save,
  Image as ImageIcon
} from 'lucide-react';
import { useFileUpload } from '@saas/products/hooks';

interface Testimonial {
  id: string;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  location?: string;
  verified?: boolean;
  company?: string;
  position?: string;
  source?: 'tiktok' | 'whatsapp' | 'instagram' | 'facebook' | 'youtube' | 'email' | 'website' | 'other';
  sourceUrl?: string;
}

interface TestimonialsEditorProps {
  testimonials: Testimonial[];
  onTestimonialsChange: (testimonials: Testimonial[]) => void;
  maxTestimonials?: number;
}

export function TestimonialsEditor({
  testimonials,
  onTestimonialsChange,
  maxTestimonials = 10
}: TestimonialsEditorProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newTestimonial, setNewTestimonial] = useState<Partial<Testimonial>>({
    name: '',
    rating: 5,
    comment: '',
    location: '',
    company: '',
    position: '',
    verified: true
  });

  const { uploadFile, isUploading } = useFileUpload({
    bucket: 'testimonialAvatars',
    onSuccess: (url) => {
      if (editingId) {
        updateTestimonial(editingId, { avatar: url });
      } else {
        setNewTestimonial(prev => ({ ...prev, avatar: url }));
      }
    },
    onError: (error) => {
      console.error('Erro no upload:', error);
    },
  });

  const addTestimonial = () => {
    if (testimonials.length >= maxTestimonials) return;

    const testimonial: Testimonial = {
      id: Date.now().toString(),
      name: newTestimonial.name || '',
      rating: newTestimonial.rating || 5,
      comment: newTestimonial.comment || '',
      avatar: newTestimonial.avatar,
      location: newTestimonial.location,
      verified: newTestimonial.verified || false,
      company: newTestimonial.company,
      position: newTestimonial.position
    };

    onTestimonialsChange([...testimonials, testimonial]);
    setNewTestimonial({
      name: '',
      rating: 5,
      comment: '',
      location: '',
      company: '',
      position: '',
      verified: true
    });
    setIsAddingNew(false);
  };

  const updateTestimonial = (id: string, updates: Partial<Testimonial>) => {
    const updated = testimonials.map(t =>
      t.id === id ? { ...t, ...updates } : t
    );
    onTestimonialsChange(updated);
  };

  const deleteTestimonial = (id: string) => {
    const updated = testimonials.filter(t => t.id !== id);
    onTestimonialsChange(updated);
  };

  const startEditing = (testimonial: Testimonial) => {
    setEditingId(testimonial.id);
    setNewTestimonial(testimonial);
  };

  const saveEdit = () => {
    if (editingId) {
      updateTestimonial(editingId, newTestimonial);
      setEditingId(null);
      setNewTestimonial({
        name: '',
        rating: 5,
        comment: '',
        location: '',
        company: '',
        position: '',
        verified: true
      });
    }
  };

  const cancelEdit = () => {
    setEditingId(null);
    setIsAddingNew(false);
    setNewTestimonial({
      name: '',
      rating: 5,
      comment: '',
      location: '',
      company: '',
      position: '',
      verified: true
    });
  };

  const handleAvatarUpload = (file: File) => {
    uploadFile(file);
  };

  const renderStars = (rating: number, editable: boolean = false) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={!editable}
            onClick={() => editable && setNewTestimonial(prev => ({ ...prev, rating: star }))}
            className={`${editable ? 'cursor-pointer' : 'cursor-default'}`}
          >
            <Star
              className={`h-4 w-4 ${
                star <= rating
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Depoimentos</h3>
          <p className="text-sm text-muted-foreground">
            {testimonials.length} de {maxTestimonials} depoimentos
          </p>
        </div>
        <Button
          onClick={() => setIsAddingNew(true)}
          disabled={testimonials.length >= maxTestimonials}
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Depoimento
        </Button>
      </div>

      {/* Lista de Depoimentos */}
      <div className="space-y-3">
        {testimonials.map((testimonial) => (
          <Card key={testimonial.id} className="relative">
            <CardContent className="p-4">
              {editingId === testimonial.id ? (
                // Modo de Edição
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Editando Depoimento</h4>
                    <div className="flex gap-2">
                      <Button size="sm" onClick={saveEdit}>
                        <Save className="h-4 w-4 mr-1" />
                        Salvar
                      </Button>
                      <Button size="sm" variant="outline" onClick={cancelEdit}>
                        <X className="h-4 w-4 mr-1" />
                        Cancelar
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="edit-name">Nome</Label>
                      <Input
                        id="edit-name"
                        value={newTestimonial.name || ''}
                        onChange={(e) => setNewTestimonial(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Nome do cliente"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-location">Localização</Label>
                      <Input
                        id="edit-location"
                        value={newTestimonial.location || ''}
                        onChange={(e) => setNewTestimonial(prev => ({ ...prev, location: e.target.value }))}
                        placeholder="Cidade, Estado"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-company">Empresa</Label>
                      <Input
                        id="edit-company"
                        value={newTestimonial.company || ''}
                        onChange={(e) => setNewTestimonial(prev => ({ ...prev, company: e.target.value }))}
                        placeholder="Nome da empresa"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-position">Cargo</Label>
                      <Input
                        id="edit-position"
                        value={newTestimonial.position || ''}
                        onChange={(e) => setNewTestimonial(prev => ({ ...prev, position: e.target.value }))}
                        placeholder="Cargo do cliente"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-source">Origem do Depoimento</Label>
                      <Select
                        value={newTestimonial.source || ''}
                        onValueChange={(value) => setNewTestimonial(prev => ({ ...prev, source: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a origem" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="tiktok">🎵 TikTok</SelectItem>
                          <SelectItem value="whatsapp">💬 WhatsApp</SelectItem>
                          <SelectItem value="instagram">📷 Instagram</SelectItem>
                          <SelectItem value="facebook">👥 Facebook</SelectItem>
                          <SelectItem value="youtube">📺 YouTube</SelectItem>
                          <SelectItem value="email">📧 Email</SelectItem>
                          <SelectItem value="website">🌐 Website</SelectItem>
                          <SelectItem value="other">💬 Outro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="edit-source-url">URL da Origem (opcional)</Label>
                      <Input
                        id="edit-source-url"
                        value={newTestimonial.sourceUrl || ''}
                        onChange={(e) => setNewTestimonial(prev => ({ ...prev, sourceUrl: e.target.value }))}
                        placeholder="https://..."
                      />
                    </div>
                  </div>

                  <div>
                    <Label>Comentário</Label>
                    <Textarea
                      value={newTestimonial.comment || ''}
                      onChange={(e) => setNewTestimonial(prev => ({ ...prev, comment: e.target.value }))}
                      placeholder="Depoimento do cliente"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div>
                        <Label>Avaliação</Label>
                        <div className="mt-1">
                          {renderStars(newTestimonial.rating || 5, true)}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={newTestimonial.verified || false}
                          onCheckedChange={(checked) => setNewTestimonial(prev => ({ ...prev, verified: checked }))}
                        />
                        <Label>Verificado</Label>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Label>Foto</Label>
                      <div className="flex items-center gap-2">
                        {newTestimonial.avatar && (
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={newTestimonial.avatar} />
                            <AvatarFallback>
                              {newTestimonial.name?.charAt(0) || 'U'}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => e.target.files?.[0] && handleAvatarUpload(e.target.files[0])}
                          className="hidden"
                          id="edit-avatar"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('edit-avatar')?.click()}
                          disabled={isUploading}
                        >
                          <Upload className="h-4 w-4 mr-1" />
                          {isUploading ? 'Enviando...' : 'Upload'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                // Modo de Visualização
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={testimonial.avatar} />
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {testimonial.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold">{testimonial.name}</h4>
                          {testimonial.verified && (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          )}
                          {testimonial.source && (
                            <Badge variant="outline" className="text-xs">
                              {testimonial.source === 'tiktok' && '🎵'}
                              {testimonial.source === 'whatsapp' && '💬'}
                              {testimonial.source === 'instagram' && '📷'}
                              {testimonial.source === 'facebook' && '👥'}
                              {testimonial.source === 'youtube' && '📺'}
                              {testimonial.source === 'email' && '📧'}
                              {testimonial.source === 'website' && '🌐'}
                              {testimonial.source === 'other' && '💬'}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {testimonial.position && `${testimonial.position}${testimonial.company ? ` na ${testimonial.company}` : ''}`}
                        </div>
                        {testimonial.location && (
                          <div className="text-xs text-muted-foreground">
                            {testimonial.location}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => startEditing(testimonial)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteTestimonial(testimonial.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {renderStars(testimonial.rating)}
                    <Badge variant="outline">{testimonial.rating}/5</Badge>
                  </div>

                  <blockquote className="text-sm text-muted-foreground italic">
                    "{testimonial.comment}"
                  </blockquote>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {/* Formulário de Novo Depoimento */}
        {isAddingNew && (
          <Card className="border-dashed">
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Novo Depoimento</h4>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={addTestimonial}>
                      <Save className="h-4 w-4 mr-1" />
                      Adicionar
                    </Button>
                    <Button size="sm" variant="outline" onClick={cancelEdit}>
                      <X className="h-4 w-4 mr-1" />
                      Cancelar
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="new-name">Nome *</Label>
                    <Input
                      id="new-name"
                      value={newTestimonial.name || ''}
                      onChange={(e) => setNewTestimonial(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Nome do cliente"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-location">Localização</Label>
                    <Input
                      id="new-location"
                      value={newTestimonial.location || ''}
                      onChange={(e) => setNewTestimonial(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="Cidade, Estado"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-company">Empresa</Label>
                    <Input
                      id="new-company"
                      value={newTestimonial.company || ''}
                      onChange={(e) => setNewTestimonial(prev => ({ ...prev, company: e.target.value }))}
                      placeholder="Nome da empresa"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-position">Cargo</Label>
                    <Input
                      id="new-position"
                      value={newTestimonial.position || ''}
                      onChange={(e) => setNewTestimonial(prev => ({ ...prev, position: e.target.value }))}
                      placeholder="Cargo do cliente"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-source">Origem do Depoimento</Label>
                    <Select
                      value={newTestimonial.source || ''}
                      onValueChange={(value) => setNewTestimonial(prev => ({ ...prev, source: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a origem" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="tiktok">🎵 TikTok</SelectItem>
                        <SelectItem value="whatsapp">💬 WhatsApp</SelectItem>
                        <SelectItem value="instagram">📷 Instagram</SelectItem>
                        <SelectItem value="facebook">👥 Facebook</SelectItem>
                        <SelectItem value="youtube">📺 YouTube</SelectItem>
                        <SelectItem value="email">📧 Email</SelectItem>
                        <SelectItem value="website">🌐 Website</SelectItem>
                        <SelectItem value="other">💬 Outro</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="new-source-url">URL da Origem (opcional)</Label>
                    <Input
                      id="new-source-url"
                      value={newTestimonial.sourceUrl || ''}
                      onChange={(e) => setNewTestimonial(prev => ({ ...prev, sourceUrl: e.target.value }))}
                      placeholder="https://..."
                    />
                  </div>
                </div>

                <div>
                  <Label>Comentário *</Label>
                  <Textarea
                    value={newTestimonial.comment || ''}
                    onChange={(e) => setNewTestimonial(prev => ({ ...prev, comment: e.target.value }))}
                    placeholder="Depoimento do cliente"
                    rows={3}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div>
                      <Label>Avaliação</Label>
                      <div className="mt-1">
                        {renderStars(newTestimonial.rating || 5, true)}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={newTestimonial.verified || false}
                        onCheckedChange={(checked) => setNewTestimonial(prev => ({ ...prev, verified: checked }))}
                      />
                      <Label>Verificado</Label>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Label>Foto</Label>
                    <div className="flex items-center gap-2">
                      {newTestimonial.avatar && (
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={newTestimonial.avatar} />
                          <AvatarFallback>
                            {newTestimonial.name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                      )}
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => e.target.files?.[0] && handleAvatarUpload(e.target.files[0])}
                        className="hidden"
                        id="new-avatar"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => document.getElementById('new-avatar')?.click()}
                        disabled={isUploading}
                      >
                        <Upload className="h-4 w-4 mr-1" />
                        {isUploading ? 'Enviando...' : 'Upload'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
