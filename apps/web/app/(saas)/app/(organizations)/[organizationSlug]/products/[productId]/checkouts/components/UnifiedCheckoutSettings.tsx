'use client';

import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Switch } from '@ui/components/switch';
import { Label } from '@ui/components/label';
import { Input } from '@ui/components/input';
import { Textarea } from '@ui/components/textarea';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@ui/components/tabs';
import { Badge } from '@ui/components/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Eye, Save, Settings, Upload, X, Loader2, Palette, Zap, MessageSquare, Shield, Settings as SettingsIcon } from 'lucide-react';
import { BannerUpload } from '@saas/products/components/BannerUpload';
import { LogoUpload } from '@saas/products/components/LogoUpload';
import { TestimonialsEditor } from './TestimonialsEditor';
import { <PERSON><PERSON><PERSON>eeCardsEditor } from './GuaranteeCardsEditor';
import { SidebarBannerUpload } from '../../../../../../../../(checkout)/checkout/components/sidebar-banner-upload';
 

interface UnifiedCheckoutSettingsProps {
  productId: string;
  organizationId: string;
  currentBanner?: string | null;
  onBannerChange?: (bannerUrl: string | null) => void;
  initialSettings?: any;
  onSave?: (settings: any) => void;
  onPreview?: () => void;
  isLoading?: boolean;
}

interface CheckoutSettingsData {
  // Banner (usando upload, sem URL manual)
  banner: {
    enabled: boolean;
    maxHeight: string;
    borderRadius: string;
    shadow: boolean;
  };

  // Header
  header: {
    showLogo: boolean;
    logoUrl?: string;
    companyName: string;
  };

  // Urgência
  urgency: {
    enabled: boolean;
    message: string;
    endTime?: string;
    backgroundColor: string;
    textColor: string;
    accentColor: string;
  };

  // Confiança
  trustBadges: {
    enabled: boolean;
    badges: Array<{
      id: string;
      title: string;
      subtitle: string;
      icon: string;
      enabled: boolean;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    showDescriptions: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Cards de Garantia
  guaranteeCards: {
    enabled: boolean;
    cards: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      customIcon?: string;
      enabled: boolean;
      order: number;
    }>;
    layout: 'horizontal' | 'vertical' | 'grid';
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };


  // Depoimentos
  testimonials: {
    enabled: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      rating: number;
      comment: string;
      avatar?: string;
      location?: string;
      verified?: boolean;
    }>;
    maxTestimonials: number;
    autoPlay: boolean;
    autoPlayInterval: number;
    showControls: boolean;
    showStars: boolean;
    showAvatars: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
  };

  // Sidebar
  sidebar: {
    enabled: boolean;
    bannerUrl?: string;
    title: string;
    content: string;
    showSummary: boolean;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    shadow: boolean;
  };
}

const defaultSettings: CheckoutSettingsData = {
  banner: {
    enabled: true,
    maxHeight: '300px',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
  header: {
    showLogo: false,
    companyName: 'SupGateway',
  },
  urgency: {
    enabled: false,
    message: 'Esta oferta se encerra em:',
    backgroundColor: 'bg-red-50',
    textColor: 'text-white',
    accentColor: 'bg-red-600',
  },
  trustBadges: {
    enabled: true,
    badges: [
      {
        id: 'security',
        title: '100% Seguro',
        subtitle: 'Pagamentos protegidos',
        icon: 'shield',
        enabled: true,
      },
      {
        id: 'guarantee',
        title: 'Garantia de 30 dias',
        subtitle: 'Devolução garantida',
        icon: 'check',
        enabled: true,
      },
      {
        id: 'support',
        title: 'Suporte 24/7',
        subtitle: 'Atendimento completo',
        icon: 'mail',
        enabled: true,
      },
    ],
    layout: 'vertical',
    showDescriptions: true,
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
  },

  guaranteeCards: {
    enabled: true,
    cards: [
      {
        id: 'security',
        title: '100% Seguro',
        description: 'Pagamentos protegidos com criptografia de ponta a ponta',
        icon: 'shield',
        enabled: true,
        order: 1,
      },
      {
        id: 'guarantee',
        title: 'Garantia de 30 dias',
        description: 'Devolução garantida se não ficar satisfeito',
        icon: 'check',
        enabled: true,
        order: 2,
      },
      {
        id: 'support',
        title: 'Suporte 24/7',
        description: 'Atendimento completo sempre disponível',
        icon: 'heart',
        enabled: true,
        order: 3,
      },
    ],
    layout: 'grid',
    backgroundColor: 'bg-green-50',
    textColor: 'text-green-800',
    borderColor: 'border-green-200',
  },
  testimonials: {
    enabled: true,
    testimonials: [
      {
        id: '1',
        name: 'Carlos Silva',
        rating: 5,
        comment: 'A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX.',
        location: 'São Paulo, SP',
        verified: true,
      },
      {
        id: '2',
        name: 'Ana Rodrigues',
        rating: 5,
        comment: 'Desde que implementamos a solução, nossa taxa de abandono de carrinho diminuiu drasticamente.',
        location: 'Rio de Janeiro, RJ',
        verified: true,
      },
    ],
    maxTestimonials: 3,
    autoPlay: true,
    autoPlayInterval: 5000,
    showControls: true,
    showStars: true,
    showAvatars: true,
    backgroundColor: 'bg-gray-50',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
  },
  sidebar: {
    enabled: false,
    title: 'Informações Importantes',
    content: 'Adicione informações úteis para seus clientes aqui.',
    showSummary: true,
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-200',
    borderRadius: 'rounded-lg',
    shadow: true,
  },
};

export function UnifiedCheckoutSettings({
  productId,
  organizationId,
  currentBanner,
  onBannerChange,
  initialSettings = {},
  onSave,
  onPreview,
  isLoading = false
}: UnifiedCheckoutSettingsProps) {
  const [settings, setSettings] = useState<CheckoutSettingsData>({
    ...defaultSettings,
    ...initialSettings
  });
  const [activeTab, setActiveTab] = useState('visual');
  const [sidebarBanner, setSidebarBanner] = useState<string | null>(
    initialSettings?.sidebar?.bannerUrl || null
  );

  const updateSetting = (section: keyof CheckoutSettingsData, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
  };

  const handleSidebarBannerChange = (url: string | null) => {
    setSidebarBanner(url);
    updateSetting('sidebar', 'bannerUrl', url);
  };

  const handleSave = () => {
    const settingsWithBanner = {
      ...settings,
      sidebar: {
        ...settings.sidebar,
        bannerUrl: sidebarBanner,
      },
    };
    onSave?.(settingsWithBanner);
  };

  const handlePreview = () => {
    onPreview?.();
  };

  const getActiveElementsCount = () => {
    let count = 0;
    if (settings.banner.enabled && currentBanner) count++;
    if (settings.header.showLogo) count++;
    if (settings.urgency.enabled) count++;
    if (settings.trustBadges.enabled) count++;
    if (settings.guaranteeCards.enabled) count++;
    if (settings.testimonials.enabled) count++;
    if (settings.sidebar.enabled) count++;
    return count;
  };

  return (
    <div className="space-y-6">
      {/* Header com contador de elementos ativos */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Configurações do Checkout</h2>
          <p className="text-sm text-muted-foreground">
            Personalize a aparência e elementos de conversão
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge>
            {getActiveElementsCount()} elementos ativos
          </Badge>
          <Button variant="outline" onClick={handlePreview}>
            <Eye className="h-4 w-4 mr-2" />
            Visualizar
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="visual" className="flex flex-col items-center gap-1 py-3">
            <Palette className="h-4 w-4" />
            <span className="text-xs">Visual & Branding</span>
          </TabsTrigger>
          <TabsTrigger value="conversion" className="flex flex-col items-center gap-1 py-3">
            <Zap className="h-4 w-4" />
            <span className="text-xs">Conversão</span>
          </TabsTrigger>
          <TabsTrigger value="social-proof" className="flex flex-col items-center gap-1 py-3">
            <MessageSquare className="h-4 w-4" />
            <span className="text-xs">Prova Social</span>
          </TabsTrigger>
          <TabsTrigger value="trust" className="flex flex-col items-center gap-1 py-3">
            <Shield className="h-4 w-4" />
            <span className="text-xs">Confiança</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex flex-col items-center gap-1 py-3">
            <SettingsIcon className="h-4 w-4" />
            <span className="text-xs">Avançado</span>
          </TabsTrigger>
        </TabsList>

        {/* Visual & Branding - Combina Banner e Header */}
        <TabsContent value="visual">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Visual & Branding
              </CardTitle>
              <CardDescription>
                Personalize a aparência do seu checkout com banner, header e identidade visual
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Upload do Banner */}
              <div className="space-y-4">
                <Label>Imagem do Banner</Label>
                <BannerUpload
                  currentBanner={currentBanner}
                  onBannerChange={onBannerChange || (() => {})}
                  showLabel={false}
                />
                {currentBanner && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Banner configurado com sucesso
                  </div>
                )}
              </div>

              {/* Configurações de exibição */}
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="banner-enabled">Exibir Banner</Label>
                  <p className="text-sm text-muted-foreground">
                    {currentBanner ? 'Banner disponível para exibição' : 'Faça upload de uma imagem primeiro'}
                  </p>
                </div>
                <Switch
                  id="banner-enabled"
                  checked={settings.banner.enabled && !!currentBanner}
                  onCheckedChange={(checked) => updateSetting('banner', 'enabled', checked)}
                  disabled={!currentBanner}
                />
              </div>

              {settings.banner.enabled && currentBanner && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="banner-height">Altura Máxima</Label>
                      <Input
                        id="banner-height"
                        value={settings.banner.maxHeight}
                        onChange={(e) => updateSetting('banner', 'maxHeight', e.target.value)}
                        placeholder="300px"
                      />
                    </div>
                    <div>
                      <Label htmlFor="banner-radius">Bordas</Label>
                      <Select
                        value={settings.banner.borderRadius}
                        onValueChange={(value) => updateSetting('banner', 'borderRadius', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rounded-none">Nenhuma</SelectItem>
                          <SelectItem value="rounded-sm">Pequena</SelectItem>
                          <SelectItem value="rounded">Média</SelectItem>
                          <SelectItem value="rounded-lg">Grande</SelectItem>
                          <SelectItem value="rounded-xl">Extra Grande</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="banner-shadow">Sombra</Label>
                    <Switch
                      id="banner-shadow"
                      checked={settings.banner.shadow}
                      onCheckedChange={(checked) => updateSetting('banner', 'shadow', checked)}
                    />
                  </div>
                </>
              )}

              {/* Seção Header */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h4 className="font-medium flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Header & Logo
                </h4>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="header-logo">Exibir Logo</Label>
                    <Switch
                      id="header-logo"
                      checked={settings.header.showLogo}
                      onCheckedChange={(checked) => updateSetting('header', 'showLogo', checked)}
                    />
                  </div>

                  {settings.header.showLogo && (
                    <LogoUpload
                      currentLogo={settings.header.logoUrl}
                      onLogoChange={(url) => updateSetting('header', 'logoUrl', url)}
                    />
                  )}

                  <div>
                    <Label htmlFor="header-company">Nome da Empresa</Label>
                    <Input
                      id="header-company"
                      value={settings.header.companyName}
                      onChange={(e) => updateSetting('header', 'companyName', e.target.value)}
                      placeholder="Sua Empresa"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>






        {/* Conversão - Urgência e Escassez */}
        <TabsContent value="conversion">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Elementos de Conversão
              </CardTitle>
              <CardDescription>
                Configure elementos que aumentam a urgência e incentivam a conversão
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Urgência */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h4 className="font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Barra de Urgência
                </h4>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="urgency-enabled">Exibir Barra de Urgência</Label>
                  <Switch
                    id="urgency-enabled"
                    checked={settings.urgency.enabled}
                    onCheckedChange={(checked) => updateSetting('urgency', 'enabled', checked)}
                  />
                </div>

                {settings.urgency.enabled && (
                  <>
                    <div>
                      <Label htmlFor="urgency-message">Mensagem de Urgência</Label>
                      <Input
                        id="urgency-message"
                        value={settings.urgency.message}
                        onChange={(e) => updateSetting('urgency', 'message', e.target.value)}
                        placeholder="Esta oferta se encerra em:"
                      />
                    </div>

                    <div>
                      <Label htmlFor="urgency-end-time">Data/Hora de Encerramento</Label>
                      <Input
                        id="urgency-end-time"
                        type="datetime-local"
                        value={settings.urgency.endTime || ''}
                        onChange={(e) => updateSetting('urgency', 'endTime', e.target.value)}
                      />
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="urgency-bg">Cor de Fundo</Label>
                        <Select
                          value={settings.urgency.backgroundColor}
                          onValueChange={(value) => updateSetting('urgency', 'backgroundColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-red-50">Vermelho Claro</SelectItem>
                            <SelectItem value="bg-orange-50">Laranja Claro</SelectItem>
                            <SelectItem value="bg-yellow-50">Amarelo Claro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="urgency-text">Cor do Texto</Label>
                        <Select
                          value={settings.urgency.textColor}
                          onValueChange={(value) => updateSetting('urgency', 'textColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text-red-800">Vermelho Escuro</SelectItem>
                            <SelectItem value="text-orange-800">Laranja Escuro</SelectItem>
                            <SelectItem value="text-yellow-800">Amarelo Escuro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="urgency-accent">Cor de Destaque</Label>
                        <Select
                          value={settings.urgency.accentColor}
                          onValueChange={(value) => updateSetting('urgency', 'accentColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-red-600">Vermelho</SelectItem>
                            <SelectItem value="bg-orange-600">Laranja</SelectItem>
                            <SelectItem value="bg-yellow-600">Amarelo</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Prova Social - Depoimentos */}
        <TabsContent value="social-proof">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Prova Social
              </CardTitle>
              <CardDescription>
                Configure depoimentos e elementos de prova social para aumentar a confiança
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="testimonials-enabled">Exibir Depoimentos de Clientes</Label>
                <Switch
                  id="testimonials-enabled"
                  checked={settings.testimonials.enabled}
                  onCheckedChange={(checked) => updateSetting('testimonials', 'enabled', checked)}
                />
              </div>

              {settings.testimonials.enabled && (
                <>
                  {/* Editor de Depoimentos */}
                  <TestimonialsEditor
                    testimonials={settings.testimonials.testimonials}
                    onTestimonialsChange={(newTestimonials) => updateSetting('testimonials', 'testimonials', newTestimonials)}
                    maxTestimonials={10}
                  />

                  {/* Configurações de Exibição */}
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-4">Configurações de Exibição</h4>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="testimonials-max">Máximo de Depoimentos</Label>
                        <Input
                          id="testimonials-max"
                          type="number"
                          value={settings.testimonials.maxTestimonials}
                          onChange={(e) => updateSetting('testimonials', 'maxTestimonials', parseInt(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="testimonials-interval">Intervalo (ms)</Label>
                        <Input
                          id="testimonials-interval"
                          type="number"
                          value={settings.testimonials.autoPlayInterval}
                          onChange={(e) => updateSetting('testimonials', 'autoPlayInterval', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="testimonials-autoplay">Reprodução Automática</Label>
                        <Switch
                          id="testimonials-autoplay"
                          checked={settings.testimonials.autoPlay}
                          onCheckedChange={(checked) => updateSetting('testimonials', 'autoPlay', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="testimonials-stars">Mostrar Estrelas</Label>
                        <Switch
                          id="testimonials-stars"
                          checked={settings.testimonials.showStars}
                          onCheckedChange={(checked) => updateSetting('testimonials', 'showStars', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="testimonials-avatars">Mostrar Avatares</Label>
                        <Switch
                          id="testimonials-avatars"
                          checked={settings.testimonials.showAvatars}
                          onCheckedChange={(checked) => updateSetting('testimonials', 'showAvatars', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Confiança - Trust Badges e Garantias */}
        <TabsContent value="trust">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Elementos de Confiança
              </CardTitle>
              <CardDescription>
                Configure badges de confiança e garantias para aumentar a credibilidade
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Trust Badges */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h4 className="font-medium flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Trust Badges
                </h4>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="trust-badges-enabled">Exibir Trust Badges</Label>
                  <Switch
                    id="trust-badges-enabled"
                    checked={settings.trustBadges.enabled}
                    onCheckedChange={(checked) => updateSetting('trustBadges', 'enabled', checked)}
                  />
                </div>

                {settings.trustBadges.enabled && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="trust-layout">Layout</Label>
                        <Select
                          value={settings.trustBadges.layout}
                          onValueChange={(value) => updateSetting('trustBadges', 'layout', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="horizontal">Horizontal</SelectItem>
                            <SelectItem value="vertical">Vertical</SelectItem>
                            <SelectItem value="grid">Grid</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="trust-descriptions">Mostrar Descrições</Label>
                        <Switch
                          id="trust-descriptions"
                          checked={settings.trustBadges.showDescriptions}
                          onCheckedChange={(checked) => updateSetting('trustBadges', 'showDescriptions', checked)}
                        />
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Cards de Garantia */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h4 className="font-medium flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Cards de Garantia
                </h4>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="guarantee-cards-enabled">Exibir Cards de Garantia</Label>
                  <Switch
                    id="guarantee-cards-enabled"
                    checked={settings.guaranteeCards.enabled}
                    onCheckedChange={(checked) => updateSetting('guaranteeCards', 'enabled', checked)}
                  />
                </div>

                {settings.guaranteeCards.enabled && (
                  <>
                    <GuaranteeCardsEditor
                      cards={settings.guaranteeCards.cards}
                      onCardsChange={(newCards) => updateSetting('guaranteeCards', 'cards', newCards)}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="guarantee-layout">Layout</Label>
                        <Select
                          value={settings.guaranteeCards.layout}
                          onValueChange={(value) => updateSetting('guaranteeCards', 'layout', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="horizontal">Horizontal</SelectItem>
                            <SelectItem value="vertical">Vertical</SelectItem>
                            <SelectItem value="grid">Grid</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Avançado - Sidebar e Configurações Extras */}
        <TabsContent value="advanced">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <SettingsIcon className="h-5 w-5" />
                Configurações Avançadas
              </CardTitle>
              <CardDescription>
                Configure elementos adicionais como sidebar e outras funcionalidades
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Sidebar Settings */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h4 className="font-medium flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Sidebar
                </h4>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="sidebar-enabled">Exibir Sidebar</Label>
                  <Switch
                    id="sidebar-enabled"
                    checked={settings.sidebar.enabled}
                    onCheckedChange={(checked) => updateSetting('sidebar', 'enabled', checked)}
                  />
                </div>

                {settings.sidebar.enabled && (
                  <>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="sidebar-show-summary">Mostrar Resumo da Compra</Label>
                      <Switch
                        id="sidebar-show-summary"
                        checked={settings.sidebar.showSummary}
                        onCheckedChange={(checked) => updateSetting('sidebar', 'showSummary', checked)}
                      />
                    </div>

                    <div>
                      <SidebarBannerUpload
                        currentBanner={sidebarBanner || undefined}
                        onBannerChange={handleSidebarBannerChange}
                      />
                    </div>

                    <div>
                      <Label htmlFor="sidebar-title">Título</Label>
                      <Input
                        id="sidebar-title"
                        value={settings.sidebar.title}
                        onChange={(e) => updateSetting('sidebar', 'title', e.target.value)}
                        placeholder="Informações Importantes"
                      />
                    </div>

                    <div>
                      <Label htmlFor="sidebar-content">Conteúdo</Label>
                      <Textarea
                        id="sidebar-content"
                        value={settings.sidebar.content}
                        onChange={(e) => updateSetting('sidebar', 'content', e.target.value)}
                        placeholder="Adicione informações úteis para seus clientes aqui."
                        rows={4}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="sidebar-bg">Cor de Fundo</Label>
                        <Select
                          value={settings.sidebar.backgroundColor}
                          onValueChange={(value) => updateSetting('sidebar', 'backgroundColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bg-blue-50">Azul Claro</SelectItem>
                            <SelectItem value="bg-gray-50">Cinza Claro</SelectItem>
                            <SelectItem value="bg-green-50">Verde Claro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="sidebar-text">Cor do Texto</Label>
                        <Select
                          value={settings.sidebar.textColor}
                          onValueChange={(value) => updateSetting('sidebar', 'textColor', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text-blue-800">Azul Escuro</SelectItem>
                            <SelectItem value="text-gray-800">Cinza Escuro</SelectItem>
                            <SelectItem value="text-green-800">Verde Escuro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
