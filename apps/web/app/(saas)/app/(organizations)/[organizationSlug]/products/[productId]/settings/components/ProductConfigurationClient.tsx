"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import { toast } from "sonner";
import {
  PackageIcon,
  TrashIcon,
  CheckIcon,
  HelpCircleIcon,
  SmartphoneIcon,
  UsersIcon,
  DollarSignIcon
} from "lucide-react";
import { ProductImageUpload } from "@saas/products";

interface ProductConfigurationClientProps {
  product: any; // Substitua por um tipo mais específico
  organizationSlug: string;
}

export function ProductConfigurationClient({
  product,
  organizationSlug
}: ProductConfigurationClientProps) {
  const [formData, setFormData] = useState({
    name: product.name || "",
    description: product.description || "",
    sellerName: "Ismael",
    sellerEmail: "<EMAIL>",
    sellerPhone: "",
    category: product.category?.name || "Tecnologia",
    format: product.type || "Serviço",
    language: "Português",
    currency: "Real (R$)",
    salesPage: "https://app.exemplo.com/products",
    isActive: product.status === "PUBLISHED",

    // Checkout & Conversion Settings
    guaranteeType: product.guaranteeType || "30_DAYS",
    guaranteeText: product.guaranteeText || "",
    showTestimonials: product.showTestimonials ?? true,
    showUrgency: product.showUrgency ?? true,
    showScarcity: product.showScarcity ?? true,
    showTrustBadges: product.showTrustBadges ?? true,
  });

  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isEditingPrice, setIsEditingPrice] = useState(false);
  const [priceValue, setPriceValue] = useState((product.priceCents / 100).toString());
  const [productImage, setProductImage] = useState(product.thumbnail || null);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleImageChange = (url: string | null) => {
    setProductImage(url);
    setHasChanges(true);
  };

  const saveProduct = async () => {
    setIsSaving(true);
    try {
      const response = await fetch(`/api/products/${product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          thumbnail: productImage,
          sellerName: formData.sellerName,
          sellerEmail: formData.sellerEmail,
          sellerPhone: formData.sellerPhone,
          category: formData.category,
          format: formData.format,
          language: formData.language,
          currency: formData.currency,
          salesPage: formData.salesPage,
          isActive: formData.isActive,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao salvar produto');
      }

      setHasChanges(false);
      toast.success('Produto salvo com sucesso!');
    } catch (error) {
      console.error('Error saving product:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao salvar produto');
    } finally {
      setIsSaving(false);
    }
  };

  const publishProduct = async () => {
    setIsPublishing(true);
    try {
      // Primeiro salva as alterações
      await saveProduct();

      // Depois publica o produto
      const response = await fetch(`/api/products/${product.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'PUBLISHED' }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao publicar produto');
      }

      toast.success('Produto publicado com sucesso!');
      // Recarregar a página para atualizar o status
      window.location.reload();
    } catch (error) {
      console.error('Error publishing product:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao publicar produto');
    } finally {
      setIsPublishing(false);
    }
  };

  const unpublishProduct = async () => {
    setIsPublishing(true);
    try {
      const response = await fetch(`/api/products/${product.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'DRAFT' }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao despublicar produto');
      }

      toast.success('Produto despublicado com sucesso!');
      // Recarregar a página para atualizar o status
      window.location.reload();
    } catch (error) {
      console.error('Error unpublishing product:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao despublicar produto');
    } finally {
      setIsPublishing(false);
    }
  };

  const updatePrice = async () => {
    const priceCents = Math.round(parseFloat(priceValue) * 100);

    if (isNaN(priceCents) || priceCents < 0) {
      toast.error('Preço inválido');
      return;
    }

    try {
      const response = await fetch(`/api/products/${product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceCents: priceCents,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao atualizar preço');
      }

      toast.success('Preço atualizado com sucesso!');
      setIsEditingPrice(false);
      // Recarregar a página para atualizar o preço
      window.location.reload();
    } catch (error) {
      console.error('Error updating price:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar preço');
    }
  };

  const cancelEditPrice = () => {
    setPriceValue((product.priceCents / 100).toString());
    setIsEditingPrice(false);
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(cents / 100);
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'COURSE': return 'Curso';
      case 'EBOOK': return 'E-book';
      case 'MENTORSHIP': return 'Mentoria';
      case 'SUBSCRIPTION': return 'Assinatura';
      case 'BUNDLE': return 'Pacote';
      case 'SERVICE': return 'Serviço';
      default: return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Bar - Top Right */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <PackageIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                {getTypeLabel(product.type)}
              </Badge>
              <Badge className={product.status === 'PUBLISHED' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'}>
                {product.status === 'PUBLISHED' ? 'Publicado' : 'Rascunho'}
              </Badge>
            </div>
            <h1 className="text-lg font-semibold text-foreground">{product.name}</h1>
          </div>
        </div>

        {/* Status Actions */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Label className="text-sm font-medium">Ativo</Label>
            <Switch
              checked={formData.isActive}
              onCheckedChange={(checked) => handleInputChange('isActive', checked)}
            />
          </div>

          {product.status === 'DRAFT' && (
            <Button
              size="sm"
              onClick={publishProduct}
              disabled={isPublishing}
            >
              {isPublishing ? 'Publicando...' : 'Publicar'}
            </Button>
          )}
          {product.status === 'PUBLISHED' && (
            <Button
              size="sm"
              variant="outline"
              onClick={unpublishProduct}
              disabled={isPublishing}
            >
              {isPublishing ? 'Despublicando...' : 'Despublicar'}
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informações básicas</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Product Cover */}
            <ProductImageUpload
              currentImage={productImage}
              onImageChange={handleImageChange}
              showLabel={true}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nome */}
              <div className="space-y-2">
                <Label htmlFor="name">Nome</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Nome do produto"
                />
                <p className="text-xs text-muted-foreground">
                  Esse nome será exibido em todos os locais da plataforma - ({formData.name.length}/60 caracteres)
                </p>
              </div>

              {/* Descrição */}
              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Descrição do produto"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.description.length}/200 caracteres
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preço Principal */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <DollarSignIcon className="h-5 w-5" />
              Preço Principal
            </CardTitle>
            <CardDescription>
              Configure o preço base do seu produto. Ofertas especiais podem ser criadas na seção "Ofertas"
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-sm font-medium">Preço Atual</Label>
                  {isEditingPrice ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-lg text-muted-foreground">R$</span>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={priceValue}
                          onChange={(e) => setPriceValue(e.target.value)}
                          className="text-2xl font-bold border-2"
                          placeholder="0,00"
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" onClick={updatePrice}>
                          Salvar
                        </Button>
                        <Button size="sm" variant="outline" onClick={cancelEditPrice}>
                          Cancelar
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="text-3xl font-bold text-foreground mt-1">
                        {formatCurrency(product.priceCents)}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Preço base do produto
                      </p>
                    </div>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium">Moeda</Label>
                  <p className="text-lg font-medium text-foreground mt-1">
                    {product.currency || 'BRL'}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Moeda de venda
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Gerenciar Preços</p>
                    <p className="text-xs text-muted-foreground">
                      Configure preços e ofertas especiais
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditingPrice(true)}
                      disabled={isEditingPrice}
                    >
                      <DollarSignIcon className="h-4 w-4 mr-2" />
                      Editar Preço
                    </Button>
                    <Button variant="outline" size="sm">
                      <UsersIcon className="h-4 w-4 mr-2" />
                      Ver Ofertas
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preferências */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Preferências</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Categoria</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Tecnologia da Informação">Tecnologia da Informação</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Design">Design</SelectItem>
                    <SelectItem value="Negócios">Negócios</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="format">Formato</Label>
                <Select value={formData.format} onValueChange={(value) => handleInputChange('format', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Serviço">Serviço</SelectItem>
                    <SelectItem value="Curso">Curso</SelectItem>
                    <SelectItem value="E-book">E-book</SelectItem>
                    <SelectItem value="Mentoria">Mentoria</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Idioma</Label>
                <Select value={formData.language} onValueChange={(value) => handleInputChange('language', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Português">Português</SelectItem>
                    <SelectItem value="English">English</SelectItem>
                    <SelectItem value="Español">Español</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Moeda base</Label>
                <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Real (R$)">Real (R$)</SelectItem>
                    <SelectItem value="Dólar (US$)">Dólar (US$)</SelectItem>
                    <SelectItem value="Euro (€)">Euro (€)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="salesPage">Página de vendas</Label>
              <Input
                id="salesPage"
                value={formData.salesPage}
                onChange={(e) => handleInputChange('salesPage', e.target.value)}
                placeholder="https://app.kirvano.com/products"
              />
            </div>
          </CardContent>
        </Card>

        {/* Garantia */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Garantia</CardTitle>
            <CardDescription>
              Configure a garantia do seu produto
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="guaranteeType">Tipo de Garantia</Label>
              <Select
                value={formData.guaranteeType}
                onValueChange={(value) => handleInputChange('guaranteeType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NONE">Sem garantia</SelectItem>
                  <SelectItem value="7_DAYS">7 dias</SelectItem>
                  <SelectItem value="30_DAYS">30 dias</SelectItem>
                  <SelectItem value="60_DAYS">60 dias</SelectItem>
                  <SelectItem value="90_DAYS">90 dias</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.guaranteeType !== "NONE" && (
              <div className="space-y-2">
                <Label htmlFor="guaranteeText">Texto personalizado da garantia (opcional)</Label>
                <Textarea
                  id="guaranteeText"
                  value={formData.guaranteeText}
                  onChange={(e) => handleInputChange('guaranteeText', e.target.value)}
                  placeholder="Ex: Se não ficar satisfeito, devolvemos seu dinheiro em até 30 dias"
                  rows={2}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Suporte ao Comprador */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Suporte ao comprador</CardTitle>
            <CardDescription className="text-sm text-amber-600 dark:text-amber-400">
              (Obrigatório)
            </CardDescription>
            <p className="text-sm text-muted-foreground">
              Informações que serão apresentadas ao seu cliente.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="sellerName">Nome do vendedor</Label>
                <Input
                  id="sellerName"
                  value={formData.sellerName}
                  onChange={(e) => handleInputChange('sellerName', e.target.value)}
                  placeholder="Nome do vendedor"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sellerEmail">E-mail</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="sellerEmail"
                    value={formData.sellerEmail}
                    onChange={(e) => handleInputChange('sellerEmail', e.target.value)}
                    placeholder="E-mail do vendedor"
                  />
                  <Button variant="outline" size="sm">
                    Atualizar
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Atual: {formData.sellerEmail}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sellerPhone">Telefone</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="sellerPhone"
                    value={formData.sellerPhone}
                    onChange={(e) => handleInputChange('sellerPhone', e.target.value)}
                    placeholder="Telefone do vendedor"
                  />
                  <Button variant="outline" size="sm">
                    Cadastrar
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  (opcional) Enviaremos um código de verificação para validação.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-6 border-t border-border/50 bg-background/50 -mx-6 px-6 py-4">
          <Button variant="outline" size="lg" className="text-red-600 hover:text-red-700 hover:bg-red-50">
            <TrashIcon className="h-4 w-4 mr-2" />
            Excluir produto
          </Button>

          <Button
            size="lg"
            disabled={!hasChanges || isSaving}
            onClick={saveProduct}
            className={hasChanges ? "bg-primary hover:bg-primary/90" : ""}
          >
            <CheckIcon className="h-4 w-4 mr-2" />
            {isSaving ? 'Salvando...' : 'Salvar alterações'}
          </Button>
        </div>
      </div>
    </div>
  );
}
