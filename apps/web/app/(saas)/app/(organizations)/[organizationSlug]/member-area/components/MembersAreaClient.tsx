"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { 
  MoreHorizontalIcon, 
  UsersIcon, 
  SettingsIcon, 
  EyeIcon,
  EditIcon,
  TrashIcon,
  ExternalLinkIcon,
  CalendarIcon,
  ActivityIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";

interface MemberArea {
  id: string;
  name: string;
  description: string;
  productName: string;
  productId: string;
  memberCount: number;
  status: "active" | "inactive" | "archived";
  createdAt: string;
  lastActivity: string;
  features: string[];
  thumbnail: string;
}

interface MembersAreaClientProps {
  memberAreas: MemberArea[];
  organizationSlug: string;
}

export function MembersAreaClient({ memberAreas, organizationSlug }: MembersAreaClientProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive" | "archived">("all");

  const filteredAreas = memberAreas.filter(area => {
    const matchesSearch = area.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         area.productName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || area.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200";
      case "inactive":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200";
      case "archived":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Ativo";
      case "inactive":
        return "Inativo";
      case "archived":
        return "Arquivado";
      default:
        return "Desconhecido";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    });
  };

  return (
    <div className="space-y-6">
      {/* Filtros */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Buscar áreas de membros..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm"
          />
        </div>
        <div className="flex gap-2">
          {["all", "active", "inactive", "archived"].map((status) => (
            <Button
              key={status}
              variant={statusFilter === status ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter(status as any)}
            >
              {status === "all" ? "Todas" : getStatusText(status)}
            </Button>
          ))}
        </div>
      </div>

      {/* Lista de Áreas em Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredAreas.length === 0 ? (
          <div className="col-span-full">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <UsersIcon className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Nenhuma área de membros encontrada</h3>
                <p className="text-muted-foreground text-center mb-4">
                  {searchTerm || statusFilter !== "all" 
                    ? "Tente ajustar os filtros de busca"
                    : "Crie sua primeira área de membros para começar"
                  }
                </p>
                <Button>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Nova Área de Membros
                </Button>
              </CardContent>
            </Card>
          </div>
        ) : (
          filteredAreas.map((area) => (
            <Card key={area.id} className="h-[420px] hover:shadow-lg transition-all duration-200 group overflow-hidden flex flex-col">
              {/* Header com gradiente */}
              <div className="h-24 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10" />
                <div className="absolute top-3 right-3">
                  <Badge className={getStatusColor(area.status)}>
                    {getStatusText(area.status)}
                  </Badge>
                </div>
                <div className="absolute -bottom-6 left-4">
                  <Avatar className="h-12 w-12 border-2 border-white shadow-md">
                    <AvatarImage src={area.thumbnail} alt={area.name} />
                    <AvatarFallback className="bg-white text-blue-600 font-semibold">
                      {area.name.split(" ").map(n => n[0]).join("").toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </div>
              </div>

              {/* Conteúdo principal */}
              <CardContent className="p-4 flex-1 flex flex-col">
                {/* Informações Principais */}
                <div className="space-y-3 flex-1">
                  <div className="pt-2">
                    <CardTitle className="text-lg line-clamp-1 group-hover:text-blue-600 transition-colors">
                      {area.name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground line-clamp-1">
                      {area.productName}
                    </p>
                    <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                      {area.description}
                    </p>
                  </div>

                  {/* Estatísticas */}
                  <div className="flex items-center justify-between text-sm py-2">
                    <div className="flex items-center space-x-1">
                      <UsersIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{area.memberCount}</span>
                      <span className="text-muted-foreground">membros</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <ActivityIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground text-xs">
                        {formatDate(area.lastActivity)}
                      </span>
                    </div>
                  </div>

                  {/* Recursos */}
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-1">
                      {area.features.slice(0, 2).map((feature, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {area.features.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{area.features.length - 2}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Ações fixas na parte inferior */}
                <div className="flex space-x-2 pt-3 mt-auto">
                  <Button variant="outline" size="sm" className="flex-1">
                    <EyeIcon className="h-4 w-4 mr-1" />
                    Gerenciar
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <MoreHorizontalIcon className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <EyeIcon className="h-4 w-4 mr-2" />
                        Visualizar
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <EditIcon className="h-4 w-4 mr-2" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <SettingsIcon className="h-4 w-4 mr-2" />
                        Configurações
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <ExternalLinkIcon className="h-4 w-4 mr-2" />
                        Ver Produto
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Arquivar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
