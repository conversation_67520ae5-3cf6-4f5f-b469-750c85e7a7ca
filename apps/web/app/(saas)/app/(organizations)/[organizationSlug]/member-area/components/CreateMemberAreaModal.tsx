"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Checkbox } from "@ui/components/checkbox";
import { Badge } from "@ui/components/badge";
import { XIcon, PlusIcon } from "lucide-react";
import { toast } from "sonner";

const createMemberAreaSchema = z.object({
  name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  description: z.string().min(10, "Descrição deve ter pelo menos 10 caracteres"),
  productId: z.string().min(1, "Selecione um produto"),
  features: z.array(z.string()).min(1, "Adicione pelo menos um recurso"),
  customFeature: z.string().optional(),
  isPrivate: z.boolean().default(true),
  allowInvites: z.boolean().default(true),
  requireApproval: z.boolean().default(false),
});

type CreateMemberAreaFormData = z.infer<typeof createMemberAreaSchema>;

interface CreateMemberAreaModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationSlug: string;
}

// Mock de produtos para demonstração
const mockProducts = [
  { id: "prod-1", name: "Marketing Digital Completo", type: "COURSE" },
  { id: "prod-2", name: "Vendas de Alto Impacto", type: "COURSE" },
  { id: "prod-3", name: "Empreendedorismo Avançado", type: "COURSE" },
  { id: "prod-4", name: "E-book: Guia de Vendas", type: "EBOOK" },
  { id: "prod-5", name: "Mentoria Individual", type: "MENTORSHIP" },
];

const defaultFeatures = [
  "Fórum exclusivo",
  "Materiais bônus",
  "Suporte prioritário",
  "Networking",
  "Cases de sucesso",
  "Mentorias",
  "Workshops",
  "Consultoria",
  "Investidores",
  "Certificado",
  "Acesso vitalício",
  "Comunidade ativa",
];

export function CreateMemberAreaModal({ 
  isOpen, 
  onClose, 
  organizationSlug 
}: CreateMemberAreaModalProps) {
  const [customFeature, setCustomFeature] = useState("");
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);

  const form = useForm<CreateMemberAreaFormData>({
    resolver: zodResolver(createMemberAreaSchema),
    defaultValues: {
      name: "",
      description: "",
      productId: "",
      features: [],
      isPrivate: true,
      allowInvites: true,
      requireApproval: false,
    },
  });

  const onSubmit = async (data: CreateMemberAreaFormData) => {
    try {
      // Simular criação da área de membros
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Área de membros criada com sucesso!");
      form.reset();
      setSelectedFeatures([]);
      setCustomFeature("");
      onClose();
    } catch (error) {
      toast.error("Erro ao criar área de membros");
    }
  };

  const handleFeatureToggle = (feature: string) => {
    const newFeatures = selectedFeatures.includes(feature)
      ? selectedFeatures.filter(f => f !== feature)
      : [...selectedFeatures, feature];
    
    setSelectedFeatures(newFeatures);
    form.setValue("features", newFeatures);
  };

  const handleAddCustomFeature = () => {
    if (customFeature.trim() && !selectedFeatures.includes(customFeature.trim())) {
      const newFeatures = [...selectedFeatures, customFeature.trim()];
      setSelectedFeatures(newFeatures);
      form.setValue("features", newFeatures);
      setCustomFeature("");
    }
  };

  const handleRemoveFeature = (feature: string) => {
    const newFeatures = selectedFeatures.filter(f => f !== feature);
    setSelectedFeatures(newFeatures);
    form.setValue("features", newFeatures);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Criar Nova Área de Membros</DialogTitle>
          <DialogDescription>
            Crie uma área exclusiva para membros de um produto específico. 
            Configure recursos e permissões para engajar sua comunidade.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informações Básicas */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Informações Básicas</h3>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Área de Membros</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Comunidade de Marketing Digital" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Descreva o que os membros encontrarão nesta área..."
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Explique os benefícios e o que torna esta área especial
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="productId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Produto Associado</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um produto" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockProducts.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            <div className="flex items-center space-x-2">
                              <span>{product.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {product.type}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Apenas compradores deste produto terão acesso à área
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Recursos e Benefícios */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Recursos e Benefícios</h3>
              
              <div className="space-y-3">
                <FormLabel>Selecione os recursos disponíveis:</FormLabel>
                <div className="grid grid-cols-2 gap-2">
                  {defaultFeatures.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <Checkbox
                        id={feature}
                        checked={selectedFeatures.includes(feature)}
                        onCheckedChange={() => handleFeatureToggle(feature)}
                      />
                      <label 
                        htmlFor={feature}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {feature}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Adicionar recurso personalizado */}
              <div className="space-y-2">
                <FormLabel>Adicionar recurso personalizado:</FormLabel>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Ex: Acesso a investidores"
                    value={customFeature}
                    onChange={(e) => setCustomFeature(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddCustomFeature())}
                  />
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={handleAddCustomFeature}
                    disabled={!customFeature.trim()}
                  >
                    <PlusIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Recursos selecionados */}
              {selectedFeatures.length > 0 && (
                <div className="space-y-2">
                  <FormLabel>Recursos selecionados:</FormLabel>
                  <div className="flex flex-wrap gap-2">
                    {selectedFeatures.map((feature) => (
                      <Badge key={feature} variant="secondary" className="flex items-center gap-1">
                        {feature}
                        <button
                          type="button"
                          onClick={() => handleRemoveFeature(feature)}
                          className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                        >
                          <XIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Configurações de Acesso */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Configurações de Acesso</h3>
              
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="isPrivate"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Área Privada</FormLabel>
                        <FormDescription>
                          Apenas membros aprovados podem ver e participar
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allowInvites"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Permitir Convites</FormLabel>
                        <FormDescription>
                          Membros podem convidar outras pessoas
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requireApproval"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Aprovação Manual</FormLabel>
                        <FormDescription>
                          Novos membros precisam ser aprovados manualmente
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? "Criando..." : "Criar Área de Membros"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
