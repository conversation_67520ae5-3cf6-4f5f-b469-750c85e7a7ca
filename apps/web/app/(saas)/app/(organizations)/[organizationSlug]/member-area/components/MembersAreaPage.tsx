"use client";

import { useState } from "react";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { But<PERSON> } from "@ui/components/button";
import { PlusIcon, UsersIcon, UserCheckIcon, TrendingUpIcon, Users2Icon, MessageSquareIcon, ActivityIcon } from "lucide-react";
import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import { MembersAreaClient } from "./MembersAreaClient";
import { CreateMemberAreaModal } from "./CreateMemberAreaModal";

// Mock data para demonstração
const mockMetrics = {
  totalMembers: 1247,
  membersGrowth: 12.5,
  activeAreas: 8,
  areasGrowth: 2.0,
  engagementRate: 87.2,
  engagementGrowth: 5.3,
  totalPosts: 342,
  postsGrowth: 8.7
};

const mockMemberAreas = [
  {
    id: "1",
    name: "Curso de Marketing Digital",
    description: "Área exclusiva para membros do curso de marketing digital",
    productName: "Marketing Digital Completo",
    productId: "prod-1",
    memberCount: 342,
    status: "active" as const,
    createdAt: "2024-01-15",
    lastActivity: "2024-01-20",
    features: ["Fórum exclusivo", "Materiais bônus", "Suporte prioritário"],
    thumbnail: "/api/placeholder/400/200"
  },
  {
    id: "2", 
    name: "Comunidade de Vendas",
    description: "Grupo exclusivo para alunos do curso de vendas",
    productName: "Vendas de Alto Impacto",
    productId: "prod-2",
    memberCount: 189,
    status: "active" as const,
    createdAt: "2024-01-10",
    lastActivity: "2024-01-19",
    features: ["Networking", "Cases de sucesso", "Mentorias"],
    thumbnail: "/api/placeholder/400/200"
  },
  {
    id: "3",
    name: "Academia de Empreendedores",
    description: "Comunidade premium para empreendedores",
    productName: "Empreendedorismo Avançado",
    productId: "prod-3", 
    memberCount: 156,
    status: "active" as const,
    createdAt: "2024-01-05",
    lastActivity: "2024-01-18",
    features: ["Workshops", "Consultoria", "Investidores"],
    thumbnail: "/api/placeholder/400/200"
  }
];

interface MembersAreaPageProps {
  organizationSlug: string;
}

export function MembersAreaPage({ organizationSlug }: MembersAreaPageProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Formatadores de valores
  const formatNumber = (value: number) => {
    return value.toLocaleString('pt-BR');
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Members Area"
        subtitle="Gerencie áreas de membros para seus produtos e crie comunidades exclusivas"
        actions={
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Nova Área de Membros
          </Button>
        }
      />

      {/* Métricas Principais */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Visão Geral da Comunidade</h2>
        <MetricGrid columns={4}>
          <MetricCard
            title="Total de Membros"
            value={formatNumber(mockMetrics.totalMembers)}
            change={formatPercentage(mockMetrics.membersGrowth)}
            isPositive={mockMetrics.membersGrowth >= 0}
            icon={UsersIcon}
            description="Membros ativos em todas as áreas"
            badge={{
              text: "ATIVOS",
              status: "success"
            }}
            className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
          />

          <MetricCard
            title="Áreas Ativas"
            value={formatNumber(mockMetrics.activeAreas)}
            change={formatPercentage(mockMetrics.areasGrowth)}
            isPositive={mockMetrics.areasGrowth >= 0}
            icon={UserCheckIcon}
            description="Áreas de membros criadas"
            badge={{
              text: "CRIADAS",
              status: "info"
            }}
            className="bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20"
          />

          <MetricCard
            title="Taxa de Engajamento"
            value={`${mockMetrics.engagementRate.toFixed(1)}%`}
            change={formatPercentage(mockMetrics.engagementGrowth)}
            isPositive={mockMetrics.engagementGrowth >= 0}
            icon={TrendingUpIcon}
            description="Membros que acessam regularmente"
            badge={{
              text: "ALTA",
              status: "success"
            }}
            className="bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20"
          />

          <MetricCard
            title="Posts e Interações"
            value={formatNumber(mockMetrics.totalPosts)}
            change={formatPercentage(mockMetrics.postsGrowth)}
            isPositive={mockMetrics.postsGrowth >= 0}
            icon={MessageSquareIcon}
            description="Conteúdo compartilhado"
            badge={{
              text: "CRESCIMENTO",
              status: "info"
            }}
            className="bg-gradient-to-br from-orange-50/50 to-transparent dark:from-orange-950/20"
          />
        </MetricGrid>
      </div>

      {/* Lista de Áreas de Membros */}
      <MembersAreaClient 
        memberAreas={mockMemberAreas}
        organizationSlug={organizationSlug}
      />

      {/* Modal de Criação */}
      <CreateMemberAreaModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        organizationSlug={organizationSlug}
      />
    </div>
  );
}
