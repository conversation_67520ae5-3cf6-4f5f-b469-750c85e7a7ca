// Formatadores para dados brasileiros

// Formatar CPF
export function formatCPF(value: string): string {
  const numbers = value.replace(/\D/g, "");
  if (numbers.length <= 3) return numbers;
  if (numbers.length <= 6) return `${numbers.slice(0, 3)}.${numbers.slice(3)}`;
  if (numbers.length <= 9) return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6)}`;
  return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9, 11)}`;
}

// Formatar CNPJ
export function formatCNPJ(value: string): string {
  const numbers = value.replace(/\D/g, "");
  if (numbers.length <= 2) return numbers;
  if (numbers.length <= 5) return `${numbers.slice(0, 2)}.${numbers.slice(2)}`;
  if (numbers.length <= 8) return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5)}`;
  if (numbers.length <= 12) return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5, 8)}/${numbers.slice(8)}`;
  return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5, 8)}/${numbers.slice(8, 12)}-${numbers.slice(12, 14)}`;
}

// Formatar CEP
export function formatCEP(value: string): string {
  const numbers = value.replace(/\D/g, "");
  if (numbers.length <= 5) return numbers;
  return `${numbers.slice(0, 5)}-${numbers.slice(5, 8)}`;
}

// Formatar telefone brasileiro
export function formatPhone(value: string): string {
  const numbers = value.replace(/\D/g, "");
  if (numbers.length <= 2) return `(${numbers}`;
  if (numbers.length <= 6) return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
  if (numbers.length <= 10) return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
  return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
}

// Formatar data (DD/MM/YYYY)
export function formatDate(value: string): string {
  const numbers = value.replace(/\D/g, "");
  if (numbers.length <= 2) return numbers;
  if (numbers.length <= 4) return `${numbers.slice(0, 2)}/${numbers.slice(2)}`;
  return `${numbers.slice(0, 2)}/${numbers.slice(2, 4)}/${numbers.slice(4, 8)}`;
}

// Formatar valor monetário
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

// Formatar número com separadores de milhares
export function formatNumber(value: number): string {
  return new Intl.NumberFormat("pt-BR").format(value);
}

// Remover formatação (manter apenas números)
export function removeFormatting(value: string): string {
  return value.replace(/\D/g, "");
}

// Limpar CPF (remover formatação)
export function cleanCPF(cpf: string): string {
  return cpf.replace(/\D/g, "");
}

// Limpar CNPJ (remover formatação)
export function cleanCNPJ(cnpj: string): string {
  return cnpj.replace(/\D/g, "");
}

// Limpar CEP (remover formatação)
export function cleanCEP(cep: string): string {
  return cep.replace(/\D/g, "");
}

// Limpar telefone (remover formatação)
export function cleanPhone(phone: string): string {
  return phone.replace(/\D/g, "");
}

// Formatar nome próprio (primeira letra maiúscula)
export function formatProperName(name: string): string {
  return name
    .toLowerCase()
    .split(" ")
    .map(word => {
      // Não capitalizar preposições comuns
      const prepositions = ["de", "da", "do", "das", "dos", "e"];
      if (prepositions.includes(word)) return word;
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
}

// Validar e formatar email
export function formatEmail(email: string): string {
  return email.toLowerCase().trim();
}

// Formatar chave PIX baseada no tipo
export function formatPixKey(value: string, type?: string): string {
  const cleanValue = value.trim();
  
  switch (type) {
    case "CPF":
      return formatCPF(cleanValue);
    case "CNPJ":
      return formatCNPJ(cleanValue);
    case "Email":
      return formatEmail(cleanValue);
    case "Telefone":
      // Para PIX, telefone deve ter +55
      const numbers = cleanValue.replace(/\D/g, "");
      if (numbers.length === 11) {
        return `+55${numbers}`;
      }
      if (numbers.length === 13 && numbers.startsWith("55")) {
        return `+${numbers}`;
      }
      return cleanValue;
    default:
      return cleanValue;
  }
}

// Detectar tipo de chave PIX
export function detectPixKeyType(value: string): string | null {
  const cleanValue = value.replace(/\D/g, "");
  
  // CPF (11 dígitos)
  if (cleanValue.length === 11 && /^\d{11}$/.test(cleanValue)) {
    return "CPF";
  }
  
  // CNPJ (14 dígitos)
  if (cleanValue.length === 14 && /^\d{14}$/.test(cleanValue)) {
    return "CNPJ";
  }
  
  // Email
  if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return "Email";
  }
  
  // Telefone (+55XXXXXXXXXXX)
  if (/^\+55\d{10,11}$/.test(value) || /^55\d{10,11}$/.test(cleanValue)) {
    return "Telefone";
  }
  
  // Chave aleatória (UUID)
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
    return "Chave Aleatória";
  }
  
  return null;
}

// Formatar tamanho de arquivo
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// Truncar texto
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + "...";
}

// Capitalizar primeira letra
export function capitalize(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

// Formatar porcentagem
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

// Gerar iniciais do nome
export function getInitials(name: string): string {
  return name
    .split(" ")
    .filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join("");
}
