// Configurações do sistema de onboarding

export const ONBOARDING_CONFIG = {
  // Configurações de arquivo
  FILE_UPLOAD: {
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    ACCEPTED_TYPES: [".pdf"],
    MIME_TYPES: ["application/pdf"],
  },

  // Configurações de validação
  VALIDATION: {
    MIN_AGE: 18,
    PHONE_FORMATS: ["(XX) XXXX-XXXX", "(XX) XXXXX-XXXX"],
    CEP_FORMAT: "XXXXX-XXX",
    CPF_FORMAT: "XXX.XXX.XXX-XX",
    CNPJ_FORMAT: "XX.XXX.XXX/XXXX-XX",
  },

  // URLs de APIs externas
  EXTERNAL_APIS: {
    VIACEP: "https://viacep.com.br/ws",
    CEP_ABERTO: "https://www.cepaberto.com/api/v3/cep",
    BACKUP_CEP: "https://brasilapi.com.br/api/cep/v1",
  },

  // Configurações de localStorage
  STORAGE: {
    KEY: "onboarding-data",
    EXPIRY_HOURS: 24,
  },

  // Configurações de UI
  UI: {
    ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 500,
    PROGRESS_ANIMATION_DURATION: 500,
  },

  // Mensagens de erro padrão
  ERROR_MESSAGES: {
    REQUIRED_FIELD: "Este campo é obrigatório",
    INVALID_CPF: "CPF inválido",
    INVALID_CNPJ: "CNPJ inválido",
    INVALID_EMAIL: "Email inválido",
    INVALID_PHONE: "Telefone inválido",
    INVALID_CEP: "CEP inválido",
    INVALID_DATE: "Data inválida",
    INVALID_PIX: "Chave PIX inválida",
    MIN_AGE: "Idade mínima de 18 anos",
    FILE_TOO_LARGE: "Arquivo muito grande (máximo 5MB)",
    INVALID_FILE_TYPE: "Tipo de arquivo não suportado",
    NETWORK_ERROR: "Erro de conexão. Tente novamente.",
    GENERIC_ERROR: "Ocorreu um erro inesperado",
  },

  // Configurações de notificação
  NOTIFICATIONS: {
    SUCCESS_DURATION: 5000,
    ERROR_DURATION: 8000,
    WARNING_DURATION: 6000,
  },

  // Configurações de retry para APIs
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY_MS: 1000,
    BACKOFF_MULTIPLIER: 2,
  },

  // Configurações de analytics
  ANALYTICS: {
    TRACK_STEP_CHANGES: true,
    TRACK_VALIDATION_ERRORS: true,
    TRACK_FILE_UPLOADS: true,
    TRACK_COMPLETION_TIME: true,
  },

  // Configurações de acessibilidade
  ACCESSIBILITY: {
    FOCUS_TRAP: true,
    KEYBOARD_NAVIGATION: true,
    SCREEN_READER_ANNOUNCEMENTS: true,
    HIGH_CONTRAST_MODE: false,
  },

  // Configurações de desenvolvimento
  DEV: {
    ENABLE_DEBUG_LOGS: process.env.NODE_ENV === "development",
    MOCK_API_RESPONSES: false,
    SKIP_VALIDATIONS: false,
    AUTO_FILL_FORMS: false,
  },
} as const;

// Configurações específicas por ambiente
export const getEnvironmentConfig = () => {
  const env = process.env.NODE_ENV;
  
  switch (env) {
    case "development":
      return {
        ...ONBOARDING_CONFIG,
        DEV: {
          ENABLE_DEBUG_LOGS: true,
          MOCK_API_RESPONSES: true,
          SKIP_VALIDATIONS: false,
          AUTO_FILL_FORMS: true,
        },
      };
      
    case "test":
      return {
        ...ONBOARDING_CONFIG,
        DEV: {
          ENABLE_DEBUG_LOGS: false,
          MOCK_API_RESPONSES: true,
          SKIP_VALIDATIONS: true,
          AUTO_FILL_FORMS: true,
        },
      };
      
    case "production":
    default:
      return ONBOARDING_CONFIG;
  }
};

// Configurações de feature flags
export const FEATURE_FLAGS = {
  ENABLE_AUTO_CEP_SEARCH: true,
  ENABLE_DOCUMENT_PREVIEW: true,
  ENABLE_STEP_VALIDATION: true,
  ENABLE_AUTO_SAVE: true,
  ENABLE_PROGRESS_PERSISTENCE: true,
  ENABLE_ANALYTICS_TRACKING: true,
  ENABLE_ERROR_REPORTING: true,
  ENABLE_A11Y_FEATURES: true,
} as const;

// Configurações de tema/estilo
export const THEME_CONFIG = {
  COLORS: {
    PRIMARY: "hsl(var(--primary))",
    SUCCESS: "hsl(var(--success))",
    ERROR: "hsl(var(--destructive))",
    WARNING: "hsl(var(--warning))",
    INFO: "hsl(var(--info))",
  },
  
  SPACING: {
    STEP_GAP: "2rem",
    FORM_GAP: "1.5rem",
    INPUT_GAP: "1rem",
  },
  
  BREAKPOINTS: {
    SM: "640px",
    MD: "768px",
    LG: "1024px",
    XL: "1280px",
  },
} as const;

// Configurações de monitoramento
export const MONITORING_CONFIG = {
  ERROR_REPORTING: {
    ENABLED: process.env.NODE_ENV === "production",
    SERVICE: "sentry", // ou outro serviço
    SAMPLE_RATE: 0.1,
  },
  
  PERFORMANCE: {
    ENABLED: true,
    TRACK_RENDER_TIME: true,
    TRACK_API_CALLS: true,
    TRACK_USER_INTERACTIONS: true,
  },
  
  ANALYTICS: {
    ENABLED: process.env.NODE_ENV === "production",
    PROVIDER: "google-analytics", // ou outro
    TRACK_EVENTS: true,
    TRACK_PAGE_VIEWS: true,
  },
} as const;

// Utilitário para acessar configurações
export const getConfig = (path: string) => {
  const config = getEnvironmentConfig();
  return path.split('.').reduce((obj, key) => obj?.[key], config);
};

// Utilitário para verificar feature flags
export const isFeatureEnabled = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[feature] ?? false;
};

// Configurações de rate limiting
export const RATE_LIMIT_CONFIG = {
  CEP_API: {
    MAX_REQUESTS_PER_MINUTE: 60,
    COOLDOWN_MS: 1000,
  },
  
  FILE_UPLOAD: {
    MAX_FILES_PER_HOUR: 50,
    MAX_TOTAL_SIZE_MB: 100,
  },
  
  FORM_SUBMISSION: {
    MAX_ATTEMPTS_PER_HOUR: 10,
    LOCKOUT_DURATION_MS: 300000, // 5 minutos
  },
} as const;

// Configurações de segurança
export const SECURITY_CONFIG = {
  FILE_VALIDATION: {
    SCAN_FOR_MALWARE: true,
    CHECK_FILE_HEADERS: true,
    VALIDATE_FILE_SIZE: true,
    SANITIZE_FILE_NAMES: true,
  },
  
  DATA_SANITIZATION: {
    STRIP_HTML: true,
    VALIDATE_INPUTS: true,
    ESCAPE_SPECIAL_CHARS: true,
  },
  
  PRIVACY: {
    ENCRYPT_SENSITIVE_DATA: true,
    MASK_PERSONAL_INFO_IN_LOGS: true,
    AUTO_DELETE_TEMP_FILES: true,
  },
} as const;
