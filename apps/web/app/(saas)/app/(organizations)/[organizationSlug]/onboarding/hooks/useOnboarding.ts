"use client";

import { useState, useCallback, useEffect } from "react";
import { toast } from "sonner";
import type {
  OnboardingStep,
  OnboardingState,
  OnboardingDataForm,
  CompanyDocuments,
  AdminDocuments,
  OnboardingSubmitData,
  ONBOARDING_STEPS
} from "../types/onboarding";

const STORAGE_KEY = "onboarding-data";

interface UseOnboardingReturn extends OnboardingState {
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: OnboardingStep) => void;
  updateData: <T extends keyof OnboardingDataForm>(section: T, data: Partial<OnboardingDataForm[T]>) => void;
  updateDocuments: (type: "company" | "admin", documents: Partial<CompanyDocuments | AdminDocuments>) => void;
  canProceed: (step?: OnboardingStep) => boolean;
  submitOnboarding: (organizationId: string) => Promise<boolean>;
  clearData: () => void;
  saveToStorage: () => void;
  loadFromStorage: () => void;
}

const STEPS: OnboardingStep[] = ["company", "address", "admin", "banking"];

export function useOnboarding(): UseOnboardingReturn {
  const [state, setState] = useState<OnboardingState>({
    currentStep: "company",
    completedSteps: [],
    data: {
      companyData: {
        name: "",
        cnpj: "",
        legalName: "",
        tradeName: "",
        email: "",
        phone: "",
        website: "",
      },
      addressData: {
        zipCode: "",
        street: "",
        number: "",
        complement: "",
        neighborhood: "",
        city: "",
        state: "",
        country: "Brasil",
      },
      adminData: {
        fullName: "",
        cpf: "",
        email: "",
        phone: "",
        birthDate: "",
        position: "",
      },
      bankingData: {
        bank: "",
        accountType: "CHECKING",
        agency: "",
        account: "",
        digit: "",
        holderName: "",
        holderCpf: "",
        pixKey: "",
      },
    },
    documents: {
      company: {},
      admin: {},
    },
    isLoading: false,
    errors: {},
  });

  // Carrega dados do localStorage na inicialização
  useEffect(() => {
    loadFromStorage();
  }, []);

  // Salva dados no localStorage sempre que o estado muda
  useEffect(() => {
    saveToStorage();
  }, [state.data, state.documents]);

  const saveToStorage = useCallback(() => {
    try {
      const dataToSave = {
        data: state.data,
        documents: state.documents,
        completedSteps: state.completedSteps,
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.error("Erro ao salvar dados no localStorage:", error);
    }
  }, [state.data, state.documents, state.completedSteps]);

  const loadFromStorage = useCallback(() => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsed = JSON.parse(savedData);
        setState(prev => ({
          ...prev,
          data: { ...prev.data, ...parsed.data },
          documents: { ...prev.documents, ...parsed.documents },
          completedSteps: parsed.completedSteps || [],
        }));
      }
    } catch (error) {
      console.error("Erro ao carregar dados do localStorage:", error);
    }
  }, []);

  const clearData = useCallback(() => {
    localStorage.removeItem(STORAGE_KEY);
    setState({
      currentStep: "company",
      completedSteps: [],
      data: {
        companyData: {
          name: "",
          cnpj: "",
          legalName: "",
          tradeName: "",
          email: "",
          phone: "",
          website: "",
        },
        addressData: {
          zipCode: "",
          street: "",
          number: "",
          complement: "",
          neighborhood: "",
          city: "",
          state: "",
          country: "Brasil",
        },
        adminData: {
          fullName: "",
          cpf: "",
          email: "",
          phone: "",
          birthDate: "",
          position: "",
        },
        bankingData: {
          bank: "",
          accountType: "CHECKING",
          agency: "",
          account: "",
          digit: "",
          holderName: "",
          holderCpf: "",
          pixKey: "",
        },
      },
      documents: {
        company: {},
        admin: {},
      },
      isLoading: false,
      errors: {},
    });
  }, []);

  const nextStep = useCallback(() => {
    const currentIndex = STEPS.indexOf(state.currentStep);
    if (currentIndex < STEPS.length - 1) {
      const nextStepValue = STEPS[currentIndex + 1];
      setState(prev => ({
        ...prev,
        currentStep: nextStepValue,
        completedSteps: prev.completedSteps.includes(prev.currentStep)
          ? prev.completedSteps
          : [...prev.completedSteps, prev.currentStep],
      }));
    }
  }, [state.currentStep]);

  const prevStep = useCallback(() => {
    const currentIndex = STEPS.indexOf(state.currentStep);
    if (currentIndex > 0) {
      setState(prev => ({
        ...prev,
        currentStep: STEPS[currentIndex - 1],
      }));
    }
  }, [state.currentStep]);

  const goToStep = useCallback((step: OnboardingStep) => {
    setState(prev => ({
      ...prev,
      currentStep: step,
    }));
  }, []);

  const updateData = useCallback(<T extends keyof OnboardingDataForm>(
    section: T,
    data: Partial<OnboardingDataForm[T]>
  ) => {
    console.log('useOnboarding updateData called:', { section, data });
    setState(prev => {
      const newState = {
        ...prev,
        data: {
          ...prev.data,
          [section]: {
            ...prev.data[section],
            ...data,
          },
        },
        errors: {
          ...prev.errors,
          [section]: undefined,
        },
      };
      console.log('useOnboarding new state:', newState);
      return newState;
    });
  }, []);

  const updateDocuments = useCallback((
    type: "company" | "admin",
    documents: Partial<CompanyDocuments | AdminDocuments>
  ) => {
    setState(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [type]: {
          ...prev.documents[type],
          ...documents,
        },
      },
    }));
  }, []);

  const canProceed = useCallback((step?: OnboardingStep): boolean => {
    const currentStepToCheck = step || state.currentStep;
    const data = state.data;

    switch (currentStepToCheck) {
      case "company":
        const companyValid = !!(
          data.companyData?.name &&
          data.companyData?.cnpj &&
          data.companyData?.legalName &&
          data.companyData?.email &&
          data.companyData?.phone
        );
        console.log('canProceed company check:', {
          name: data.companyData?.name,
          cnpj: data.companyData?.cnpj,
          legalName: data.companyData?.legalName,
          email: data.companyData?.email,
          phone: data.companyData?.phone,
          website: data.companyData?.website,
          tradeName: data.companyData?.tradeName,
          isValid: companyValid
        });
        return companyValid;

      case "address":
        return !!(
          data.addressData?.zipCode &&
          data.addressData?.street &&
          data.addressData?.number &&
          data.addressData?.neighborhood &&
          data.addressData?.city &&
          data.addressData?.state
        );

      case "admin":
        return !!(
          data.adminData?.fullName &&
          data.adminData?.cpf &&
          data.adminData?.email &&
          data.adminData?.phone &&
          data.adminData?.birthDate &&
          data.adminData?.position
        );

      case "banking":
        return !!(
          data.bankingData?.bank &&
          data.bankingData?.accountType &&
          data.bankingData?.agency &&
          data.bankingData?.account &&
          data.bankingData?.digit &&
          data.bankingData?.holderName &&
          data.bankingData?.holderCpf &&
          data.bankingData?.pixKey
        );

      default:
        return false;
    }
  }, [state.currentStep, state.data]);

  const submitOnboarding = useCallback(async (organizationId: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, errors: {} }));

    try {
      const submitData: OnboardingSubmitData = {
        ...state.data as OnboardingDataForm,
        organizationId,
        companyDocuments: state.documents.company,
        adminDocuments: state.documents.admin,
      };

      // Aqui você faria a chamada para a API
      const response = await fetch("/api/onboarding", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        throw new Error("Erro ao enviar dados do onboarding");
      }

      toast.success("Onboarding enviado com sucesso! Aguarde a aprovação.");
      clearData();
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      toast.error(`Erro ao enviar onboarding: ${errorMessage}`);
      setState(prev => ({
        ...prev,
        errors: { submit: errorMessage },
      }));
      return false;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.data, state.documents, clearData]);

  return {
    ...state,
    nextStep,
    prevStep,
    goToStep,
    updateData,
    updateDocuments,
    canProceed,
    submitOnboarding,
    clearData,
    saveToStorage,
    loadFromStorage,
  };
}
