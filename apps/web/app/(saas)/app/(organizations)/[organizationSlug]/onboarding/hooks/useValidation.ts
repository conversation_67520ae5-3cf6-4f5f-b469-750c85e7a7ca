"use client";

import { useCallback } from "react";

// Função para validar CPF
export function validateCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, "");
  
  if (cleanCPF.length !== 11) return false;
  
  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false;
  
  // Validação do primeiro dígito verificador
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;
  
  // Validação do segundo dígito verificador
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;
  
  return true;
}

// Função para validar CNPJ
export function validateCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, "");
  
  if (cleanCNPJ.length !== 14) return false;
  
  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false;
  
  // Validação do primeiro dígito verificador
  let sum = 0;
  let weight = 2;
  for (let i = 11; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  let remainder = sum % 11;
  const firstDigit = remainder < 2 ? 0 : 11 - remainder;
  if (firstDigit !== parseInt(cleanCNPJ.charAt(12))) return false;
  
  // Validação do segundo dígito verificador
  sum = 0;
  weight = 2;
  for (let i = 12; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  remainder = sum % 11;
  const secondDigit = remainder < 2 ? 0 : 11 - remainder;
  if (secondDigit !== parseInt(cleanCNPJ.charAt(13))) return false;
  
  return true;
}

// Função para validar email
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Função para validar telefone brasileiro
export function validatePhone(phone: string): boolean {
  const cleanPhone = phone.replace(/\D/g, "");
  return cleanPhone.length === 10 || cleanPhone.length === 11;
}

// Função para validar CEP
export function validateCEP(cep: string): boolean {
  const cleanCEP = cep.replace(/\D/g, "");
  return cleanCEP.length === 8;
}

// Função para validar data de nascimento
export function validateBirthDate(date: string): boolean {
  const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
  if (!dateRegex.test(date)) return false;
  
  const [day, month, year] = date.split("/").map(Number);
  const birthDate = new Date(year, month - 1, day);
  const today = new Date();
  
  // Verifica se a data é válida
  if (birthDate.getDate() !== day || birthDate.getMonth() !== month - 1 || birthDate.getFullYear() !== year) {
    return false;
  }
  
  // Verifica se a pessoa tem pelo menos 18 anos
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 >= 18;
  }
  
  return age >= 18;
}

// Função para validar chave PIX
export function validatePixKey(pixKey: string): { isValid: boolean; type?: string } {
  const cleanKey = pixKey.trim();
  
  if (!cleanKey) return { isValid: false };
  
  // CPF
  if (/^\d{3}\.\d{3}\.\d{3}-\d{2}$/.test(cleanKey) || /^\d{11}$/.test(cleanKey)) {
    const cpf = cleanKey.replace(/\D/g, "");
    return { isValid: validateCPF(cpf), type: "CPF" };
  }
  
  // CNPJ
  if (/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/.test(cleanKey) || /^\d{14}$/.test(cleanKey)) {
    const cnpj = cleanKey.replace(/\D/g, "");
    return { isValid: validateCNPJ(cnpj), type: "CNPJ" };
  }
  
  // Email
  if (validateEmail(cleanKey)) {
    return { isValid: true, type: "Email" };
  }
  
  // Telefone
  if (/^\+55\d{2}\d{8,9}$/.test(cleanKey) || /^\d{2}\d{8,9}$/.test(cleanKey)) {
    return { isValid: true, type: "Telefone" };
  }
  
  // Chave aleatória (UUID)
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(cleanKey)) {
    return { isValid: true, type: "Chave Aleatória" };
  }
  
  return { isValid: false };
}

// Hook principal de validação
export function useValidation() {
  const validateField = useCallback((field: string, value: string): { isValid: boolean; error?: string } => {
    switch (field) {
      case "cpf":
        if (!value.trim()) return { isValid: false, error: "CPF é obrigatório" };
        if (!validateCPF(value)) return { isValid: false, error: "CPF inválido" };
        return { isValid: true };
        
      case "cnpj":
        if (!value.trim()) return { isValid: false, error: "CNPJ é obrigatório" };
        if (!validateCNPJ(value)) return { isValid: false, error: "CNPJ inválido" };
        return { isValid: true };
        
      case "email":
        if (!value.trim()) return { isValid: false, error: "Email é obrigatório" };
        if (!validateEmail(value)) return { isValid: false, error: "Email inválido" };
        return { isValid: true };
        
      case "phone":
        if (!value.trim()) return { isValid: false, error: "Telefone é obrigatório" };
        if (!validatePhone(value)) return { isValid: false, error: "Telefone inválido" };
        return { isValid: true };
        
      case "cep":
        if (!value.trim()) return { isValid: false, error: "CEP é obrigatório" };
        if (!validateCEP(value)) return { isValid: false, error: "CEP inválido" };
        return { isValid: true };
        
      case "birthDate":
        if (!value.trim()) return { isValid: false, error: "Data de nascimento é obrigatória" };
        if (!validateBirthDate(value)) return { isValid: false, error: "Data inválida ou idade menor que 18 anos" };
        return { isValid: true };
        
      case "pixKey":
        if (!value.trim()) return { isValid: false, error: "Chave PIX é obrigatória" };
        const pixValidation = validatePixKey(value);
        if (!pixValidation.isValid) return { isValid: false, error: "Chave PIX inválida" };
        return { isValid: true };
        
      default:
        if (!value.trim()) return { isValid: false, error: "Campo obrigatório" };
        return { isValid: true };
    }
  }, []);
  
  const validateRequired = useCallback((value: string, fieldName: string): { isValid: boolean; error?: string } => {
    if (!value.trim()) {
      return { isValid: false, error: `${fieldName} é obrigatório` };
    }
    return { isValid: true };
  }, []);
  
  return {
    validateField,
    validateRequired,
    validateCPF,
    validateCNPJ,
    validateEmail,
    validatePhone,
    validateCEP,
    validateBirthDate,
    validatePixKey,
  };
}
