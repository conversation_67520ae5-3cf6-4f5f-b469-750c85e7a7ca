"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { CreditCard, Info } from "lucide-react";
import { CPFInput } from "./inputs";
import { bankingDataSchema, type BankingDataForm, ACCOUNT_TYPES } from "../types/onboarding";
import { BRAZILIAN_BANKS, getPopularBanks, getOtherBanks } from "../data/banks";
import { validatePixKey, formatPixKey, detectPixKeyType } from "../utils/formatters";

interface BankingDataProps {
  data: BankingDataForm;
  onDataChange: (data: Partial<BankingDataForm>) => void;
  onValidation?: (isValid: boolean) => void;
}

export function BankingData({ data, onDataChange, onValidation }: BankingDataProps) {
  const [pixKeyType, setPixKeyType] = useState<string | null>(null);
  
  const form = useForm<BankingDataForm>({
    resolver: zodResolver(bankingDataSchema),
    defaultValues: data,
    mode: "onChange",
  });

  const { watch, formState: { isValid }, setValue } = form;

  // Watch all form values and update parent component
  const watchedValues = watch();
  
  // Update parent when form data changes
  React.useEffect(() => {
    onDataChange(watchedValues);
    onValidation?.(isValid);
  }, [watchedValues, isValid, onDataChange, onValidation]);

  // Handle PIX key validation and formatting
  const handlePixKeyChange = (value: string) => {
    const detectedType = detectPixKeyType(value);
    setPixKeyType(detectedType);
    
    if (detectedType) {
      const formattedValue = formatPixKey(value, detectedType);
      setValue("pixKey", formattedValue);
    } else {
      setValue("pixKey", value);
    }
  };

  const popularBanks = getPopularBanks();
  const otherBanks = getOtherBanks();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CreditCard className="w-5 h-5 text-primary" />
          <CardTitle>Dados Bancários</CardTitle>
        </div>
        <CardDescription>
          Informe os dados bancários para recebimento dos pagamentos
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <Form {...form}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="bank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Banco *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o banco" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">
                        Bancos Populares
                      </div>
                      {popularBanks.map((bank) => (
                        <SelectItem key={bank.code} value={bank.code}>
                          {bank.name}
                        </SelectItem>
                      ))}
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-t mt-2 pt-2">
                        Outros Bancos
                      </div>
                      {otherBanks.map((bank) => (
                        <SelectItem key={bank.code} value={bank.code}>
                          {bank.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accountType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Conta *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ACCOUNT_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="agency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Agência *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="0000"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="account"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conta *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="********"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="digit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Dígito *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="0"
                      maxLength={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="holderName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Titular *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nome completo do titular da conta"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="holderCpf"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CPF do Titular *</FormLabel>
                  <FormControl>
                    <CPFInput
                      value={field.value}
                      onChange={field.onChange}
                      showValidation
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="pixKey"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  Chave PIX *
                  {pixKeyType && (
                    <Badge variant="secondary" className="text-xs">
                      {pixKeyType}
                    </Badge>
                  )}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Digite sua chave PIX (CPF, CNPJ, email, telefone ou chave aleatória)"
                    value={field.value}
                    onChange={(e) => handlePixKeyChange(e.target.value)}
                  />
                </FormControl>
                <div className="flex items-start gap-2 text-xs text-muted-foreground">
                  <Info className="w-3 h-3 mt-0.5 flex-shrink-0" />
                  <span>
                    Aceita CPF, CNPJ, email, telefone (+55XXXXXXXXXXX) ou chave aleatória (UUID)
                  </span>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </Form>
      </CardContent>
    </Card>
  );
}
