"use client";

import React from "react";
import { cn } from "@ui/lib";
import { Check } from "lucide-react";
import { ONBOARDING_STEPS, STEP_LABELS, type OnboardingStep } from "../types/onboarding";

interface ProgressBarProps {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  onStepClick?: (step: OnboardingStep) => void;
  className?: string;
}

export function ProgressBar({ 
  currentStep, 
  completedSteps, 
  onStepClick,
  className 
}: ProgressBarProps) {
  const currentStepIndex = ONBOARDING_STEPS.indexOf(currentStep);
  const totalSteps = ONBOARDING_STEPS.length;

  const getStepStatus = (step: OnboardingStep, index: number) => {
    if (completedSteps.includes(step)) {
      return "completed";
    }
    if (step === currentStep) {
      return "current";
    }
    if (index < currentStepIndex) {
      return "completed";
    }
    return "upcoming";
  };

  const getStepStyles = (status: string) => {
    switch (status) {
      case "completed":
        return {
          circle: "bg-primary text-primary-foreground border-primary",
          line: "bg-primary",
          text: "text-primary font-medium",
        };
      case "current":
        return {
          circle: "bg-primary text-primary-foreground border-primary ring-4 ring-primary/20",
          line: "bg-muted",
          text: "text-primary font-medium",
        };
      default:
        return {
          circle: "bg-muted text-muted-foreground border-muted",
          line: "bg-muted",
          text: "text-muted-foreground",
        };
    }
  };

  const progressPercentage = ((currentStepIndex + 1) / totalSteps) * 100;

  return (
    <div className={cn("w-full", className)}>
      {/* Progress bar visual */}
      <div className="relative">
        {/* Background line */}
        <div className="absolute top-5 left-0 right-0 h-0.5 bg-muted" />
        
        {/* Progress line */}
        <div 
          className="absolute top-5 left-0 h-0.5 bg-primary transition-all duration-500 ease-out"
          style={{ width: `${progressPercentage}%` }}
        />

        {/* Steps */}
        <div className="relative flex justify-between">
          {ONBOARDING_STEPS.map((step, index) => {
            const status = getStepStatus(step, index);
            const styles = getStepStyles(status);
            const isClickable = onStepClick && (status === "completed" || status === "current");

            return (
              <div key={step} className="flex flex-col items-center">
                {/* Step circle */}
                <button
                  type="button"
                  onClick={() => isClickable && onStepClick(step)}
                  disabled={!isClickable}
                  className={cn(
                    "relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200",
                    styles.circle,
                    isClickable && "cursor-pointer hover:scale-105",
                    !isClickable && "cursor-default"
                  )}
                >
                  {status === "completed" ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-semibold">{index + 1}</span>
                  )}
                </button>

                {/* Step label */}
                <div className="mt-3 text-center">
                  <p className={cn("text-sm transition-colors", styles.text)}>
                    {STEP_LABELS[step]}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Progress text */}
      <div className="mt-6 text-center">
        <p className="text-sm text-muted-foreground">
          Etapa {currentStepIndex + 1} de {totalSteps}
        </p>
        <div className="mt-2 w-full bg-muted rounded-full h-2">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
}
