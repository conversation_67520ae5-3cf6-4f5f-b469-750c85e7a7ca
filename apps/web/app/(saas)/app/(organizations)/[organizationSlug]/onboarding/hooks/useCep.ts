"use client";

import { useState, useCallback } from "react";
import type { CepResponse } from "../types/onboarding";

interface CepData {
  zipCode: string;
  street: string;
  complement: string;
  neighborhood: string;
  city: string;
  state: string;
}

interface UseCepReturn {
  data: CepData | null;
  isLoading: boolean;
  error: string | null;
  searchCep: (cep: string) => Promise<CepData | null>;
  clearData: () => void;
}

export function useCep(): UseCepReturn {
  const [data, setData] = useState<CepData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchCep = useCallback(async (cep: string): Promise<CepData | null> => {
    const cleanCep = cep.replace(/\D/g, "");
    
    if (cleanCep.length !== 8) {
      setError("CEP deve ter 8 dígitos");
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Primeiro tenta a API ViaCEP
      const viaCepResponse = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
      
      if (viaCepResponse.ok) {
        const viaCepData: CepResponse = await viaCepResponse.json();
        
        if (viaCepData.erro) {
          throw new Error("CEP não encontrado");
        }

        const formattedData: CepData = {
          zipCode: viaCepData.cep.replace(/\D/g, "").replace(/(\d{5})(\d{3})/, "$1-$2"),
          street: viaCepData.logradouro,
          complement: viaCepData.complemento,
          neighborhood: viaCepData.bairro,
          city: viaCepData.localidade,
          state: viaCepData.uf,
        };

        setData(formattedData);
        return formattedData;
      }

      // Se ViaCEP falhar, tenta API alternativa (CEP Aberto)
      const cepAbertoResponse = await fetch(`https://www.cepaberto.com/api/v3/cep?cep=${cleanCep}`, {
        headers: {
          'Authorization': 'Token token=your-token-here', // Substitua por um token real se necessário
        },
      });

      if (cepAbertoResponse.ok) {
        const cepAbertoData = await cepAbertoResponse.json();
        
        const formattedData: CepData = {
          zipCode: cleanCep.replace(/(\d{5})(\d{3})/, "$1-$2"),
          street: cepAbertoData.address || "",
          complement: "",
          neighborhood: cepAbertoData.district || "",
          city: cepAbertoData.city?.name || "",
          state: cepAbertoData.state?.abbreviation || "",
        };

        setData(formattedData);
        return formattedData;
      }

      throw new Error("CEP não encontrado");
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erro ao buscar CEP";
      setError(errorMessage);
      setData(null);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearData = useCallback(() => {
    setData(null);
    setError(null);
  }, []);

  return {
    data,
    isLoading,
    error,
    searchCep,
    clearData,
  };
}

// Hook simplificado para busca rápida de CEP
export function useQuickCepSearch() {
  const [isLoading, setIsLoading] = useState(false);

  const searchCep = useCallback(async (cep: string): Promise<CepData | null> => {
    const cleanCep = cep.replace(/\D/g, "");
    
    if (cleanCep.length !== 8) {
      return null;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
      
      if (!response.ok) {
        return null;
      }

      const data: CepResponse = await response.json();
      
      if (data.erro) {
        return null;
      }

      return {
        zipCode: data.cep.replace(/\D/g, "").replace(/(\d{5})(\d{3})/, "$1-$2"),
        street: data.logradouro,
        complement: data.complemento,
        neighborhood: data.bairro,
        city: data.localidade,
        state: data.uf,
      };
      
    } catch {
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    searchCep,
    isLoading,
  };
}
