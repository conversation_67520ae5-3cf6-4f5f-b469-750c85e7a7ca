"use client";

import { forwardRef, useState } from "react";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Loader2, MapPin } from "lucide-react";
import { formatCEP } from "../../utils/formatters";
import { validateCEP } from "../../hooks/useValidation";
import { useCep } from "../../hooks/useCep";

interface CEPInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  value?: string;
  onChange?: (value: string) => void;
  onAddressFound?: (address: {
    street: string;
    neighborhood: string;
    city: string;
    state: string;
  }) => void;
  onValidation?: (isValid: boolean) => void;
  error?: boolean;
  showValidation?: boolean;
  autoSearch?: boolean;
}

export const CEPInput = forwardRef<HTMLInputElement, CEPInputProps>(
  ({ 
    className, 
    value = "", 
    onChange, 
    onAddressFound,
    onValidation,
    error, 
    showValidation = false,
    autoSearch = true,
    onBlur,
    ...props 
  }, ref) => {
    const [isValid, setIsValid] = useState<boolean | null>(null);
    const { searchCep, isLoading } = useCep();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const formattedValue = formatCEP(e.target.value);
      onChange?.(formattedValue);
      
      // Reset validation state while typing
      if (showValidation && isValid !== null) {
        setIsValid(null);
      }
    };

    const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
      if (value) {
        const valid = validateCEP(value);
        
        if (showValidation) {
          setIsValid(valid);
          onValidation?.(valid);
        }
        
        // Auto search address if CEP is valid
        if (valid && autoSearch) {
          const addressData = await searchCep(value);
          if (addressData) {
            onAddressFound?.({
              street: addressData.street,
              neighborhood: addressData.neighborhood,
              city: addressData.city,
              state: addressData.state,
            });
          }
        }
      }
      onBlur?.(e);
    };

    const getInputClassName = () => {
      let baseClass = "font-mono pr-10";
      
      if (error) {
        baseClass += " border-destructive focus-visible:border-destructive";
      } else if (showValidation && isValid === true) {
        baseClass += " border-green-500 focus-visible:border-green-500";
      } else if (showValidation && isValid === false) {
        baseClass += " border-destructive focus-visible:border-destructive";
      }
      
      return cn(baseClass, className);
    };

    return (
      <div className="relative">
        <Input
          {...props}
          ref={ref}
          type="text"
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="00000-000"
          maxLength={9}
          className={getInputClassName()}
        />
        
        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
          {isLoading && (
            <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
          )}
          
          {!isLoading && (
            <>
              {showValidation && isValid === false && (
                <div className="w-2 h-2 bg-destructive rounded-full" />
              )}
              
              {showValidation && isValid === true && (
                <div className="w-2 h-2 bg-green-500 rounded-full" />
              )}
              
              {!showValidation && (
                <MapPin className="w-4 h-4 text-muted-foreground" />
              )}
            </>
          )}
        </div>
      </div>
    );
  }
);

CEPInput.displayName = "CEPInput";
