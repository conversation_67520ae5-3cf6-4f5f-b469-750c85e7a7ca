// Exemplo de testes para o sistema de onboarding

import { 
  validateCPF, 
  validateCNPJ, 
  validateEmail, 
  validatePhone, 
  validateCEP, 
  validateBirthDate, 
  validatePixKey 
} from "../hooks/useValidation";

import { 
  formatCPF, 
  formatCNPJ, 
  formatCEP, 
  formatPhone, 
  formatDate,
  detectPixKeyType 
} from "../utils/formatters";

describe("Validação de CPF", () => {
  test("deve validar CPF válido", () => {
    expect(validateCPF("123.456.789-09")).toBe(true);
    expect(validateCPF("********909")).toBe(true);
  });

  test("deve rejeitar CPF inválido", () => {
    expect(validateCPF("123.456.789-00")).toBe(false);
    expect(validateCPF("111.111.111-11")).toBe(false);
    expect(validateCPF("123.456.789")).toBe(false);
  });

  test("deve formatar CPF corretamente", () => {
    expect(formatCPF("********909")).toBe("123.456.789-09");
    expect(formatCPF("********9")).toBe("123.456.789");
    expect(formatCPF("123")).toBe("123");
  });
});

describe("Validação de CNPJ", () => {
  test("deve validar CNPJ válido", () => {
    expect(validateCNPJ("11.222.333/0001-81")).toBe(true);
    expect(validateCNPJ("11222333000181")).toBe(true);
  });

  test("deve rejeitar CNPJ inválido", () => {
    expect(validateCNPJ("11.222.333/0001-00")).toBe(false);
    expect(validateCNPJ("11.111.111/1111-11")).toBe(false);
    expect(validateCNPJ("11.222.333/0001")).toBe(false);
  });

  test("deve formatar CNPJ corretamente", () => {
    expect(formatCNPJ("11222333000181")).toBe("11.222.333/0001-81");
    expect(formatCNPJ("1122233300018")).toBe("11.222.333/0001-8");
    expect(formatCNPJ("112223")).toBe("11.222.3");
  });
});

describe("Validação de Email", () => {
  test("deve validar email válido", () => {
    expect(validateEmail("<EMAIL>")).toBe(true);
    expect(validateEmail("<EMAIL>")).toBe(true);
  });

  test("deve rejeitar email inválido", () => {
    expect(validateEmail("invalid-email")).toBe(false);
    expect(validateEmail("@domain.com")).toBe(false);
    expect(validateEmail("user@")).toBe(false);
  });
});

describe("Validação de Telefone", () => {
  test("deve validar telefone válido", () => {
    expect(validatePhone("(11) 99999-9999")).toBe(true);
    expect(validatePhone("(11) 9999-9999")).toBe(true);
  });

  test("deve rejeitar telefone inválido", () => {
    expect(validatePhone("(11) 999-999")).toBe(false);
    expect(validatePhone("11 99999-9999")).toBe(false);
  });

  test("deve formatar telefone corretamente", () => {
    expect(formatPhone("11999999999")).toBe("(11) 99999-9999");
    expect(formatPhone("1199999999")).toBe("(11) 9999-9999");
    expect(formatPhone("11")).toBe("(11");
  });
});

describe("Validação de CEP", () => {
  test("deve validar CEP válido", () => {
    expect(validateCEP("01234-567")).toBe(true);
    expect(validateCEP("01234567")).toBe(true);
  });

  test("deve rejeitar CEP inválido", () => {
    expect(validateCEP("0123-567")).toBe(false);
    expect(validateCEP("01234-56")).toBe(false);
  });

  test("deve formatar CEP corretamente", () => {
    expect(formatCEP("01234567")).toBe("01234-567");
    expect(formatCEP("01234")).toBe("01234");
  });
});

describe("Validação de Data de Nascimento", () => {
  test("deve validar data válida para maior de idade", () => {
    const date18YearsAgo = new Date();
    date18YearsAgo.setFullYear(date18YearsAgo.getFullYear() - 20);
    const dateString = date18YearsAgo.toLocaleDateString("pt-BR");
    
    expect(validateBirthDate(dateString)).toBe(true);
  });

  test("deve rejeitar data para menor de idade", () => {
    const date10YearsAgo = new Date();
    date10YearsAgo.setFullYear(date10YearsAgo.getFullYear() - 10);
    const dateString = date10YearsAgo.toLocaleDateString("pt-BR");
    
    expect(validateBirthDate(dateString)).toBe(false);
  });

  test("deve formatar data corretamente", () => {
    expect(formatDate("01011990")).toBe("01/01/1990");
    expect(formatDate("0101")).toBe("01/01");
    expect(formatDate("01")).toBe("01");
  });
});

describe("Validação de Chave PIX", () => {
  test("deve validar CPF como chave PIX", () => {
    const result = validatePixKey("123.456.789-09");
    expect(result.isValid).toBe(true);
    expect(result.type).toBe("CPF");
  });

  test("deve validar CNPJ como chave PIX", () => {
    const result = validatePixKey("11.222.333/0001-81");
    expect(result.isValid).toBe(true);
    expect(result.type).toBe("CNPJ");
  });

  test("deve validar email como chave PIX", () => {
    const result = validatePixKey("<EMAIL>");
    expect(result.isValid).toBe(true);
    expect(result.type).toBe("Email");
  });

  test("deve validar telefone como chave PIX", () => {
    const result = validatePixKey("+5511999999999");
    expect(result.isValid).toBe(true);
    expect(result.type).toBe("Telefone");
  });

  test("deve detectar tipo de chave PIX", () => {
    expect(detectPixKeyType("123.456.789-09")).toBe("CPF");
    expect(detectPixKeyType("11.222.333/0001-81")).toBe("CNPJ");
    expect(detectPixKeyType("<EMAIL>")).toBe("Email");
    expect(detectPixKeyType("+5511999999999")).toBe("Telefone");
    expect(detectPixKeyType("550e8400-e29b-41d4-a716-446655440000")).toBe("Chave Aleatória");
  });
});

describe("Integração de Componentes", () => {
  test("deve validar dados completos da empresa", () => {
    const companyData = {
      name: "Empresa Teste Ltda",
      cnpj: "11.222.333/0001-81",
      legalName: "Empresa Teste Ltda",
      tradeName: "Teste",
      email: "<EMAIL>",
      phone: "(11) 99999-9999",
      website: "https://teste.com"
    };

    expect(validateCNPJ(companyData.cnpj)).toBe(true);
    expect(validateEmail(companyData.email)).toBe(true);
    expect(validatePhone(companyData.phone)).toBe(true);
  });

  test("deve validar dados completos do administrador", () => {
    const adminData = {
      fullName: "João Silva",
      cpf: "123.456.789-09",
      email: "<EMAIL>",
      phone: "(11) 88888-8888",
      birthDate: "01/01/1980",
      position: "CEO"
    };

    expect(validateCPF(adminData.cpf)).toBe(true);
    expect(validateEmail(adminData.email)).toBe(true);
    expect(validatePhone(adminData.phone)).toBe(true);
    expect(validateBirthDate(adminData.birthDate)).toBe(true);
  });

  test("deve validar dados bancários completos", () => {
    const bankingData = {
      bank: "001",
      accountType: "CHECKING" as const,
      agency: "1234",
      account: "********",
      digit: "9",
      holderName: "João Silva",
      holderCpf: "123.456.789-09",
      pixKey: "<EMAIL>"
    };

    expect(validateCPF(bankingData.holderCpf)).toBe(true);
    expect(validatePixKey(bankingData.pixKey).isValid).toBe(true);
  });
});

// Mock para testes de API
export const mockOnboardingData = {
  organizationId: "org-123",
  companyData: {
    name: "Empresa Teste Ltda",
    cnpj: "11.222.333/0001-81",
    legalName: "Empresa Teste Ltda ME",
    tradeName: "Teste",
    email: "<EMAIL>",
    phone: "(11) 99999-9999",
    website: "https://teste.com"
  },
  addressData: {
    zipCode: "01234-567",
    street: "Rua Teste",
    number: "123",
    complement: "Sala 1",
    neighborhood: "Centro",
    city: "São Paulo",
    state: "SP",
    country: "Brasil"
  },
  adminData: {
    fullName: "João Silva",
    cpf: "123.456.789-09",
    email: "<EMAIL>",
    phone: "(11) 88888-8888",
    birthDate: "01/01/1980",
    position: "CEO"
  },
  bankingData: {
    bank: "001",
    accountType: "CHECKING" as const,
    agency: "1234",
    account: "********",
    digit: "9",
    holderName: "João Silva",
    holderCpf: "123.456.789-09",
    pixKey: "<EMAIL>"
  },
  companyDocuments: {},
  adminDocuments: {}
};
