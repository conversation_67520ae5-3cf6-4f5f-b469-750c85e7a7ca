import type { BankOption } from "../types/onboarding";

export const BRAZILIAN_BANKS: BankOption[] = [
  { code: "001", name: "Banco do Brasil", fullName: "Banco do Brasil S.A." },
  { code: "033", name: "<PERSON><PERSON>", fullName: "Banco <PERSON>nder (Brasil) S.A." },
  { code: "104", name: "Caixa Econômica Federal", fullName: "Caixa Econômica Federal" },
  { code: "237", name: "Bradesco", fullName: "Banco Bradesco S.A." },
  { code: "341", name: "Itaú", fullName: "Itaú Unibanco S.A." },
  { code: "260", name: "Nu Pagamentos", fullName: "Nu Pagamentos S.A." },
  { code: "290", name: "PagSeguro", fullName: "PagSeguro Digital Ltd." },
  { code: "323", name: "Mercado <PERSON>go", fullName: "Mercado <PERSON>go S.A." },
  { code: "077", name: "Inter", fullName: "Banco Inter S.A." },
  { code: "212", name: "Original", fullName: "Banco Original S.A." },
  { code: "336", name: "C6 Bank", fullName: "Banco C6 S.A." },
  { code: "655", name: "Neon", fullName: "Neon Pagamentos S.A." },
  { code: "364", name: "Gerencianet", fullName: "Gerencianet Pagamentos do Brasil Ltda." },
  { code: "380", name: "PicPay", fullName: "PicPay Servicos S.A." },
  { code: "208", name: "BTG Pactual", fullName: "Banco BTG Pactual S.A." },
  { code: "041", name: "Banrisul", fullName: "Banco do Estado do Rio Grande do Sul S.A." },
  { code: "070", name: "BRB", fullName: "BRB - Banco de Brasília S.A." },
  { code: "085", name: "Ailos", fullName: "Cooperativa Central de Crédito Ailos" },
  { code: "097", name: "Credisis", fullName: "Cooperativa Central de Crédito Noroeste Brasileiro Ltda." },
  { code: "099", name: "Uniprime", fullName: "Uniprime Norte do Paraná - Cooperativa de Crédito Ltda." },
  { code: "136", name: "Unicred", fullName: "Confederação Nacional das Cooperativas Centrais Unicred Ltda." },
  { code: "151", name: "Nossa Caixa", fullName: "Nossa Caixa Nosso Banco S.A." },
  { code: "184", name: "Itaú BBA", fullName: "Itaú BBA S.A." },
  { code: "218", name: "Bonsucesso", fullName: "Banco Bonsucesso S.A." },
  { code: "224", name: "Fibra", fullName: "Banco Fibra S.A." },
  { code: "233", name: "Next", fullName: "Banco Next S.A." },
  { code: "246", name: "ABC Brasil", fullName: "Banco ABC Brasil S.A." },
  { code: "249", name: "Investcred", fullName: "Banco Investcred Unibanco S.A." },
  { code: "254", name: "Parana Banco", fullName: "Parana Banco S.A." },
  { code: "265", name: "Fator", fullName: "Banco Fator S.A." },
  { code: "266", name: "Cédula", fullName: "Banco Cédula S.A." },
  { code: "300", name: "La Nación", fullName: "Banco de la Nación Argentina" },
  { code: "318", name: "BMG", fullName: "Banco BMG S.A." },
  { code: "320", name: "CCB Brasil", fullName: "China Construction Bank (Brasil) Banco Múltiplo S.A." },
  { code: "356", name: "ABN Amro", fullName: "ABN Amro Bank" },
  { code: "366", name: "Société Générale", fullName: "Société Générale Brasil S.A." },
  { code: "370", name: "Mizuho", fullName: "Mizuho Bank" },
  { code: "376", name: "J.P. Morgan", fullName: "J.P. Morgan S.A." },
  { code: "389", name: "Mercantil do Brasil", fullName: "Banco Mercantil do Brasil S.A." },
  { code: "394", name: "BMC", fullName: "Banco BMC S.A." },
  { code: "399", name: "Kirton", fullName: "Kirton Bank S.A." },
  { code: "412", name: "Capital", fullName: "Banco Capital S.A." },
  { code: "422", name: "Safra", fullName: "Banco Safra S.A." },
  { code: "456", name: "MUFG", fullName: "Banco MUFG Brasil S.A." },
  { code: "464", name: "Sumitomo Mitsui", fullName: "Sumitomo Mitsui Brasileiro S.A." },
  { code: "473", name: "Caixa Geral", fullName: "Caixa Geral de Depósitos" },
  { code: "477", name: "Citibank", fullName: "Citibank N.A." },
  { code: "479", name: "Boston", fullName: "Bank of America Merrill Lynch" },
  { code: "487", name: "Deutsche", fullName: "Deutsche Bank S.A." },
  { code: "488", name: "JPMorgan Chase", fullName: "JPMorgan Chase Bank" },
  { code: "492", name: "ING", fullName: "ING Bank N.V." },
  { code: "495", name: "La Provincia", fullName: "Banco de la Provincia de Buenos Aires" },
  { code: "505", name: "Credit Suisse", fullName: "Credit Suisse (Brasil) S.A." },
  { code: "545", name: "Senso", fullName: "Senso Corretora de Cambio e Valores Mobiliarios S.A." },
  { code: "600", name: "Luso Brasileiro", fullName: "Banco Luso Brasileiro S.A." },
  { code: "604", name: "Industrial", fullName: "Banco Industrial do Brasil S.A." },
  { code: "610", name: "VR", fullName: "Banco VR S.A." },
  { code: "611", name: "Paulista", fullName: "Banco Paulista S.A." },
  { code: "612", name: "Guanabara", fullName: "Banco Guanabara S.A." },
  { code: "613", name: "Omni", fullName: "Omni Banco S.A." },
  { code: "623", name: "Pan", fullName: "Banco Pan S.A." },
  { code: "626", name: "Ficsa", fullName: "Banco Ficsa S.A." },
  { code: "630", name: "Intercap", fullName: "Banco Intercap S.A." },
  { code: "633", name: "Rendimento", fullName: "Banco Rendimento S.A." },
  { code: "634", name: "Triângulo", fullName: "Banco Triângulo S.A." },
  { code: "637", name: "Sofisa", fullName: "Banco Sofisa S.A." },
  { code: "641", name: "Alvorada", fullName: "Banco Alvorada S.A." },
  { code: "643", name: "Pine", fullName: "Banco Pine S.A." },
  { code: "652", name: "Itaú Holding", fullName: "Itaú Unibanco Holding S.A." },
  { code: "653", name: "Indusval", fullName: "Banco Indusval S.A." },
  { code: "654", name: "A.J. Renner", fullName: "Banco A.J. Renner S.A." },
  { code: "658", name: "Porto Real", fullName: "Banco Porto Real S.A." },
  { code: "707", name: "Daycoval", fullName: "Banco Daycoval S.A." },
  { code: "712", name: "Ourinvest", fullName: "Banco Ourinvest S.A." },
  { code: "739", name: "Cetelem", fullName: "Banco Cetelem S.A." },
  { code: "741", name: "Ribeirão Preto", fullName: "Banco Ribeirão Preto S.A." },
  { code: "743", name: "Semear", fullName: "Banco Semear S.A." },
  { code: "745", name: "Citibank", fullName: "Banco Citibank S.A." },
  { code: "746", name: "Modal", fullName: "Banco Modal S.A." },
  { code: "747", name: "Rabobank", fullName: "Rabobank International Brasil S.A." },
  { code: "748", name: "Sicredi", fullName: "Banco Cooperativo Sicredi S.A." },
  { code: "752", name: "BNP Paribas", fullName: "BNP Paribas Brasil S.A." },
  { code: "753", name: "NBC", fullName: "NBC Bank Brasil S.A." },
  { code: "755", name: "Bofa Merrill Lynch", fullName: "Bank of America Merrill Lynch" },
  { code: "756", name: "Sicoob", fullName: "Banco Cooperativo do Brasil S.A." },
  { code: "757", name: "KEB", fullName: "KEB Hana Bank Brasil S.A." },
];

// Função para buscar banco por código
export function getBankByCode(code: string): BankOption | undefined {
  return BRAZILIAN_BANKS.find(bank => bank.code === code);
}

// Função para buscar bancos por nome (busca parcial)
export function searchBanksByName(query: string): BankOption[] {
  const searchTerm = query.toLowerCase();
  return BRAZILIAN_BANKS.filter(bank => 
    bank.name.toLowerCase().includes(searchTerm) ||
    bank.fullName.toLowerCase().includes(searchTerm)
  );
}

// Bancos mais populares (para mostrar no topo da lista)
export const POPULAR_BANKS = [
  "001", // Banco do Brasil
  "033", // Santander
  "104", // Caixa Econômica Federal
  "237", // Bradesco
  "341", // Itaú
  "260", // Nu Pagamentos
  "077", // Inter
  "336", // C6 Bank
];

export function getPopularBanks(): BankOption[] {
  return POPULAR_BANKS.map(code => getBankByCode(code)).filter(Boolean) as BankOption[];
}

export function getOtherBanks(): BankOption[] {
  return BRAZILIAN_BANKS.filter(bank => !POPULAR_BANKS.includes(bank.code));
}
