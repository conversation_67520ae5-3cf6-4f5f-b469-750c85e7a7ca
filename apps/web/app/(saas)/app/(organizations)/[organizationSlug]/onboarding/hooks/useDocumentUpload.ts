"use client";

import { useState, useCallback } from "react";
import { useToast } from "@ui/hooks/use-toast";
import { getSignedUploadUrl, STORAGE_CONFIG } from "@repo/storage";
import type { DocumentFile } from "../types/onboarding";

interface UseDocumentUploadReturn {
  uploadFile: (
    file: File,
    category: "company" | "admin",
    type: string
  ) => Promise<DocumentFile | null>;
  isUploading: boolean;
  uploadProgress: Record<string, number>;
}

export function useDocumentUpload(): UseDocumentUploadReturn {
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const uploadFile = useCallback(async (
    file: File,
    category: "company" | "admin",
    type: string
  ): Promise<DocumentFile | null> => {
    try {
      setIsUploading(true);
      const fileKey = `${category}-${type}`;

      // Validar tipo de arquivo
      if (!STORAGE_CONFIG.allowedTypes.includes(file.type)) {
        toast({
          title: "Tipo de arquivo inválido",
          description: "Apenas arquivos JPG, PNG e PDF são permitidos.",
          variant: "destructive",
        });
        return null;
      }

      // Validar tamanho
      if (file.size > STORAGE_CONFIG.maxFileSize) {
        toast({
          title: "Arquivo muito grande",
          description: `O arquivo deve ter no máximo ${STORAGE_CONFIG.maxFileSize / 1024 / 1024}MB.`,
          variant: "destructive",
        });
        return null;
      }

      // Gerar nome único para o arquivo
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const fileName = `${category}/${type}/${timestamp}.${fileExtension}`;

      // Simular progresso de upload
      setUploadProgress(prev => ({ ...prev, [fileKey]: 0 }));

      // Obter URL assinada para upload
      const uploadUrl = await getSignedUploadUrl(fileName, {
        bucket: STORAGE_CONFIG.buckets.onboardingDocuments,
      });

      // Simular progresso
      setUploadProgress(prev => ({ ...prev, [fileKey]: 50 }));

      // Upload do arquivo
      const response = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!response.ok) {
        throw new Error("Falha no upload do arquivo");
      }

      // Simular progresso final
      setUploadProgress(prev => ({ ...prev, [fileKey]: 100 }));

      // Criar objeto DocumentFile
      const documentFile: DocumentFile = {
        file,
        name: file.name,
        type: file.type,
        size: file.size,
        url: uploadUrl.split('?')[0], // URL sem parâmetros de assinatura
      };

      toast({
        title: "Upload realizado com sucesso!",
        description: `${file.name} foi enviado com sucesso.`,
        variant: "default",
      });

      return documentFile;

    } catch (error) {
      console.error("Erro no upload:", error);
      toast({
        title: "Erro no upload",
        description: error instanceof Error ? error.message : "Falha ao enviar o arquivo",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsUploading(false);
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[`${category}-${type}`];
        return newProgress;
      });
    }
  }, [toast]);

  return {
    uploadFile,
    isUploading,
    uploadProgress,
  };
}
