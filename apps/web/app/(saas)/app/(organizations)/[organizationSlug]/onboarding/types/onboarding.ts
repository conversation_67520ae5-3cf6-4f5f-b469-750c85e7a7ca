import { z } from "zod";

// Tipos base para os dados do onboarding
export interface CompanyData {
  name: string;
  cnpj: string;
  legalName: string;
  tradeName?: string;
  email: string;
  phone: string;
  website?: string;
}

export interface AddressData {
  zipCode: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  country: string;
}

export interface AdminData {
  fullName: string;
  cpf: string;
  email: string;
  phone: string;
  birthDate: string;
  position: string;
}

export interface BankingData {
  bank: string;
  accountType: "CHECKING" | "SAVINGS";
  agency: string;
  account: string;
  digit: string;
  holderName: string;
  holderCpf: string;
  pixKey: string;
}

export interface DocumentFile {
  file: File;
  name: string;
  type: string;
  size: number;
  url?: string;
}

export interface CompanyDocuments {
  socialContract?: DocumentFile;
  cnpjCard?: DocumentFile;
  addressProof?: DocumentFile;
}

export interface AdminDocuments {
  rg?: DocumentFile;
  cpf?: DocumentFile;
  addressProof?: DocumentFile;
}

export interface OnboardingData {
  companyData: CompanyData;
  addressData: AddressData;
  adminData: AdminData;
  bankingData: BankingData;
  companyDocuments: CompanyDocuments;
  adminDocuments: AdminDocuments;
}

// Schemas de validação com Zod
export const companyDataSchema = z.object({
  name: z.string().min(1, "Nome da empresa é obrigatório").max(100, "Nome deve ter no máximo 100 caracteres"),
  cnpj: z.string().min(1, "CNPJ é obrigatório").refine((val) => {
    const cleaned = val.replace(/\D/g, '');
    return cleaned.length === 14;
  }, "CNPJ deve ter 14 dígitos"),
  legalName: z.string().min(1, "Razão social é obrigatória").max(200, "Razão social deve ter no máximo 200 caracteres"),
  tradeName: z.string().max(100, "Nome fantasia deve ter no máximo 100 caracteres").optional(),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido").refine((val) => {
    const cleaned = val.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 11;
  }, "Telefone deve ter 10 ou 11 dígitos"),
  website: z.string().optional().refine((val) => !val || val === "" || z.string().url().safeParse(val).success, {
    message: "URL inválida"
  }),
});

export const addressDataSchema = z.object({
  zipCode: z.string().min(9, "CEP inválido").regex(/^\d{5}-\d{3}$/, "Formato de CEP inválido"),
  street: z.string().min(1, "Logradouro é obrigatório").max(200, "Logradouro deve ter no máximo 200 caracteres"),
  number: z.string().min(1, "Número é obrigatório").max(20, "Número deve ter no máximo 20 caracteres"),
  complement: z.string().max(100, "Complemento deve ter no máximo 100 caracteres").optional(),
  neighborhood: z.string().min(1, "Bairro é obrigatório").max(100, "Bairro deve ter no máximo 100 caracteres"),
  city: z.string().min(1, "Cidade é obrigatória").max(100, "Cidade deve ter no máximo 100 caracteres"),
  state: z.string().min(2, "Estado é obrigatório").max(2, "Estado deve ter 2 caracteres"),
  country: z.string().default("Brasil"),
});

export const adminDataSchema = z.object({
  fullName: z.string().min(1, "Nome completo é obrigatório").max(200, "Nome deve ter no máximo 200 caracteres"),
  cpf: z.string().min(14, "CPF inválido").regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, "Formato de CPF inválido"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(14, "Telefone inválido").regex(/^\(\d{2}\) \d{4,5}-\d{4}$/, "Formato de telefone inválido"),
  birthDate: z.string().min(10, "Data de nascimento é obrigatória").regex(/^\d{2}\/\d{2}\/\d{4}$/, "Formato de data inválido"),
  position: z.string().min(1, "Cargo é obrigatório").max(100, "Cargo deve ter no máximo 100 caracteres"),
});

export const bankingDataSchema = z.object({
  bank: z.string().min(1, "Banco é obrigatório"),
  accountType: z.enum(["CHECKING", "SAVINGS"], { required_error: "Tipo de conta é obrigatório" }),
  agency: z.string().min(1, "Agência é obrigatória").max(10, "Agência deve ter no máximo 10 caracteres"),
  account: z.string().min(1, "Conta é obrigatória").max(20, "Conta deve ter no máximo 20 caracteres"),
  digit: z.string().min(1, "Dígito verificador é obrigatório").max(2, "Dígito deve ter no máximo 2 caracteres"),
  holderName: z.string().min(1, "Nome do titular é obrigatório").max(200, "Nome deve ter no máximo 200 caracteres"),
  holderCpf: z.string().min(14, "CPF do titular inválido").regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, "Formato de CPF inválido"),
  pixKey: z.string().min(1, "Chave PIX é obrigatória").max(200, "Chave PIX deve ter no máximo 200 caracteres"),
});

export const onboardingDataSchema = z.object({
  companyData: companyDataSchema,
  addressData: addressDataSchema,
  adminData: adminDataSchema,
  bankingData: bankingDataSchema,
});

// Tipos derivados dos schemas
export type CompanyDataForm = z.infer<typeof companyDataSchema>;
export type AddressDataForm = z.infer<typeof addressDataSchema>;
export type AdminDataForm = z.infer<typeof adminDataSchema>;
export type BankingDataForm = z.infer<typeof bankingDataSchema>;
export type OnboardingDataForm = z.infer<typeof onboardingDataSchema>;

// Estados do onboarding
export type OnboardingStep = "company" | "address" | "admin" | "banking";

export interface OnboardingState {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  data: Partial<OnboardingDataForm>;
  documents: {
    company: CompanyDocuments;
    admin: AdminDocuments;
  };
  isLoading: boolean;
  errors: Record<string, string>;
}

// Tipos para APIs
export interface CepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  erro?: boolean;
}

export interface BankOption {
  code: string;
  name: string;
  fullName: string;
}

export interface OnboardingSubmitData extends OnboardingDataForm {
  organizationId: string;
  companyDocuments: CompanyDocuments;
  adminDocuments: AdminDocuments;
}

// Constantes
export const ONBOARDING_STEPS: OnboardingStep[] = ["company", "address", "admin", "banking"];

export const STEP_LABELS: Record<OnboardingStep, string> = {
  company: "Dados da Empresa",
  address: "Endereço",
  admin: "Administrador",
  banking: "Dados Bancários",
};

export const ACCOUNT_TYPES = [
  { value: "CHECKING", label: "Conta Corrente" },
  { value: "SAVINGS", label: "Conta Poupança" },
] as const;

export const BRAZILIAN_STATES = [
  { value: "AC", label: "Acre" },
  { value: "AL", label: "Alagoas" },
  { value: "AP", label: "Amapá" },
  { value: "AM", label: "Amazonas" },
  { value: "BA", label: "Bahia" },
  { value: "CE", label: "Ceará" },
  { value: "DF", label: "Distrito Federal" },
  { value: "ES", label: "Espírito Santo" },
  { value: "GO", label: "Goiás" },
  { value: "MA", label: "Maranhão" },
  { value: "MT", label: "Mato Grosso" },
  { value: "MS", label: "Mato Grosso do Sul" },
  { value: "MG", label: "Minas Gerais" },
  { value: "PA", label: "Pará" },
  { value: "PB", label: "Paraíba" },
  { value: "PR", label: "Paraná" },
  { value: "PE", label: "Pernambuco" },
  { value: "PI", label: "Piauí" },
  { value: "RJ", label: "Rio de Janeiro" },
  { value: "RN", label: "Rio Grande do Norte" },
  { value: "RS", label: "Rio Grande do Sul" },
  { value: "RO", label: "Rondônia" },
  { value: "RR", label: "Roraima" },
  { value: "SC", label: "Santa Catarina" },
  { value: "SP", label: "São Paulo" },
  { value: "SE", label: "Sergipe" },
  { value: "TO", label: "Tocantins" },
] as const;
