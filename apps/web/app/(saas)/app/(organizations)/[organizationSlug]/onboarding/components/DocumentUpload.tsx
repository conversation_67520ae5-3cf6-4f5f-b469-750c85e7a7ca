"use client";

import React, { useState, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Progress } from "@ui/components/progress";
import {
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertCircle,
  Building2,
  User,
  Loader2
} from "lucide-react";
import { useToast } from "@ui/hooks/use-toast";
import { useDocumentUpload } from "../hooks/useDocumentUpload";
import type { CompanyDocuments, AdminDocuments, DocumentFile } from "../types/onboarding";

interface DocumentUploadProps {
  companyDocuments: CompanyDocuments;
  adminDocuments: AdminDocuments;
  onCompanyDocumentChange: (type: keyof CompanyDocuments, file: File | null) => void;
  onAdminDocumentChange: (type: keyof AdminDocuments, file: File | null) => void;
}


const DOCUMENT_TYPES = {
  company: [
    {
      key: "socialContract" as keyof CompanyDocuments,
      label: "Contrato Social",
      description: "Documento de constituição da empresa",
      required: true,
    },
    {
      key: "cnpjCard" as keyof CompanyDocuments,
      label: "Cartão CNPJ",
      description: "Cartão de inscrição no CNPJ",
      required: true,
    },
    {
      key: "addressProof" as keyof CompanyDocuments,
      label: "Comprovante de Endereço",
      description: "Comprovante de endereço da empresa",
      required: true,
    },
  ],
  admin: [
    {
      key: "rg" as keyof AdminDocuments,
      label: "RG",
      description: "Carteira de identidade",
      required: true,
    },
    {
      key: "cpf" as keyof AdminDocuments,
      label: "CPF",
      description: "Cadastro de pessoa física",
      required: true,
    },
    {
      key: "addressProof" as keyof AdminDocuments,
      label: "Comprovante de Endereço",
      description: "Comprovante de endereço do administrador",
      required: true,
    },
  ],
};

export function DocumentUpload({
  companyDocuments,
  adminDocuments,
  onCompanyDocumentChange,
  onAdminDocumentChange,
}: DocumentUploadProps) {
  const { toast } = useToast();
  const { uploadFile, isUploading, uploadProgress } = useDocumentUpload();

  const handleFileUpload = useCallback(async (
    file: File,
    type: string,
    category: "company" | "admin"
  ) => {
    const documentFile = await uploadFile(file, category, type);

    if (documentFile) {
      // Atualizar estado
      if (category === "company") {
        onCompanyDocumentChange(type as keyof CompanyDocuments, documentFile);
      } else {
        onAdminDocumentChange(type as keyof AdminDocuments, documentFile);
      }
    }
  }, [uploadFile, onCompanyDocumentChange, onAdminDocumentChange]);

  const handleFileSelect = useCallback((
    event: React.ChangeEvent<HTMLInputElement>,
    type: string,
    category: "company" | "admin"
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    handleFileUpload(file, type, category);
  }, [handleFileUpload]);

  const handleFileRemove = useCallback((
    type: string,
    category: "company" | "admin"
  ) => {
    if (category === "company") {
      onCompanyDocumentChange(type as keyof CompanyDocuments, null);
    } else {
      onAdminDocumentChange(type as keyof AdminDocuments, null);
    }
  }, [onCompanyDocumentChange, onAdminDocumentChange]);

  const renderDocumentSection = (
    title: string,
    icon: React.ReactNode,
    documents: CompanyDocuments | AdminDocuments,
    documentTypes: typeof DOCUMENT_TYPES.company | typeof DOCUMENT_TYPES.admin,
    category: "company" | "admin"
  ) => (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          {icon}
          <CardTitle>{title}</CardTitle>
        </div>
        <CardDescription>
          Faça upload dos documentos necessários para aprovação da conta
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {documentTypes.map((docType) => {
          const document = documents[docType.key];
          const isUploaded = !!document;
          const isRequired = docType.required;

          return (
            <div
              key={docType.key}
              className="border rounded-lg p-4 space-y-3"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">
                    {docType.label}
                    {isRequired && <span className="text-red-500 ml-1">*</span>}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {docType.description}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {isUploaded ? (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Enviado</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">Pendente</span>
                    </div>
                  )}
                </div>
              </div>

              {isUploaded ? (
                <div className="flex items-center justify-between bg-green-50 p-3 rounded-md">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium">{document.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({(document.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFileRemove(docType.key, category)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-muted-foreground/50 transition-colors">
                  <input
                    type="file"
                    id={`${category}-${docType.key}`}
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={(e) => handleFileSelect(e, docType.key, category)}
                    className="hidden"
                    disabled={isUploading}
                  />
                  <label
                    htmlFor={`${category}-${docType.key}`}
                    className="cursor-pointer flex flex-col items-center gap-2"
                  >
                    {isUploading ? (
                      <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                    ) : (
                      <Upload className="w-8 h-8 text-muted-foreground" />
                    )}
                    <div>
                      <p className="text-sm font-medium">
                        {isUploading ? "Enviando..." : "Clique para enviar"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        JPG, PNG ou PDF (máx. 10MB)
                      </p>
                    </div>
                  </label>
            </div>
              )}

              {uploadProgress[`${category}-${docType.key}`] && (
                <div className="space-y-2">
                  <Progress value={uploadProgress[`${category}-${docType.key}`]} />
                  <p className="text-xs text-center text-muted-foreground">
                    Enviando... {uploadProgress[`${category}-${docType.key}`]}%
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Todos os documentos são obrigatórios para aprovação da conta.
          Certifique-se de que os arquivos estejam legíveis e dentro do prazo de validade.
        </AlertDescription>
      </Alert>

      {renderDocumentSection(
        "Documentos da Empresa",
        <Building2 className="w-5 h-5 text-primary" />,
        companyDocuments,
        DOCUMENT_TYPES.company,
        "company"
      )}

      {renderDocumentSection(
        "Documentos do Administrador",
        <User className="w-5 h-5 text-primary" />,
        adminDocuments,
        DOCUMENT_TYPES.admin,
        "admin"
      )}
    </div>
  );
}
