"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { User } from "lucide-react";
import { CPFInput, PhoneInput, DateInput } from "./inputs";
import { adminDataSchema, type AdminDataForm } from "../types/onboarding";

interface AdminDataProps {
  data: AdminDataForm;
  onDataChange: (data: Partial<AdminDataForm>) => void;
  onValidation?: (isValid: boolean) => void;
}

export function AdminData({ data, onDataChange, onValidation }: AdminDataProps) {
  const form = useForm<AdminDataForm>({
    resolver: zodResolver(adminDataSchema),
    defaultValues: data,
    mode: "onChange",
  });

  const { watch, formState: { isValid } } = form;

  // Watch all form values and update parent component
  const watchedValues = watch();
  
  // Update parent when form data changes
  React.useEffect(() => {
    onDataChange(watchedValues);
    onValidation?.(isValid);
  }, [watchedValues, isValid, onDataChange, onValidation]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <User className="w-5 h-5 text-primary" />
          <CardTitle>Dados do Administrador</CardTitle>
        </div>
        <CardDescription>
          Informe os dados do responsável legal pela empresa
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <Form {...form}>
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome Completo *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Digite o nome completo do administrador"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="cpf"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CPF *</FormLabel>
                  <FormControl>
                    <CPFInput
                      value={field.value}
                      onChange={field.onChange}
                      showValidation
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="birthDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Nascimento *</FormLabel>
                  <FormControl>
                    <DateInput
                      value={field.value}
                      onChange={field.onChange}
                      showValidation
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Pessoal *</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telefone Pessoal *</FormLabel>
                  <FormControl>
                    <PhoneInput
                      value={field.value}
                      onChange={field.onChange}
                      showValidation
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="position"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cargo na Empresa *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Ex: CEO, Diretor, Sócio-administrador"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </Form>
      </CardContent>
    </Card>
  );
}
