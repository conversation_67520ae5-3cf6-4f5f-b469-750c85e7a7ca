"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Loader2, ChevronLeft, ChevronRight, Send, AlertCircle } from "lucide-react";
import { useOnboarding } from "../hooks/useOnboarding";
import { ProgressBar } from "./ProgressBar";
import { CompanyData } from "./CompanyData";
import { AddressData } from "./AddressData";
import { AdminData } from "./AdminData";
import { BankingData } from "./BankingData";
import { DocumentUpload } from "./DocumentUpload";
import type { OnboardingStep, CompanyDocuments, AdminDocuments } from "../types/onboarding";

interface OnboardingFormProps {
  organizationId: string;
  onComplete?: () => void;
}

export function OnboardingForm({ organizationId, onComplete }: OnboardingFormProps) {
  const [stepValidations, setStepValidations] = useState<Record<OnboardingStep, boolean>>({
    company: false,
    address: false,
    admin: false,
    banking: false,
  });

  const {
    currentStep,
    completedSteps,
    data,
    documents,
    isLoading,
    errors,
    nextStep,
    prevStep,
    goToStep,
    updateData,
    updateDocuments,
    canProceed,
    submitOnboarding,
  } = useOnboarding();

  const handleStepValidation = (step: OnboardingStep, isValid: boolean) => {
    console.log(`Step validation for ${step}:`, isValid);
    setStepValidations(prev => ({
      ...prev,
      [step]: isValid,
    }));
  };

  const handleCompanyDocumentChange = (type: keyof CompanyDocuments, file: File | null) => {
    if (file) {
      const documentFile = {
        file,
        name: file.name,
        type: file.type,
        size: file.size,
      };
      updateDocuments("company", { [type]: documentFile });
    } else {
      const updatedDocs = { ...documents.company };
      delete updatedDocs[type];
      updateDocuments("company", updatedDocs);
    }
  };

  const handleAdminDocumentChange = (type: keyof AdminDocuments, file: File | null) => {
    if (file) {
      const documentFile = {
        file,
        name: file.name,
        type: file.type,
        size: file.size,
      };
      updateDocuments("admin", { [type]: documentFile });
    } else {
      const updatedDocs = { ...documents.admin };
      delete updatedDocs[type];
      updateDocuments("admin", updatedDocs);
    }
  };

  const handleNext = () => {
    if (canProceed()) {
      nextStep();
    }
  };

  const handlePrevious = () => {
    prevStep();
  };

  const handleSubmit = async () => {
    const success = await submitOnboarding(organizationId);
    if (success) {
      onComplete?.();
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "company":
        return (
          <CompanyData
            data={data.companyData}
            onDataChange={(newData) => updateData("companyData", newData)}
            onValidation={(isValid) => handleStepValidation("company", isValid)}
            onNext={handleNext}
            onPrevious={handlePrevious}
            isFirstStep={isFirstStep}
            isLastStep={isLastStep}
            canProceed={canProceedToNext}
            isLoading={isLoading}
          />
        );

      case "address":
        return (
          <AddressData
            data={data.addressData}
            onDataChange={(newData) => updateData("addressData", newData)}
            onValidation={(isValid) => handleStepValidation("address", isValid)}
          />
        );

      case "admin":
        return (
          <AdminData
            data={data.adminData}
            onDataChange={(newData) => updateData("adminData", newData)}
            onValidation={(isValid) => handleStepValidation("admin", isValid)}
          />
        );

      case "banking":
        return (
          <BankingData
            data={data.bankingData}
            onDataChange={(newData) => updateData("bankingData", newData)}
            onValidation={(isValid) => handleStepValidation("banking", isValid)}
          />
        );

      default:
        return null;
    }
  };

  const isFirstStep = currentStep === "company";
  const isLastStep = currentStep === "banking";
  const canProceedToNext = canProceed() && stepValidations[currentStep];

  console.log('OnboardingForm debug:', {
    currentStep,
    canProceed: canProceed(),
    stepValidations,
    canProceedToNext,
    companyData: data.companyData
  });

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Progress Bar */}
      <ProgressBar
        currentStep={currentStep}
        completedSteps={completedSteps}
        onStepClick={goToStep}
      />

      {/* Error Display */}
      {errors.submit && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errors.submit}</AlertDescription>
        </Alert>
      )}

      {/* Current Step Content */}
      <div className="space-y-6">
        {renderCurrentStep()}

        {/* Document Upload - Show on last step */}
        {isLastStep && (
          <DocumentUpload
            companyDocuments={documents.company}
            adminDocuments={documents.admin}
            onCompanyDocumentChange={handleCompanyDocumentChange}
            onAdminDocumentChange={handleAdminDocumentChange}
          />
        )}
      </div>

    </div>
  );
}
