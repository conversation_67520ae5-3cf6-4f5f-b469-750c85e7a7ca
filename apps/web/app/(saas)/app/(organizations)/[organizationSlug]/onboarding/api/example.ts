// Exemplo de integração com API backend para o sistema de onboarding

import type { OnboardingSubmitData } from "../types/onboarding";

/**
 * Exemplo de função para enviar dados do onboarding para o backend
 */
export async function submitOnboardingData(data: OnboardingSubmitData): Promise<{
  success: boolean;
  message: string;
  onboardingId?: string;
}> {
  try {
    // Preparar FormData para upload de arquivos
    const formData = new FormData();
    
    // Adicionar dados JSON
    formData.append("data", JSON.stringify({
      organizationId: data.organizationId,
      companyData: data.companyData,
      addressData: data.addressData,
      adminData: data.adminData,
      bankingData: data.bankingData,
    }));

    // Adicionar documentos da empresa
    if (data.companyDocuments.socialContract?.file) {
      formData.append("company_social_contract", data.companyDocuments.socialContract.file);
    }
    if (data.companyDocuments.cnpjCard?.file) {
      formData.append("company_cnpj_card", data.companyDocuments.cnpjCard.file);
    }
    if (data.companyDocuments.addressProof?.file) {
      formData.append("company_address_proof", data.companyDocuments.addressProof.file);
    }

    // Adicionar documentos do administrador
    if (data.adminDocuments.rg?.file) {
      formData.append("admin_rg", data.adminDocuments.rg.file);
    }
    if (data.adminDocuments.cpf?.file) {
      formData.append("admin_cpf", data.adminDocuments.cpf.file);
    }
    if (data.adminDocuments.addressProof?.file) {
      formData.append("admin_address_proof", data.adminDocuments.addressProof.file);
    }

    // Enviar para o backend
    const response = await fetch("/api/onboarding", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao enviar dados");
    }

    const result = await response.json();
    
    return {
      success: true,
      message: "Onboarding enviado com sucesso!",
      onboardingId: result.onboardingId,
    };

  } catch (error) {
    console.error("Erro ao enviar onboarding:", error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : "Erro desconhecido",
    };
  }
}

/**
 * Exemplo de função para verificar status do onboarding
 */
export async function checkOnboardingStatus(organizationId: string): Promise<{
  status: "PENDING" | "APPROVED" | "REJECTED" | "INCOMPLETE";
  message?: string;
  rejectionReason?: string;
}> {
  try {
    const response = await fetch(`/api/onboarding/status/${organizationId}`);
    
    if (!response.ok) {
      throw new Error("Erro ao verificar status");
    }

    return await response.json();

  } catch (error) {
    console.error("Erro ao verificar status:", error);
    
    return {
      status: "INCOMPLETE",
      message: "Erro ao verificar status do onboarding",
    };
  }
}

/**
 * Exemplo de função para upload de arquivo individual
 */
export async function uploadDocument(
  file: File,
  documentType: string,
  organizationId: string
): Promise<{
  success: boolean;
  url?: string;
  message: string;
}> {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("documentType", documentType);
    formData.append("organizationId", organizationId);

    const response = await fetch("/api/onboarding/upload", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro no upload");
    }

    const result = await response.json();
    
    return {
      success: true,
      url: result.url,
      message: "Arquivo enviado com sucesso!",
    };

  } catch (error) {
    console.error("Erro no upload:", error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : "Erro no upload",
    };
  }
}

/**
 * Exemplo de schema de resposta da API
 */
export interface OnboardingApiResponse {
  success: boolean;
  data?: {
    onboardingId: string;
    status: "PENDING" | "APPROVED" | "REJECTED";
    createdAt: string;
    updatedAt: string;
    reviewNotes?: string;
  };
  error?: {
    code: string;
    message: string;
    details?: Record<string, string[]>;
  };
}

/**
 * Exemplo de validação no backend (schema esperado)
 */
export const backendValidationSchema = {
  companyData: {
    name: { required: true, maxLength: 100 },
    cnpj: { required: true, format: "cnpj" },
    legalName: { required: true, maxLength: 200 },
    tradeName: { required: false, maxLength: 100 },
    email: { required: true, format: "email" },
    phone: { required: true, format: "phone" },
    website: { required: false, format: "url" },
  },
  addressData: {
    zipCode: { required: true, format: "cep" },
    street: { required: true, maxLength: 200 },
    number: { required: true, maxLength: 20 },
    complement: { required: false, maxLength: 100 },
    neighborhood: { required: true, maxLength: 100 },
    city: { required: true, maxLength: 100 },
    state: { required: true, length: 2 },
    country: { required: true, default: "Brasil" },
  },
  adminData: {
    fullName: { required: true, maxLength: 200 },
    cpf: { required: true, format: "cpf" },
    email: { required: true, format: "email" },
    phone: { required: true, format: "phone" },
    birthDate: { required: true, format: "date", minAge: 18 },
    position: { required: true, maxLength: 100 },
  },
  bankingData: {
    bank: { required: true },
    accountType: { required: true, enum: ["CHECKING", "SAVINGS"] },
    agency: { required: true, maxLength: 10 },
    account: { required: true, maxLength: 20 },
    digit: { required: true, maxLength: 2 },
    holderName: { required: true, maxLength: 200 },
    holderCpf: { required: true, format: "cpf" },
    pixKey: { required: true, format: "pix" },
  },
  documents: {
    company: {
      socialContract: { required: true, type: "pdf", maxSize: "5MB" },
      cnpjCard: { required: true, type: "pdf", maxSize: "5MB" },
      addressProof: { required: true, type: "pdf", maxSize: "5MB" },
    },
    admin: {
      rg: { required: true, type: "pdf", maxSize: "5MB" },
      cpf: { required: true, type: "pdf", maxSize: "5MB" },
      addressProof: { required: true, type: "pdf", maxSize: "5MB" },
    },
  },
};

/**
 * Exemplo de webhook para notificações de status
 */
export interface OnboardingWebhookPayload {
  event: "onboarding.approved" | "onboarding.rejected" | "onboarding.pending_review";
  organizationId: string;
  onboardingId: string;
  status: "APPROVED" | "REJECTED" | "PENDING";
  timestamp: string;
  data?: {
    rejectionReason?: string;
    approvedBy?: string;
    reviewNotes?: string;
  };
}

/**
 * Exemplo de função para processar webhook
 */
export function handleOnboardingWebhook(payload: OnboardingWebhookPayload) {
  switch (payload.event) {
    case "onboarding.approved":
      // Ativar conta, enviar email de boas-vindas, etc.
      console.log(`Onboarding aprovado para ${payload.organizationId}`);
      break;
      
    case "onboarding.rejected":
      // Enviar email com motivo da rejeição, permitir reenvio, etc.
      console.log(`Onboarding rejeitado para ${payload.organizationId}: ${payload.data?.rejectionReason}`);
      break;
      
    case "onboarding.pending_review":
      // Notificar que está em análise
      console.log(`Onboarding em análise para ${payload.organizationId}`);
      break;
  }
}
