# 🚀 Sistema de Onboarding - SupGateway

## 📋 Visão Geral

Sistema completo de onboarding para empresas que desejam ativar pagamentos no SupGateway. O sistema coleta dados da empresa, endereço, administrador e dados bancários, além de permitir upload de documentos necessários para aprovação.

## ✨ Funcionalidades Implementadas

### ✅ **Fluxo Completo de Onboarding**

- **4 Etapas Sequenciais**: Dados da empresa, endereço, administrador e dados bancários
- **Validação em Tempo Real**: Feedback imediato para o usuário
- **Navegação Inteligente**: Botões habilitados apenas quando dados são válidos
- **Persistência Local**: Dados salvos automaticamente no localStorage

### ✅ **Busca Automática por CNPJ**

- **Integração com API**: Busca automática de dados da empresa via CNPJ
- **Preenchimento Inteligente**: Dados preenchidos automaticamente quando encontrados
- **Fallback Manual**: Opção de preenchimento manual se a busca falhar
- **Validação Brasileira**: CNPJ, CPF, CEP com validação de dígitos verificadores

### ✅ **Sistema de Upload de Documentos**

- **Integração com Storage**: Upload seguro usando sistema @storage/
- **6 Tipos de Documentos**: Contrato social, CNPJ, RG, CPF, comprovantes de endereço
- **Validação de Arquivos**: Apenas JPG, PNG e PDF até 10MB
- **Progress Bar**: Indicador visual de progresso do upload
- **Drag & Drop**: Interface intuitiva para upload

### ✅ **Validação Robusta**

- **Schemas Zod**: Validação completa com mensagens em português
- **Formatação Automática**: CNPJ, CPF, telefone e CEP formatados automaticamente
- **Validação de Email**: Verificação de formato de email
- **Validação de URL**: Website opcional com validação de URL

## 🏗️ Arquitetura

### Estrutura de Arquivos

```
onboarding/
├── components/           # Componentes React
│   ├── OnboardingForm.tsx      # Formulário principal
│   ├── CompanyData.tsx         # Dados da empresa
│   ├── CNPJSearch.tsx          # Busca por CNPJ
│   ├── DocumentUpload.tsx      # Upload de documentos
│   ├── AddressData.tsx         # Dados de endereço
│   ├── AdminData.tsx           # Dados do administrador
│   ├── BankingData.tsx         # Dados bancários
│   └── ProgressBar.tsx         # Barra de progresso
├── hooks/               # Hooks customizados
│   ├── useOnboarding.ts        # Hook principal do onboarding
│   └── useDocumentUpload.ts    # Hook para upload de documentos
├── types/               # Tipos TypeScript
│   └── onboarding.ts           # Tipos e schemas
├── utils/               # Utilitários
│   └── formatters.ts           # Formatadores brasileiros
└── page.tsx            # Página principal
```

### Componentes Principais

#### OnboardingForm
```tsx
<OnboardingForm
  organizationId="org-123"
  onComplete={() => router.push('/dashboard')}
/>
```

#### CNPJSearch
```tsx
<CNPJSearch
  onDataFound={(data) => updateCompanyData(data)}
  onSkip={() => setManualForm(true)}
  onValidation={(isValid) => setStepValid(isValid)}
/>
```

#### DocumentUpload
```tsx
<DocumentUpload
  companyDocuments={documents.company}
  adminDocuments={documents.admin}
  onCompanyDocumentChange={handleCompanyDocument}
  onAdminDocumentChange={handleAdminDocument}
/>
```

## 🔧 Configuração

### 1. Variáveis de Ambiente

```env
# API de CNPJ
NEXT_PUBLIC_CNPJ_API_AUTOCOMPLETE=your_api_key

# Storage S3
S3_ENDPOINT=your_s3_endpoint
S3_REGION=your_region
S3_ACCESS_KEY_ID=your_access_key
S3_SECRET_ACCESS_KEY=your_secret_key
```

### 2. Dependências

```json
{
  "react-hook-form": "^7.48.0",
  "@hookform/resolvers": "^3.3.0",
  "zod": "^3.22.0",
  "lucide-react": "^0.294.0",
  "sonner": "^1.2.0"
}
```

## 📝 Uso

### 1. Página de Onboarding

```tsx
// app/(saas)/app/(organizations)/[organizationSlug]/onboarding/page.tsx
export default function OnboardingPage({ params }: OnboardingPageProps) {
  const { organizationSlug } = params;

  return (
    <div className="space-y-8">
      <OnboardingForm
        organizationId={organizationSlug}
        onComplete={() => router.push(`/app/${organizationSlug}/dashboard`)}
      />
    </div>
  );
}
```

### 2. Hook useOnboarding

```tsx
const {
  currentStep,
  data,
  documents,
  canProceed,
  nextStep,
  prevStep,
  updateData,
  updateDocuments,
  submitOnboarding
} = useOnboarding();
```

### 3. Upload de Documentos

```tsx
const { uploadFile, isUploading, uploadProgress } = useDocumentUpload();

const handleUpload = async (file: File) => {
  const document = await uploadFile(file, "company", "socialContract");
  if (document) {
    updateDocuments("company", { socialContract: document });
  }
};
```

## 🎨 Estilização

O sistema usa Shadcn UI com Tailwind CSS:

- **Cards**: Para seções de formulário
- **Alerts**: Para mensagens de erro e sucesso
- **Progress**: Para barra de progresso e upload
- **Buttons**: Com estados de loading e disabled
- **Form**: Componentes de formulário com validação

## 🔒 Segurança

- **Validação Client-Side**: Schemas Zod para validação
- **Validação Server-Side**: Validação no backend (implementar)
- **Upload Seguro**: URLs assinadas para upload
- **Sanitização**: Dados sanitizados antes do envio

## 🚀 Próximos Passos

1. **Implementar API Backend**: Endpoints para salvar dados do onboarding
2. **Validação Server-Side**: Validação completa no backend
3. **Notificações**: Sistema de notificações para aprovação
4. **Dashboard Admin**: Interface para aprovar/rejeitar onboarding
5. **Testes**: Testes unitários e de integração

## 📊 Monitoramento

- **Logs de Debug**: Console logs para debugging
- **Toast Notifications**: Feedback visual para o usuário
- **Error Handling**: Tratamento robusto de erros
- **Loading States**: Estados de carregamento em todas as operações

## 🤝 Contribuição

Para contribuir com o sistema de onboarding:

1. Siga os padrões de código estabelecidos
2. Use TypeScript para tipagem
3. Implemente testes para novas funcionalidades
4. Documente mudanças no README
5. Use commits semânticos

---

**Desenvolvido com ❤️ para SupGateway**
