"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Alert, AlertDescription } from "@ui/components/alert";
import { CheckCircle, Clock, FileText, Shield, Info } from "lucide-react";
import { OnboardingForm } from "./components/OnboardingForm";

interface OnboardingPageProps {
  params: {
    organizationSlug: string;
  };
}

export default function OnboardingPage({ params }: OnboardingPageProps) {
  const { organizationSlug } = params;
  const router = useRouter();

  // TODO: Verificar se o usuário tem permissão para acessar esta organização
  // TODO: Verificar se o onboarding já foi concluído
  // TODO: Buscar dados da organização

  const handleOnboardingComplete = () => {
    // Redirecionar para o dashboard após conclusão
    router.push(`/app/${organizationSlug}/dashboard`);
  };

  return (
    <div className="space-y-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-foreground mb-3">
            Configure sua Conta de Pagamentos
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Complete as informações da sua empresa para ativar os pagamentos.
          </p>
        </div>


        {/* Onboarding Form */}
        <OnboardingForm
          organizationId={organizationSlug}
          onComplete={handleOnboardingComplete}
        />

    </div>
  );
}
