"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Building2 } from "lucide-react";
import { CNPJInput, PhoneInput } from "./inputs";
import { companyDataSchema, type CompanyDataForm } from "../types/onboarding";
import { CNPJSearch } from "./CNPJSearch";

interface CompanyDataProps {
  data: CompanyDataForm;
  onDataChange: (data: Partial<CompanyDataForm>) => void;
  onValidation?: (isValid: boolean) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstStep?: boolean;
  isLastStep?: boolean;
  canProceed?: boolean;
  isLoading?: boolean;
}

export function CompanyData({
  data,
  onDataChange,
  onValidation,
  onNext,
  onPrevious,
  isFirstStep = false,
  isLastStep = false,
  canProceed = false,
  isLoading = false
}: CompanyDataProps) {
  const [useManualForm, setUseManualForm] = useState(false);

  const form = useForm<CompanyDataForm>({
    resolver: zodResolver(companyDataSchema),
    defaultValues: data,
    mode: "onChange",
  });

  const { watch, formState: { isValid } } = form;

  // Watch all form values and update parent component
  const watchedValues = watch();

  // Update parent when form data changes
  React.useEffect(() => {
    console.log('CompanyData form changed:', {
      isValid,
      errors: form.formState.errors,
      values: watchedValues,
      touchedFields: form.formState.touchedFields
    });
    onDataChange(watchedValues);
    onValidation?.(isValid);
  }, [watchedValues, isValid, onDataChange, onValidation, form.formState.errors, form.formState.touchedFields]);

  // Handler para quando dados são encontrados via CNPJ
  const handleDataFound = (foundData: any) => {
    console.log('CompanyData handleDataFound called with:', foundData);

    // Mapear os dados encontrados para o formato esperado
    const mappedData: Partial<CompanyDataForm> = {
      name: foundData.name,
      cnpj: foundData.cnpj,
      legalName: foundData.legalName,
      tradeName: foundData.tradeName,
      email: foundData.email,
      phone: foundData.phone,
      website: foundData.website,
    };

    console.log('CompanyData mappedData:', mappedData);

    // Atualizar o formulário com os dados encontrados
    form.reset({ ...data, ...mappedData });

    // Atualizar os dados no hook useOnboarding
    onDataChange(mappedData);

    // Forçar validação após reset
    setTimeout(() => {
      form.trigger();
      console.log('CompanyData form validation after reset:', {
        isValid: form.formState.isValid,
        errors: form.formState.errors
      });
    }, 100);

    // Também atualizar o endereço se estiver disponível
    if (foundData.address) {
      // Aqui você pode atualizar os dados de endereço se necessário
      // Por enquanto, vamos apenas atualizar os dados da empresa
    }
  };

  // Handler para pular a busca e usar formulário manual
  const handleSkipSearch = () => {
    setUseManualForm(true);
  };

  // Se não está usando formulário manual, mostrar busca por CNPJ
  if (!useManualForm) {
    return (
      <CNPJSearch
        onDataFound={handleDataFound}
        onSkip={handleSkipSearch}
        showNavigationButtons={false}
        onValidation={onValidation}
        onNext={onNext}
        onPrevious={onPrevious}
        isFirstStep={isFirstStep}
        isLastStep={isLastStep}
        canProceed={canProceed}
        isLoading={isLoading}
      />
    );
  }

  // Formulário manual
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Building2 className="w-5 h-5 text-primary" />
          <CardTitle>Dados da Empresa</CardTitle>
        </div>
        <CardDescription>
          Informe os dados básicos da sua empresa para identificação
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <Form {...form}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Empresa *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Digite o nome da empresa"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cnpj"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CNPJ *</FormLabel>
                  <FormControl>
                    <CNPJInput
                      value={field.value}
                      onChange={field.onChange}
                      showValidation
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="legalName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Razão Social *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Digite a razão social da empresa"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tradeName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome Fantasia</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Digite o nome fantasia (opcional)"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Corporativo *</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telefone *</FormLabel>
                  <FormControl>
                    <PhoneInput
                      value={field.value}
                      onChange={field.onChange}
                      showValidation
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Site da Empresa</FormLabel>
                <FormControl>
                  <Input
                    type="url"
                    placeholder="https://www.empresa.com (opcional)"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </Form>
      </CardContent>
    </Card>
  );
}
