"use client";

import { forwardRef, useState } from "react";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Phone } from "lucide-react";
import { formatPhone } from "../../utils/formatters";
import { validatePhone } from "../../hooks/useValidation";

interface PhoneInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  value?: string;
  onChange?: (value: string) => void;
  onValidation?: (isValid: boolean) => void;
  error?: boolean;
  showValidation?: boolean;
}

export const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ 
    className, 
    value = "", 
    onChange, 
    onValidation,
    error, 
    showValidation = false,
    onBlur,
    ...props 
  }, ref) => {
    const [isValid, setIsValid] = useState<boolean | null>(null);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const formattedValue = formatPhone(e.target.value);
      onChange?.(formattedValue);
      
      // Reset validation state while typing
      if (showValidation && isValid !== null) {
        setIsValid(null);
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      if (showValidation && value) {
        const valid = validatePhone(value);
        setIsValid(valid);
        onValidation?.(valid);
      }
      onBlur?.(e);
    };

    const getInputClassName = () => {
      let baseClass = "font-mono pl-10";
      
      if (error) {
        baseClass += " border-destructive focus-visible:border-destructive";
      } else if (showValidation && isValid === true) {
        baseClass += " border-green-500 focus-visible:border-green-500";
      } else if (showValidation && isValid === false) {
        baseClass += " border-destructive focus-visible:border-destructive";
      }
      
      return cn(baseClass, className);
    };

    return (
      <div className="relative">
        <div className="absolute left-3 top-1/2 -translate-y-1/2">
          <Phone className="w-4 h-4 text-muted-foreground" />
        </div>
        
        <Input
          {...props}
          ref={ref}
          type="tel"
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="(00) 00000-0000"
          maxLength={15}
          className={getInputClassName()}
        />
        
        {showValidation && isValid === false && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-destructive rounded-full" />
          </div>
        )}
        
        {showValidation && isValid === true && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
          </div>
        )}
      </div>
    );
  }
);

PhoneInput.displayName = "PhoneInput";
