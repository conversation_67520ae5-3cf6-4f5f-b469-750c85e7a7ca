"use client";

import { forwardRef, useState } from "react";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { formatCPF } from "../../utils/formatters";
import { validateCPF } from "../../hooks/useValidation";

interface CPFInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  value?: string;
  onChange?: (value: string) => void;
  onValidation?: (isValid: boolean) => void;
  error?: boolean;
  showValidation?: boolean;
}

export const CPFInput = forwardRef<HTMLInputElement, CPFInputProps>(
  ({ 
    className, 
    value = "", 
    onChange, 
    onValidation,
    error, 
    showValidation = false,
    onBlur,
    ...props 
  }, ref) => {
    const [isValid, setIsValid] = useState<boolean | null>(null);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const formattedValue = formatCPF(e.target.value);
      onChange?.(formattedValue);
      
      // Reset validation state while typing
      if (showValidation && isValid !== null) {
        setIsValid(null);
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      if (showValidation && value) {
        const valid = validateCPF(value);
        setIsValid(valid);
        onValidation?.(valid);
      }
      onBlur?.(e);
    };

    const getInputClassName = () => {
      let baseClass = "font-mono";
      
      if (error) {
        baseClass += " border-destructive focus-visible:border-destructive";
      } else if (showValidation && isValid === true) {
        baseClass += " border-green-500 focus-visible:border-green-500";
      } else if (showValidation && isValid === false) {
        baseClass += " border-destructive focus-visible:border-destructive";
      }
      
      return cn(baseClass, className);
    };

    return (
      <div className="relative">
        <Input
          {...props}
          ref={ref}
          type="text"
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="000.000.000-00"
          maxLength={14}
          className={getInputClassName()}
        />
        
        {showValidation && isValid === false && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-destructive rounded-full" />
          </div>
        )}
        
        {showValidation && isValid === true && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
          </div>
        )}
      </div>
    );
  }
);

CPFInput.displayName = "CPFInput";
