"use client";

import { forwardRef, useState } from "react";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Calendar } from "lucide-react";
import { formatDate } from "../../utils/formatters";
import { validateBirthDate } from "../../hooks/useValidation";

interface DateInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  value?: string;
  onChange?: (value: string) => void;
  onValidation?: (isValid: boolean) => void;
  error?: boolean;
  showValidation?: boolean;
  minAge?: number;
}

export const DateInput = forwardRef<HTMLInputElement, DateInputProps>(
  ({ 
    className, 
    value = "", 
    onChange, 
    onValidation,
    error, 
    showValidation = false,
    minAge = 18,
    onBlur,
    ...props 
  }, ref) => {
    const [isValid, setIsValid] = useState<boolean | null>(null);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const formattedValue = formatDate(e.target.value);
      onChange?.(formattedValue);
      
      // Reset validation state while typing
      if (showValidation && isValid !== null) {
        setIsValid(null);
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      if (showValidation && value) {
        const valid = validateBirthDate(value);
        setIsValid(valid);
        onValidation?.(valid);
      }
      onBlur?.(e);
    };

    const getInputClassName = () => {
      let baseClass = "font-mono pl-10";
      
      if (error) {
        baseClass += " border-destructive focus-visible:border-destructive";
      } else if (showValidation && isValid === true) {
        baseClass += " border-green-500 focus-visible:border-green-500";
      } else if (showValidation && isValid === false) {
        baseClass += " border-destructive focus-visible:border-destructive";
      }
      
      return cn(baseClass, className);
    };

    return (
      <div className="relative">
        <div className="absolute left-3 top-1/2 -translate-y-1/2">
          <Calendar className="w-4 h-4 text-muted-foreground" />
        </div>
        
        <Input
          {...props}
          ref={ref}
          type="text"
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="DD/MM/AAAA"
          maxLength={10}
          className={getInputClassName()}
        />
        
        {showValidation && isValid === false && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-destructive rounded-full" />
          </div>
        )}
        
        {showValidation && isValid === true && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
          </div>
        )}
      </div>
    );
  }
);

DateInput.displayName = "DateInput";
