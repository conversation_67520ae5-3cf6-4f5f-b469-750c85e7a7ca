"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { SearchIcon, Building2, AlertTriangleIcon, CheckCircle, ChevronLeft, ChevronRight, Loader2, Send } from "lucide-react";
import { useToast } from "@ui/hooks/use-toast";

// Schema para busca de CNPJ
const cnpjSearchSchema = z.object({
  cnpj: z.string().min(14, "CNPJ deve ter pelo menos 14 caracteres"),
});

// Schema para dados da empresa (usando o mesmo schema do onboarding)
const companyDataSchema = z.object({
  name: z.string().min(1, "Nome da empresa é obrigatório").max(100, "Nome deve ter no máximo 100 caracteres"),
  cnpj: z.string().min(1, "CNPJ é obrigatório").refine((val) => {
    const cleaned = val.replace(/\D/g, '');
    return cleaned.length === 14;
  }, "CNPJ deve ter 14 dígitos"),
  legalName: z.string().min(1, "Razão social é obrigatória").max(200, "Razão social deve ter no máximo 200 caracteres"),
  tradeName: z.string().max(100, "Nome fantasia deve ter no máximo 100 caracteres").optional(),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido").refine((val) => {
    const cleaned = val.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 11;
  }, "Telefone deve ter 10 ou 11 dígitos"),
  website: z.string().optional().refine((val) => !val || val === "" || z.string().url().safeParse(val).success, {
    message: "URL inválida"
  }),
});

type CnpjSearchValues = z.infer<typeof cnpjSearchSchema>;
type CompanyDataValues = z.infer<typeof companyDataSchema>;

interface CNPJInfo {
  cnpj: string;
  razao_social: string;
  nome_fantasia: string;
  email: string;
  telefone1: string;
  endereco: {
    logradouro: string;
    numero: string;
    complemento: string;
    bairro: string;
    cep: string;
    uf: string;
    municipio: string;
  };
}

interface CNPJSearchProps {
  onDataFound: (data: CompanyDataValues) => void;
  onSkip: () => void;
  showNavigationButtons?: boolean;
  onValidation?: (isValid: boolean) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstStep?: boolean;
  isLastStep?: boolean;
  canProceed?: boolean;
  isLoading?: boolean;
}

export function CNPJSearch({
  onDataFound,
  onSkip,
  showNavigationButtons = true,
  onValidation,
  onNext,
  onPrevious,
  isFirstStep = false,
  isLastStep = false,
  canProceed = false,
  isLoading = false
}: CNPJSearchProps) {
  const { toast } = useToast();
  const [isSearching, setIsSearching] = useState(false);
  const [searchStep, setSearchStep] = useState(true);
  const [foundData, setFoundData] = useState<CompanyDataValues | null>(null);

  // Formulário para busca de CNPJ
  const searchForm = useForm<CnpjSearchValues>({
    resolver: zodResolver(cnpjSearchSchema),
    defaultValues: {
      cnpj: "",
    },
  });

  // Formulário para dados da empresa
  const companyForm = useForm<CompanyDataValues>({
    resolver: zodResolver(companyDataSchema),
    defaultValues: {
      name: "",
      legalName: "",
      tradeName: "",
      cnpj: "",
      email: "",
      phone: "",
      website: "",
    },
  });

  const { watch, formState: { isValid } } = companyForm;

  // Watch all form values and update parent component
  const watchedValues = watch();

  // Update validation when form changes
  React.useEffect(() => {
    console.log('CNPJSearch validation changed:', {
      isValid,
      errors: companyForm.formState.errors,
      values: watchedValues,
      touchedFields: companyForm.formState.touchedFields,
      dirtyFields: companyForm.formState.dirtyFields
    });
    onValidation?.(isValid);
  }, [isValid, onValidation, companyForm.formState.errors, watchedValues, companyForm.formState.touchedFields, companyForm.formState.dirtyFields]);

  // Função para formatar CNPJ
  const formatCNPJ = (cnpj: string) => {
    const cleaned = cnpj.replace(/\D/g, '');
    return cleaned.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
  };

  // Função para formatar telefone
  const formatPhone = (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 11) {
      return cleaned.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3');
    } else if (cleaned.length === 10) {
      return cleaned.replace(/^(\d{2})(\d{4})(\d{4})$/, '($1) $2-$3');
    }
    // Se não conseguir formatar, retorna o telefone original
    return phone;
  };

  // Função para buscar informações do CNPJ
  const searchCNPJ = async (cnpj: string) => {
    setIsSearching(true);
    try {
      // Limpar caracteres especiais do CNPJ
      const cleanCnpj = cnpj.replace(/[^\d]/g, "");

      // Buscar na API - usando a mesma API da Pluggou
      const response = await fetch(`https://api.invertexto.com/v1/cnpj/${cleanCnpj}?token=${process.env.NEXT_PUBLIC_CNPJ_API_AUTOCOMPLETE || "19220|CM7hhF41PYSAsSJpdqo3czVc74neDrLj"}`);

      if (!response.ok) {
        throw new Error("Não foi possível consultar o CNPJ. Tente novamente ou preencha manualmente.");
      }

      const data: CNPJInfo = await response.json();

      // Montar endereço completo
      const fullAddress = `${data.endereco.logradouro} ${data.endereco.numero}${data.endereco.complemento ? ', ' + data.endereco.complemento : ''}, ${data.endereco.bairro}`;

      // Preparar dados para o formulário
      const companyData: CompanyDataValues = {
        name: data.nome_fantasia || data.razao_social,
        legalName: data.razao_social,
        tradeName: data.nome_fantasia || "",
        cnpj: formatCNPJ(cleanCnpj),
        email: data.email || "<EMAIL>",
        phone: formatPhone(data.telefone1 || "11999999999"),
        website: "",
      };

      // Preencher o formulário
      console.log('Dados formatados para o formulário:', companyData);
      companyForm.reset(companyData);
      setFoundData(companyData);
      setSearchStep(false);

      // Verificar se o formulário é válido após reset
      setTimeout(() => {
        const isValid = companyForm.formState.isValid;
        console.log('Formulário válido após reset:', isValid);
        console.log('Erros do formulário:', companyForm.formState.errors);
      }, 100);

      toast({
        title: "CNPJ encontrado!",
        description: "Os dados da empresa foram preenchidos automaticamente. Verifique e complete as informações necessárias.",
        variant: "default",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro na consulta",
        description: error instanceof Error ? error.message : "Ocorreu um erro ao consultar o CNPJ",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Handler para o formulário de busca
  const onSearch = async (values: CnpjSearchValues) => {
    await searchCNPJ(values.cnpj);
  };

  // Handler para o formulário completo
  const onConfirm = (values: CompanyDataValues) => {
    console.log('CNPJSearch onConfirm called with values:', values);
    console.log('CNPJSearch form state:', {
      isValid: companyForm.formState.isValid,
      errors: companyForm.formState.errors,
      touchedFields: companyForm.formState.touchedFields
    });
    onDataFound(values);
  };

  // Voltar para busca
  const handleBackToSearch = () => {
    setSearchStep(true);
    setFoundData(null);
    searchForm.reset();
  };

  // Pular busca e preencher manualmente
  const handleSkip = () => {
    onSkip();
  };

  // Passo 1: Busca por CNPJ
  if (searchStep) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-primary" />
            <CardTitle>Consulta de CNPJ</CardTitle>
          </div>
          <CardDescription>
            Informe o CNPJ da sua empresa para preenchermos automaticamente os dados cadastrais.
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Form {...searchForm}>
            <form onSubmit={searchForm.handleSubmit(onSearch)} className="space-y-6">
              <FormField
                control={searchForm.control}
                name="cnpj"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CNPJ da Empresa</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="00.000.000/0000-00"
                          maxLength={18}
                        />
                      </FormControl>
                      <Button type="submit" disabled={isSearching}>
                        {isSearching ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Consultando...
                          </>
                        ) : (
                          <>
                            <SearchIcon className="mr-2 h-4 w-4" />
                            Consultar
                          </>
                        )}
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSkip}
                  className="flex-1"
                >
                  Preencher Manualmente
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  // Passo 2: Dados encontrados
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-500" />
          <CardTitle>Dados Encontrados</CardTitle>
        </div>
        <CardDescription>
          Verifique e complete as informações da sua empresa para continuar.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...companyForm}>
          <form onSubmit={companyForm.handleSubmit(onConfirm)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={companyForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Empresa</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nome da empresa" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={companyForm.control}
                name="cnpj"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CNPJ</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="00.000.000/0000-00" readOnly />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={companyForm.control}
              name="legalName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Razão Social</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Razão Social da empresa" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={companyForm.control}
              name="tradeName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome Fantasia</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Nome Fantasia (opcional)" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={companyForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Corporativo</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} placeholder="<EMAIL>" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={companyForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telefone Comercial</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="(00) 0000-0000" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={companyForm.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Site da Empresa</FormLabel>
                  <FormControl>
                    <Input type="url" {...field} placeholder="https://www.empresa.com (opcional)" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


            <div className="flex justify-between gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                disabled={isFirstStep || isLoading}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Anterior
              </Button>

              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    console.log('Manual validation trigger:', {
                      isValid,
                      errors: companyForm.formState.errors,
                      values: watchedValues,
                      canProceed
                    });
                    companyForm.trigger();
                  }}
                  className="text-xs"
                >
                  Debug Validação
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    console.log('Force submit with current values:', watchedValues);
                    onDataFound(watchedValues);
                  }}
                  className="text-xs"
                >
                  Forçar Envio
                </Button>

                {!isLastStep ? (
                  <Button
                    type="button"
                    onClick={onNext}
                    disabled={!canProceed || isLoading}
                    className="flex items-center gap-2"
                  >
                    Próximo
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={onNext}
                    disabled={!canProceed || isLoading}
                    className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Enviando...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Finalizar Onboarding
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
