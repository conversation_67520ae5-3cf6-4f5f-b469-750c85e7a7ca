import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { PageTabs } from "@saas/shared/components/PageTabs";
import { FinanceOverview } from "@saas/finance/components/FinanceOverview";
import { FinanceTransactions } from "@saas/finance/components/FinanceTransactions";
import { FinanceBanking } from "@saas/finance/components/FinanceBanking";
import { WithdrawModal } from "@saas/finance/components/WithdrawModal";
import { Button } from "@ui/components/button";
import { DollarSignIcon } from "lucide-react";
import { notFound } from "next/navigation";

export default async function FinancePage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const tabs = [
    {
      value: "overview",
      label: "Visão Geral",
      content: <FinanceOverview organizationId={organization.id} organizationSlug={organizationSlug} />,
    },
    {
      value: "extract",
      label: "Extrato",
      content: <FinanceTransactions organizationId={organization.id} />,
    },
    {
      value: "banking",
      label: "Dados Bancários",
      content: <FinanceBanking organizationId={organization.id} organizationSlug={organizationSlug} />,
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Financeiro"
        subtitle="Controle financeiro completo da sua organização"
        actions={
          <WithdrawModal availableBalance={98750.25} organizationSlug={organizationSlug}>
            <Button>
              <DollarSignIcon className="h-4 w-4 mr-2" />
              Solicitar Saque
            </Button>
          </WithdrawModal>
        }
      />

      <PageTabs tabs={tabs} defaultValue="overview" />
    </div>
  );
}
