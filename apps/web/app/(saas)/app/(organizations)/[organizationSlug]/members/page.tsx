import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { getActiveOrganization, getSession } from "@saas/auth/lib/server";
import { MembersDashboard } from "@saas/organizations/components/MembersDashboard";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { notFound, redirect } from "next/navigation";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("organizations.members.title"),
		description: t("organizations.members.description"),
	};
}

export default async function MembersPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const session = await getSession();
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	// Only organization admins can access member management
	if (!isOrganizationAdmin(organization, session?.user)) {
		return redirect(`/app/${organizationSlug}`);
	}

	const t = await getTranslations();

	return (
		<div className="space-y-6">
			<PageHeader
				title={t("organizations.members.title")}
				subtitle={t("organizations.members.description")}
			/>

			<MembersDashboard organizationId={organization.id} />
		</div>
	);
}
