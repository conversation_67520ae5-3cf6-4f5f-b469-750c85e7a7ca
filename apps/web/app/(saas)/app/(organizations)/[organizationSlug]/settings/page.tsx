import { getActiveOrganization } from "@saas/auth/lib/server";
import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { 
	BuildingIcon,
	UsersIcon,
	CreditCardIcon,
	ZapIcon,
	PaletteIcon,
	SettingsIcon,
	ArrowRightIcon,
	ShieldIcon,
	BarChart3Icon
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function OrganizationSettingsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const session = await getSession();
	const organization = await getActiveOrganization(organizationSlug);

	if (!session || !organization) {
		redirect("/app");
	}

	const userIsAdmin = isAdmin(session.user);

	if (!userIsAdmin) {
		redirect(`/app/${organizationSlug}`);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Configurações da Organização</h1>
					<p className="text-muted-foreground">
						Gerencie as configurações da {organization.name}
					</p>
				</div>
				<Badge variant="outline" className="text-sm">
					Administrador
				</Badge>
			</div>

			{/* Informações da Organização */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex items-center space-x-4">
						<div className="w-16 h-16 rounded-lg bg-primary/10 flex items-center justify-center">
							<BuildingIcon className="h-8 w-8 text-primary" />
						</div>
						<div className="flex-1">
							<h2 className="text-xl font-semibold">{organization.name}</h2>
							<p className="text-muted-foreground">
								{organization.domain || 'Sem domínio configurado'}
							</p>
							<p className="text-sm text-muted-foreground">
								Criada em {new Date(organization.createdAt).toLocaleDateString('pt-BR')}
							</p>
						</div>
						<Link href={`/app/${organizationSlug}/settings/general`}>
							<Button variant="outline" size="sm">
								Editar Organização
							</Button>
						</Link>
					</div>
				</CardContent>
			</Card>

			{/* Configurações - Seguindo o padrão da conta do usuário */}
			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Configurações Gerais</CardTitle>
						<SettingsIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Informações básicas e configurações da organização
						</CardDescription>
						<div className="space-y-2">
							<Link href={`/app/${organizationSlug}/settings/general`}>
								<Button variant="outline" size="sm" className="w-full justify-between">
									Configurações Gerais
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href={`/app/${organizationSlug}/settings/operations`}>
								<Button variant="outline" size="sm" className="w-full justify-between">
									Configurações Operacionais
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Membros</CardTitle>
						<UsersIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Gerencie membros e permissões
						</CardDescription>
						<Link href={`/app/${organizationSlug}/settings/members`}>
							<Button variant="outline" size="sm" className="w-full justify-between">
								Gerenciar Membros
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Cobrança</CardTitle>
						<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Planos, faturas e pagamentos
						</CardDescription>
						<Link href={`/app/${organizationSlug}/settings/billing`}>
							<Button variant="outline" size="sm" className="w-full justify-between">
								Configurar Cobrança
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Integrações</CardTitle>
						<ZapIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Conecte com ferramentas externas
						</CardDescription>
						<Link href={`/app/${organizationSlug}/settings/integrations`}>
							<Button variant="outline" size="sm" className="w-full justify-between">
								Configurar Integrações
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Branding</CardTitle>
						<PaletteIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Personalize a identidade visual
						</CardDescription>
						<Link href={`/app/${organizationSlug}/settings/branding`}>
							<Button variant="outline" size="sm" className="w-full justify-between">
								Configurar Branding
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Segurança</CardTitle>
						<ShieldIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Configurações de segurança
						</CardDescription>
						<Link href={`/app/${organizationSlug}/settings/security`}>
							<Button variant="outline" size="sm" className="w-full justify-between">
								Configurar Segurança
								<ArrowRightIcon className="h-4 w-4" />
							</Button>
						</Link>
					</CardContent>
				</Card>
			</div>

			{/* Analytics */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BarChart3Icon className="h-5 w-5" />
						Analytics e Relatórios
					</CardTitle>
					<CardDescription>
						Acesse relatórios e métricas da organização
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Link href={`/app/${organizationSlug}/analytics`}>
						<Button variant="outline" className="w-full justify-between">
							Ver Analytics
							<ArrowRightIcon className="h-4 w-4" />
						</Button>
					</Link>
				</CardContent>
			</Card>
		</div>
	);
}
