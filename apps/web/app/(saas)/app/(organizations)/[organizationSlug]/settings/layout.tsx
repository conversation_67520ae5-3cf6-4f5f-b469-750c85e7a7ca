import { isAdmin } from "@repo/auth/lib/helper";
import { config } from "@repo/config";
import { getActiveOrganization, getSession } from "@saas/auth/lib/server";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { SettingsMenu } from "@saas/settings/components/SettingsMenu";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { SidebarContentLayout } from "@saas/shared/components/SidebarContentLayout";
import {
	CreditCardIcon,
	Settings2Icon,
	TriangleAlertIcon,
	Users2Icon,
	CogIcon,
	ZapIcon,
	PaletteIcon,
	ShieldIcon,
	BarChart3Icon,
} from "lucide-react";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import type { PropsWithChildren } from "react";

export default async function SettingsLayout({
	children,
	params,
}: PropsWithChildren<{
	params: Promise<{ organizationSlug: string }>;
}>) {
	const t = await getTranslations();
	const session = await getSession();
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		redirect("/app");
	}

	const userIsOrganizationAdmin = isAdmin(session?.user);

	if (!userIsOrganizationAdmin) {
		redirect(`/app/${organizationSlug}`);
	}

	const organizationSettingsBasePath = `/app/${organizationSlug}/settings`;

	const menuItems = [
		{
			title: organization.name,
			avatar: (
				<OrganizationLogo
					name={organization.name}
					logoUrl={organization.logo}
				/>
			),
			items: [
				{
					title: "Visão Geral",
					href: organizationSettingsBasePath,
					icon: <Settings2Icon className="size-4 opacity-50" />,
				},
				{
					title: "Configurações Gerais",
					href: `${organizationSettingsBasePath}/general`,
					icon: <Settings2Icon className="size-4 opacity-50" />,
				},
				{
					title: "Membros",
					href: `${organizationSettingsBasePath}/members`,
					icon: <Users2Icon className="size-4 opacity-50" />,
				},
				{
					title: "Cobrança",
					href: `${organizationSettingsBasePath}/billing`,
					icon: <CreditCardIcon className="size-4 opacity-50" />,
				},
				{
					title: "Integrações",
					href: `${organizationSettingsBasePath}/integrations`,
					icon: <ZapIcon className="size-4 opacity-50" />,
				},
				{
					title: "Branding",
					href: `${organizationSettingsBasePath}/branding`,
					icon: <PaletteIcon className="size-4 opacity-50" />,
				},
				{
					title: "Segurança",
					href: `${organizationSettingsBasePath}/security`,
					icon: <ShieldIcon className="size-4 opacity-50" />,
				},
				{
					title: "Configurações Operacionais",
					href: `${organizationSettingsBasePath}/operations`,
					icon: <CogIcon className="size-4 opacity-50" />,
				},
			],
		},
	];

	return (
		<>
			<PageHeader
				title="Configurações da Organização"
				subtitle={`Gerencie as configurações da ${organization.name}`}
			/>
			<SidebarContentLayout
				sidebar={<SettingsMenu menuItems={menuItems} />}
			>
				{children}
			</SidebarContentLayout>
		</>
	);
}