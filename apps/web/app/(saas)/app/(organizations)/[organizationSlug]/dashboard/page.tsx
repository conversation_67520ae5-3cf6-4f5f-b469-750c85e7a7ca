import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components";
import { Badge } from "@ui/components";
import { Progress } from "@ui/components";
import { Button } from "@ui/components";
import { MetricCard, MetricGrid } from "@saas/shared/components/MetricCard";
import {
  DollarSign,
  CreditCard,
  Zap,
  Wallet,
  TrendingUp,
  Users,
  ShoppingCart,
  Calendar,
  Search,
  Eye,
  ExternalLink,
  Mountain,
  Flag,
  Receipt,
  BarChart3,
  Clock,
  TargetIcon,
  PercentIcon,
  ActivityIcon
} from "lucide-react";

export default async function DashboardPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  // Mock data - in real app this would come from API
  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleDateString('pt-BR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  // Formatadores de valores
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const formatNumber = (value: number) => {
    return value.toLocaleString('pt-BR');
  };

  // Dados simulados para demonstração do padrão
  const mockMetrics = {
    totalRevenue: 0,
    revenueGrowth: 0,
    totalSales: 0,
    salesGrowth: 0,
    totalCustomers: 0,
    customersGrowth: 0,
    totalProducts: 0,
    productsGrowth: 0,
    conversionRate: 0,
    conversionGrowth: 0,
    performanceScore: 0,
    scoreChange: 0,
    pendingBalance: 0,
    availableBalance: 0
  };

  const revenueData = [
    { date: "29 jul", value: 0 },
    { date: "30 jul", value: 0 },
    { date: "31 jul", value: 0 },
    { date: "1 ago", value: 0 },
    { date: "2 ago", value: 0 },
    { date: "3 ago", value: 0 },
    { date: "4 ago", value: 0 },
    { date: "5 ago", value: 0 },
    { date: "6 ago", value: 0 },
    { date: "7 ago", value: 0 },
    { date: "8 ago", value: 0 },
    { date: "9 ago", value: 0 },
    { date: "10 ago", value: 0 },
    { date: "11 ago", value: 0 },
    { date: "12 ago", value: 0 },
    { date: "13 ago", value: 0 },
    { date: "14 ago", value: 0 },
    { date: "15 ago", value: 0 },
    { date: "16 ago", value: 0 },
    { date: "17 ago", value: 0 },
    { date: "18 ago", value: 0 },
    { date: "19 ago", value: 0 },
    { date: "20 ago", value: 0 },
    { date: "21 ago", value: 0 },
    { date: "22 ago", value: 0 },
    { date: "23 ago", value: 0 },
    { date: "24 ago", value: 0 },
    { date: "25 ago", value: 0 },
    { date: "26 ago", value: 0 },
    { date: "27 ago", value: 0 },
    { date: "28 ago", value: 1500 }, // Spike at the end
  ];

  const paymentMethods = [
    { name: "Cartão de crédito", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "PIX", icon: Zap, percentage: 0, count: "0/0" },
    { name: "Boleto", icon: Receipt, percentage: 0, count: "0/0" },
    { name: "PicPay", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "Apple Pay", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "Google Pay", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "Samsung Pay", icon: CreditCard, percentage: 0, count: "0/0" },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* Top Bar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>7/29/2025 - 8/28/2025</span>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <input
              type="text"
              placeholder="Buscar produto..."
              className="w-64 pl-10 pr-4 py-2 rounded-lg border bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Welcome Section */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Hoje é {formattedDate}</CardTitle>
            <CardDescription>Olá, {organization.name} 👋</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Pequenas ações geram grandes resultados
            </p>
          </CardContent>
        </Card>

        <MetricCard
          title="Saldo Disponível"
          value={formatCurrency(mockMetrics.availableBalance)}
          change={""}
          icon={Wallet}
          description="Disponível para saque"
          badge={{
            text: "SALDO",
            variant: "default"
          }}
          className="bg-gradient-to-br from-amber-50/50 to-transparent dark:from-amber-950/20"
        />

        <MetricCard
          title="Saldo Pendente"
          value={formatCurrency(mockMetrics.pendingBalance)}
          change={""}
          icon={Clock}
          description="Aguardando processamento"
          badge={{
            text: "PENDENTE",
            variant: "outline"
          }}
          className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
        />
      </div>

      {/* Métricas Principais */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Visão Geral do Negócio</h2>
        <MetricGrid columns={4}>
          <MetricCard
            title="Receita Total"
            value={formatCurrency(mockMetrics.totalRevenue)}
            change={formatPercentage(mockMetrics.revenueGrowth)}
            isPositive={mockMetrics.revenueGrowth >= 0}
            icon={DollarSign}
            description="Receita acumulada"
            badge={{
              text: "MÊS ATUAL",
              variant: "default"
            }}
            className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
          />

          <MetricCard
            title="Vendas"
            value={formatNumber(mockMetrics.totalSales)}
            change={formatPercentage(mockMetrics.salesGrowth)}
            isPositive={mockMetrics.salesGrowth >= 0}
            icon={ShoppingCart}
            description="Vendas realizadas"
            badge={{
              text: "HOJE",
              variant: "outline"
            }}
            className="bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20"
          />

          <MetricCard
            title="Clientes"
            value={formatNumber(mockMetrics.totalCustomers)}
            change={formatPercentage(mockMetrics.customersGrowth)}
            isPositive={mockMetrics.customersGrowth >= 0}
            icon={Users}
            description="Clientes únicos"
            badge={{
              text: "ATIVOS",
              variant: "secondary"
            }}
            className="bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20"
          />

          <MetricCard
            title="Produtos"
            value={formatNumber(mockMetrics.totalProducts)}
            change={formatPercentage(mockMetrics.productsGrowth)}
            isPositive={mockMetrics.productsGrowth >= 0}
            icon={TargetIcon}
            description="Produtos ativos"
            badge={{
              text: "PUBLICADOS",
              variant: "outline"
            }}
            className="bg-gradient-to-br from-orange-50/50 to-transparent dark:from-orange-950/20"
          />
        </MetricGrid>
      </div>

      {/* Métricas de Performance */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Performance & Analytics</h2>
        <MetricGrid columns={3}>
          <MetricCard
            title="Taxa de Conversão"
            value={`${mockMetrics.conversionRate.toFixed(1)}%`}
            change={formatPercentage(mockMetrics.conversionGrowth)}
            isPositive={mockMetrics.conversionGrowth >= 0}
            icon={PercentIcon}
            description="Visitantes que compram"
            badge={{
              text: "PERFORMANCE",
              variant: "default"
            }}
            className="bg-gradient-to-br from-amber-50/50 to-transparent dark:from-amber-950/20"
          />

          <MetricCard
            title="Score de Performance"
            value={`${mockMetrics.performanceScore}/100`}
            change={`${mockMetrics.scoreChange > 0 ? '+' : ''}${mockMetrics.scoreChange} pontos`}
            isPositive={mockMetrics.scoreChange >= 0}
            icon={ActivityIcon}
            description="Saúde do negócio"
            badge={{
              text: "SCORE",
              variant: "secondary"
            }}
            className="bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20"
          />

          <MetricCard
            title="Ticket Médio"
            value={formatCurrency(mockMetrics.totalRevenue / Math.max(mockMetrics.totalSales, 1))}
            change="+0%"
            isPositive={true}
            icon={Receipt}
            description="Valor médio por venda"
            badge={{
              text: "MÉDIO",
              variant: "outline"
            }}
            className="bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20"
          />
        </MetricGrid>
      </div>

      {/* Saúde da Conta */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Saúde da Conta</h2>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Em breve, você poderá acompanhar a saúde da sua conta
                </p>
                <p className="text-xs text-muted-foreground">
                  Metrics detalhadas estarão disponíveis em breve
                </p>
              </div>
              <div className="h-12 w-12 rounded-lg bg-primary/10 p-3">
                <BarChart3 className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Chart and Achievements */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Receita Líquida</CardTitle>
              <Badge status="info">30 DIAS</Badge>
            </div>
            <CardDescription>
              {formatCurrency(mockMetrics.totalRevenue)} - Receita do período
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-48 flex items-end justify-between gap-1">
              {revenueData.map((item, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-gradient-to-t from-primary/30 to-primary/10 rounded-t-sm transition-all duration-300 hover:from-primary/50 hover:to-primary/20"
                    style={{
                      height: `${Math.max(item.value / 20, 2)}px`,
                      minHeight: '2px'
                    }}
                  />
                  <span className="text-xs text-muted-foreground mt-1">
                    {item.date}
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-4 flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-primary/30 rounded"></div>
                <span className="text-muted-foreground">Receita diária</span>
              </div>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">Últimos 30 dias</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Jornada de conquistas</CardTitle>
              <Button variant="ghost" size="sm" className="text-primary">
                Saiba mais <ExternalLink className="h-3 w-3 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Sua jornada começa com a primeira venda. Vamos nessa?
            </p>

            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                  <Mountain className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">1ª venda Explorador</p>
                  <p className="text-xs text-muted-foreground">Primeiro passo da jornada</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                  <Flag className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">10k Avançado</p>
                  <p className="text-xs text-muted-foreground">Meta intermediária</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                  <DollarSign className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">100k Expert</p>
                  <p className="text-xs text-muted-foreground">Nível máximo</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Grid */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Métodos de Pagamento</h2>
          <Badge status="info">7 DISPONÍVEIS</Badge>
        </div>
        <div className="grid gap-4 md:grid-cols-4">
          {paymentMethods.slice(0, 4).map((method, index) => (
            <Card key={index} className="hover:shadow-md transition-all duration-200">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 p-2.5">
                    <method.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-sm">{method.name}</span>
                    <p className="text-xs text-muted-foreground">Indisponível</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{method.percentage}%</span>
                    <span className="font-medium">{method.count}</span>
                  </div>
                  <Progress value={method.percentage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          {paymentMethods.slice(4).map((method, index) => (
            <Card key={index} className="hover:shadow-md transition-all duration-200">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 p-2.5">
                    <method.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-sm">{method.name}</span>
                    <p className="text-xs text-muted-foreground">Indisponível</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{method.percentage}%</span>
                    <span className="font-medium">{method.count}</span>
                  </div>
                  <Progress value={method.percentage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
