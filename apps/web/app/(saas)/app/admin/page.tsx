import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { 
	UsersIcon,
	BuildingIcon,
	ShieldIcon,
	BarChart3Icon,
	SettingsIcon,
	CreditCardIcon,
	ActivityIcon,
	AlertTriangleIcon,
	ArrowRightIcon,
	TrendingUpIcon,
	DollarSignIcon
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";

export default async function AdminDashboard() {
	const session = await getSession();

	if (!session || !isAdmin(session.user)) {
		redirect("/app");
	}

	// Buscar estatísticas do sistema
	const [
		totalUsers,
		totalOrganizations,
		recentUsers,
		recentOrganizations,
		activeOrganizations,
	] = await Promise.all([
		db.user.count(),
		db.organization.count(),
		db.user.findMany({
			take: 5,
			orderBy: { createdAt: 'desc' },
			select: { id: true, name: true, email: true, createdAt: true, role: true }
		}),
		db.organization.findMany({
			take: 5,
			orderBy: { createdAt: 'desc' },
			select: { id: true, name: true, slug: true, createdAt: true }
		}),
		db.organization.count({
			where: {
				subscriptionStatus: 'active'
			}
		})
	]);

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Painel Administrativo</h1>
					<p className="text-muted-foreground">
						Gerencie usuários, organizações e configurações do sistema
					</p>
				</div>
				<Badge variant="destructive" className="text-sm">
					{session.user.role}
				</Badge>
			</div>

			{/* Estatísticas Gerais */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Usuários</CardTitle>
						<UsersIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalUsers}</div>
						<p className="text-xs text-muted-foreground">
							Usuários registrados
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Organizações</CardTitle>
						<BuildingIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalOrganizations}</div>
						<p className="text-xs text-muted-foreground">
							{activeOrganizations} ativas
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Mensal</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">R$ 0,00</div>
						<p className="text-xs text-muted-foreground">
							+0% em relação ao mês anterior
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Sistema</CardTitle>
						<ActivityIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">Online</div>
						<p className="text-xs text-muted-foreground">
							Todos os serviços operacionais
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Ações Rápidas */}
			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Gestão de Usuários</CardTitle>
						<UsersIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Gerencie usuários, roles e permissões
						</CardDescription>
						<div className="space-y-2">
							<Link href="/app/admin/users">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Ver Usuários
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href="/app/admin/users/create">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Criar Usuário
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Organizações</CardTitle>
						<BuildingIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Gerencie organizações e configurações
						</CardDescription>
						<div className="space-y-2">
							<Link href="/app/admin/organizations">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Ver Organizações
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href="/app/admin/organizations/create">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Criar Organização
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Configurações</CardTitle>
						<SettingsIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<CardDescription className="mb-4">
							Configurações globais do sistema
						</CardDescription>
						<div className="space-y-2">
							<Link href="/app/admin/settings">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Configurações
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
							<Link href="/app/admin/analytics">
								<Button variant="outline" size="sm" className="w-full justify-between">
									Analytics
									<ArrowRightIcon className="h-4 w-4" />
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Atividade Recente */}
			<div className="grid gap-6 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<UsersIcon className="h-5 w-5" />
							Usuários Recentes
						</CardTitle>
						<CardDescription>
							Últimos usuários registrados no sistema
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{recentUsers.map((user) => (
								<div key={user.id} className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium">{user.name || 'Sem nome'}</p>
										<p className="text-xs text-muted-foreground">{user.email}</p>
									</div>
									<div className="text-right">
										<Badge variant="outline" className="text-xs">
											{user.role}
										</Badge>
										<p className="text-xs text-muted-foreground">
											{new Date(user.createdAt).toLocaleDateString('pt-BR')}
										</p>
									</div>
								</div>
							))}
						</div>
						<div className="mt-4">
							<Link href="/app/admin/users">
								<Button variant="outline" size="sm" className="w-full">
									Ver Todos os Usuários
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<BuildingIcon className="h-5 w-5" />
							Organizações Recentes
						</CardTitle>
						<CardDescription>
							Últimas organizações criadas
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{recentOrganizations.map((org) => (
								<div key={org.id} className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium">{org.name}</p>
										<p className="text-xs text-muted-foreground">{org.slug}</p>
									</div>
									<div className="text-right">
										<p className="text-xs text-muted-foreground">
											{new Date(org.createdAt).toLocaleDateString('pt-BR')}
										</p>
									</div>
								</div>
							))}
						</div>
						<div className="mt-4">
							<Link href="/app/admin/organizations">
								<Button variant="outline" size="sm" className="w-full">
									Ver Todas as Organizações
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Alertas e Notificações */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<AlertTriangleIcon className="h-5 w-5" />
						Alertas do Sistema
					</CardTitle>
					<CardDescription>
						Notificações importantes e alertas de segurança
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="flex items-center space-x-4 p-4 border rounded-lg">
							<ActivityIcon className="h-5 w-5 text-green-600" />
							<div>
								<p className="text-sm font-medium">Sistema Operacional</p>
								<p className="text-xs text-muted-foreground">
									Todos os serviços estão funcionando normalmente
								</p>
							</div>
						</div>
						<div className="flex items-center space-x-4 p-4 border rounded-lg">
							<ShieldIcon className="h-5 w-5 text-blue-600" />
							<div>
								<p className="text-sm font-medium">Segurança Atualizada</p>
								<p className="text-xs text-muted-foreground">
									Última verificação de segurança: hoje
								</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

