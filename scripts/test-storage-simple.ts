#!/usr/bin/env tsx

/**
 * Script simples para testar as configurações do storage
 *
 * Uso:
 * npx tsx scripts/test-storage-simple.ts
 */

import { getSignedUploadUrl } from "@repo/storage";
import { STORAGE_CONFIG } from "@repo/storage";

async function testStorageConfig() {
  console.log("🧪 Testando configuração do storage...\n");

  // Verificar variáveis de ambiente
  console.log("📋 Verificando variáveis de ambiente:");
  const envVars = {
    S3_ENDPOINT: process.env.S3_ENDPOINT,
    S3_REGION: process.env.S3_REGION || "auto",
    S3_ACCESS_KEY_ID: process.env.S3_ACCESS_KEY_ID ? "✅ Configurado" : "❌ Ausente",
    S3_SECRET_ACCESS_KEY: process.env.S3_SECRET_ACCESS_KEY ? "✅ Configurado" : "❌ Ausente",
  };

  Object.entries(envVars).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });

  console.log("\n📦 Configuração do storage:");
  console.log(`   Buckets: ${Object.keys(STORAGE_CONFIG.buckets).join(", ")}`);
  console.log(`   Tamanho máximo: ${STORAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`);
  console.log(`   Tipos permitidos: ${STORAGE_CONFIG.allowedTypes.join(", ")}`);

  // Verificar se todas as variáveis estão presentes
  const missingVars = Object.entries(envVars)
    .filter(([_, value]) => value.includes("❌"))
    .map(([key, _]) => key);

  if (missingVars.length > 0) {
    console.log("\n❌ Variáveis de ambiente ausentes:");
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log("\n💡 Configure as variáveis de ambiente antes de testar o upload.");
    return;
  }

  console.log("\n✅ Todas as variáveis de ambiente estão configuradas");

  // Testar conexão básica
  try {
    console.log("\n🔄 Testando conexão com Cloudflare R2...");

    const testPath = `test-connection-${Date.now()}.txt`;
    const bucketName = STORAGE_CONFIG.buckets.checkoutBanners;

    const uploadUrl = await getSignedUploadUrl(testPath, { bucket: bucketName });

    console.log("✅ Conexão com Cloudflare R2 bem-sucedida!");
    console.log(`   Bucket: ${bucketName}`);
    console.log(`   URL de upload gerada: ${uploadUrl.substring(0, 80)}...`);

    console.log("\n🎉 Configuração do storage está funcionando corretamente!");
    console.log("✅ Pronto para fazer uploads de banners de checkout");

  } catch (error) {
    console.error("\n❌ Erro ao testar conexão:");
    console.error(error);

    if (error instanceof Error) {
      if (error.message.includes("Missing env variable")) {
        console.log("\n💡 Verifique se todas as variáveis de ambiente estão configuradas.");
      } else if (error.message.includes("Could not get signed")) {
        console.log("\n💡 Verifique se:");
        console.log("   - As credenciais do Cloudflare R2 estão corretas");
        console.log("   - O bucket 'checkout-banners' existe");
        console.log("   - As credenciais têm permissão para acessar o bucket");
      }
    }
  }
}

testStorageConfig().catch(console.error);
