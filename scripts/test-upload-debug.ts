#!/usr/bin/env tsx

/**
 * Script para testar upload de arquivos em produção
 * Execute com: npx tsx scripts/test-upload-debug.ts
 */

import { config } from "dotenv";
import { resolve } from "path";

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), ".env") });

import { config as appConfig } from "@repo/config";
import { getSignedUploadUrl, getSignedUrl } from "@repo/storage";

async function testUpload() {
  console.log("🔍 Testando configuração de upload...");
  
  // 1. Verificar configuração
  console.log("\n📋 Configuração atual:");
  console.log("S3 Endpoint:", process.env.S3_ENDPOINT);
  console.log("S3 Region:", process.env.S3_REGION);
  console.log("S3 Access Key ID:", process.env.S3_ACCESS_KEY_ID ? "✅ Configurado" : "❌ Não configurado");
  console.log("S3 Secret Access Key:", process.env.S3_SECRET_ACCESS_KEY ? "✅ Configurado" : "❌ Não configurado");
  
  console.log("\n📦 Buckets configurados:");
  console.log("Avatars:", appConfig.storage.bucketNames.avatars);
  console.log("Products:", appConfig.storage.bucketNames.products);
  console.log("Checkout Banners:", appConfig.storage.bucketNames.checkoutBanners);
  console.log("Testimonial Avatars:", appConfig.storage.bucketNames.testimonialAvatars);
  
  console.log("\n🌐 Endpoints:");
  console.log("Public Endpoint:", appConfig.storage.publicEndpoint);
  console.log("CDN Domain:", appConfig.storage.cdnDomain);
  
  // 2. Testar geração de URL assinada
  try {
    console.log("\n🔗 Testando geração de URL assinada...");
    const testPath = `test-upload-${Date.now()}.jpg`;
    const bucketName = appConfig.storage.bucketNames.checkoutBanners;
    
    console.log("Path:", testPath);
    console.log("Bucket:", bucketName);
    
    const signedUrl = await getSignedUploadUrl(testPath, { bucket: bucketName });
    console.log("✅ URL assinada gerada com sucesso!");
    console.log("URL:", signedUrl);
    
    // 3. Testar URL de acesso
    console.log("\n🔍 Testando URL de acesso...");
    const accessUrl = await getSignedUrl(testPath, {
      bucket: bucketName,
      expiresIn: 60 * 60 * 24 // 1 dia
    });
    console.log("✅ URL de acesso gerada com sucesso!");
    console.log("URL:", accessUrl);
    
  } catch (error) {
    console.error("❌ Erro ao testar upload:", error);
    console.error("Stack:", error instanceof Error ? error.stack : "N/A");
  }
  
  // 4. Testar endpoint da API
  console.log("\n🌐 Testando endpoint da API...");
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://sup.nextrusti.com";
    const apiUrl = `${baseUrl}/api/uploads/public-signed-upload-url?bucket=${appConfig.storage.bucketNames.checkoutBanners}&path=test-api-${Date.now()}.jpg`;
    
    console.log("Testando URL:", apiUrl);
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    console.log("Status:", response.status);
    console.log("Headers:", Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log("✅ API respondeu com sucesso!");
      console.log("Response:", data);
    } else {
      const errorText = await response.text();
      console.log("❌ API retornou erro:");
      console.log("Error:", errorText);
    }
    
  } catch (error) {
    console.error("❌ Erro ao testar API:", error);
  }
}

// Executar teste
testUpload().catch(console.error);
