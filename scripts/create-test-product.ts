import { db } from '@repo/database';

async function createTestProduct() {
  try {
    // Verificar se já existe um produto de teste
    const existingProduct = await db.product.findFirst({
      where: {
        name: 'Produto de Teste - Checkout'
      }
    });

    if (existingProduct) {
      console.log('Produto de teste já existe:', existingProduct.id);
      return existingProduct;
    }

    // Criar um produto de teste
    const product = await db.product.create({
      data: {
        name: 'Produto de Teste - Checkout',
        description: 'Este é um produto de teste para verificar o funcionamento do checkout',
        type: 'COURSE',
        priceCents: 9900, // R$ 99,00
        currency: 'BRL',
        status: 'PUBLISHED',
        checkoutType: 'DEFAULT',
        settings: {
          installmentsLimit: 12,
          enableInstallments: true,
          acceptedPayments: ['CREDIT_CARD', 'PIX'],
          customCheckoutUrl: null,
          successUrl: null,
          cancelUrl: null,
          termsUrl: null
        },
        thumbnail: '/images/checkout/banner.png'
      }
    });

    console.log('Produto de teste criado:', product.id);
    console.log('URL do checkout:', `http://localhost:3000/checkout/${product.id}`);

    return product;
  } catch (error) {
    console.error('Erro ao criar produto de teste:', error);
    throw error;
  }
}

createTestProduct()
  .then(() => {
    console.log('Script executado com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Erro ao executar script:', error);
    process.exit(1);
  });
