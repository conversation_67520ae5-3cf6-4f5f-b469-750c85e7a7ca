#!/usr/bin/env tsx

/**
 * Script para testar a API de upload localmente e em produção
 * Execute com: npx tsx scripts/test-api-upload.ts
 */

import { config } from "dotenv";
import { resolve } from "path";

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), ".env") });

async function testApiUpload() {
  console.log("🔍 Testando API de upload...");
  
  const testCases = [
    {
      name: "Local API",
      baseUrl: "http://localhost:3000",
      description: "Testando API local"
    },
    {
      name: "Production API", 
      baseUrl: "https://sup.nextrusti.com",
      description: "Testando API em produção"
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📡 ${testCase.name}: ${testCase.description}`);
    
    try {
      const apiUrl = `${testCase.baseUrl}/api/uploads/public-signed-upload-url?bucket=supgateway&path=test-${Date.now()}.jpg`;
      
      console.log("URL:", apiUrl);
      
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      console.log("Status:", response.status);
      console.log("Headers:", Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        const data = await response.json();
        console.log("✅ Sucesso!");
        console.log("Response:", data);
      } else {
        const errorText = await response.text();
        console.log("❌ Erro:");
        console.log("Error:", errorText);
      }
      
    } catch (error) {
      console.error("❌ Erro na requisição:", error);
    }
  }
}

// Executar teste
testApiUpload().catch(console.error);
