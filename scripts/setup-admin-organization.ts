import { PrismaClient, UserRole } from "@prisma/client";
import { readFileSync } from "fs";
import { join } from "path";

// Carregar variáveis de ambiente do arquivo .env
const envFile = readFileSync(join(process.cwd(), '.env'), 'utf8');
const envVars = envFile.split('\n').reduce((acc, line) => {
  const equalIndex = line.indexOf('=');
  if (equalIndex > 0) {
    const key = line.substring(0, equalIndex).trim();
    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
    if (key && value) {
      acc[key] = value;
    }
  }
  return acc;
}, {} as Record<string, string>);

// Definir variáveis de ambiente
Object.entries(envVars).forEach(([key, value]) => {
  process.env[key] = value;
});

const db = new PrismaClient();

async function setupAdminOrganization() {
  try {
    console.log("🚀 Configurando organização e perfil do admin...");

    // Buscar o usuário admin
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (!adminUser) {
      console.log("❌ Usuário admin não encontrado!");
      return;
    }

    console.log(`👤 Admin encontrado: ${adminUser.name} (${adminUser.email})`);

    // Verificar se já tem organização
    const existingMembership = await db.member.findFirst({
      where: {
        userId: adminUser.id,
      },
      include: {
        organization: true,
      },
    });

    if (existingMembership) {
      console.log("🏢 Organização já existe!");
      console.log(`🏢 Nome: ${existingMembership.organization.name}`);
      console.log(`🏢 Slug: ${existingMembership.organization.slug}`);
    } else {
      // Criar organização para o admin
      const organization = await db.organization.create({
        data: {
          name: "SupGateway Admin",
          slug: "supgateway-admin",
          subscriptionPlan: "enterprise",
          subscriptionStatus: "active",
          enableAffiliatePogram: true,
          enableDigitalProducts: true,
          enableCertificates: true,
          enableCustomBranding: true,
          enableCustomDomain: true,
          enableAdvancedPayments: true,
          maxProducts: 999999,
          maxUsers: 999999,
          maxStorageGB: 999999,
          maxTransactions: 999999,
          members: {
            create: {
              userId: adminUser.id,
              role: "OWNER",
              createdAt: new Date(),
            },
          },
        },
      });

      console.log("✅ Organização criada!");
      console.log(`🏢 ID: ${organization.id}`);
      console.log(`🏢 Nome: ${organization.name}`);
      console.log(`🏢 Slug: ${organization.slug}`);
    }

    // Verificar se já tem perfil de professor
    const existingTeacherProfile = await db.teacherProfile.findFirst({
      where: {
        userId: adminUser.id,
      },
    });

    if (existingTeacherProfile) {
      console.log("👨‍🏫 Perfil de professor já existe!");
    } else {
      // Criar perfil de professor
      await db.teacherProfile.create({
        data: {
          userId: adminUser.id,
          bio: "Administrador do SupGateway",
          expertise: ["Plataforma", "Gestão", "Desenvolvimento"],
          experience: "Experiência completa na plataforma",
          isVerified: true,
        },
      });

      console.log("✅ Perfil de professor criado!");
    }

    console.log("\n🎉 Setup completo!");
    console.log("📝 Informações do admin:");
    console.log(`📧 Email: ${adminUser.email}`);
    console.log(`👤 Username: ${adminUser.username}`);
    console.log(`🔑 Role: ${adminUser.role}`);
    console.log("\n🔐 Para fazer login:");
    console.log("1. Acesse a página de login");
    console.log("2. Use o email: <EMAIL>");
    console.log("3. Use a funcionalidade 'Esqueci minha senha' para definir uma senha");
    console.log("4. Ou configure autenticação via OAuth (Google, GitHub, etc.)");

  } catch (error) {
    console.error("❌ Erro ao configurar admin:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
setupAdminOrganization();
