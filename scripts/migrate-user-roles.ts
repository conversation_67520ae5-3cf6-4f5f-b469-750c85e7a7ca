#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";

const db = new PrismaClient();

async function migrateUserRoles() {
  console.log("🔄 Iniciando migração das roles de usuário...\n");

  try {
    // Migrar USER -> CUSTOMER
    const userCount = await db.user.count({
      where: { role: "USER" }
    });

    if (userCount > 0) {
      await db.user.updateMany({
        where: { role: "USER" },
        data: { role: "CUSTOMER" }
      });
      console.log(`✅ Migrados ${userCount} usuários de USER para CUSTOMER`);
    }

    // Migrar TEACHER -> SELLER
    const teacherCount = await db.user.count({
      where: { role: "TEACHER" }
    });

    if (teacherCount > 0) {
      await db.user.updateMany({
        where: { role: "TEACHER" },
        data: { role: "SELLER" }
      });
      console.log(`✅ Migrados ${teacherCount} usuários de TEACHER para SELLER`);
    }

    // Verificar roles atuais
    const roleCounts = await db.user.groupBy({
      by: ["role"],
      _count: true
    });

    console.log("\n📊 Roles atuais após migração:");
    console.log("=====================================");
    roleCounts.forEach(({ role, _count }) => {
      console.log(`${role}: ${_count} usuários`);
    });

    console.log("\n🎉 Migração concluída com sucesso!");
    console.log("\n📝 Resumo das mudanças:");
    console.log("- USER → CUSTOMER");
    console.log("- TEACHER → SELLER");
    console.log("- AFFILIATE (mantido)");
    console.log("- ADMIN (mantido)");
    console.log("- SUPER_ADMIN (mantido)");

  } catch (error) {
    console.error("❌ Erro durante a migração:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar migração
migrateUserRoles();
