#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";
import { createUserAccount } from "@repo/database";
import { auth } from "@repo/auth";

const db = new PrismaClient();

async function fixAdminPassword() {
  console.log("🔐 Corrigindo senha do admin...\n");

  try {
    // Buscar o usuário admin
    const adminUser = await db.user.findFirst({
      where: { email: "<EMAIL>" }
    });

    if (!adminUser) {
      console.error("❌ Usuário admin não encontrado!");
      process.exit(1);
    }

    // Senha para o admin
    const adminPassword = "admin123456";

    // Usar o better-auth para fazer o hash correto
    const authContext = await auth.$context;
    const hashedPassword = await authContext.password.hash(adminPassword);

    // Usar a função createUserAccount que funciona corretamente
    await createUserAccount({
      userId: adminUser.id,
      providerId: "credential",
      accountId: adminUser.id,
      hashedPassword,
    });

    console.log("✅ Senha corrigida com sucesso!");
    console.log("\n🔐 INFORMAÇÕES DE LOGIN:");
    console.log("==========================");
    console.log(`📧 Email: <EMAIL>`);
    console.log(`🔑 Senha: ${adminPassword}`);
    console.log(`🌐 URL: http://localhost:3001/auth/login`);
    console.log(`👤 Role: SUPER_ADMIN`);

  } catch (error) {
    console.error("❌ Erro ao corrigir senha:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar
fixAdminPassword();
