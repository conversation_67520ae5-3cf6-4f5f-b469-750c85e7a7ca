# Scripts de Teste do Storage

Este diretório contém scripts para testar a funcionalidade do storage com Cloudflare R2.

## Scripts Disponíveis

### 1. `check-storage-env.ts`
Verifica se as variáveis de ambiente estão configuradas corretamente.

```bash
# Usando dotenv-cli (recomendado)
npx dotenv -c -- tsx scripts/check-storage-env.ts

# Ou se as variáveis já estão no ambiente
npx tsx scripts/check-storage-env.ts
```

**O que faz:**
- Verifica se todas as variáveis de ambiente necessárias estão presentes
- Mostra a configuração atual do storage
- Fornece instruções de configuração se algo estiver ausente

### 2. `test-storage-simple.ts`
Teste básico de conexão com Cloudflare R2.

```bash
# Usando dotenv-cli (recomendado)
npx dotenv -c -- tsx scripts/test-storage-simple.ts

# Ou se as variáveis já estão no ambiente
npx tsx scripts/test-storage-simple.ts
```

**O que faz:**
- Testa a conexão com Cloudflare R2
- Gera uma URL de upload assinada
- Verifica se as credenciais estão funcionando

### 3. `test-storage-upload.ts`
Teste completo de upload e download de arquivos.

```bash
# Usando dotenv-cli (recomendado)
npx dotenv -c -- tsx scripts/test-storage-upload.ts

# Ou se as variáveis já estão no ambiente
npx tsx scripts/test-storage-upload.ts
```

**O que faz:**
- Faz upload de um arquivo de teste
- Testa o download do arquivo
- Verifica se o conteúdo está correto
- Testa URLs assinadas

### 4. `test-upload-hook.ts`
Testa o hook `useFileUpload` (simulação).

```bash
# Usando dotenv-cli (recomendado)
npx dotenv -c -- tsx scripts/test-upload-hook.ts

# Ou se as variáveis já estão no ambiente
npx tsx scripts/test-upload-hook.ts
```

**O que faz:**
- Simula o uso do hook de upload
- Testa callbacks de sucesso e erro
- Verifica o progresso de upload

### 5. `test-storage-direct.ts`
Testa o storage diretamente (sem API, sem servidor).

```bash
npx dotenv -c -- tsx scripts/test-storage-direct.ts
```

**O que faz:**
- Testa conexão direta com Cloudflare R2
- Faz upload e download de arquivos
- Verifica se as variáveis de ambiente estão corretas
- Útil para debug rápido do storage

### 6. `test-upload-api.ts`
Testa a API de upload (requer servidor rodando).

```bash
# Primeiro, inicie o servidor
npm run dev

# Em outro terminal, teste a API
npx dotenv -c -- tsx scripts/test-upload-api.ts
```

**O que faz:**
- Testa a API de upload de arquivos
- Verifica URLs assinadas
- Testa upload e download via API
- Útil para verificar se a integração frontend-backend está funcionando

### 7. `test-storage-all.ts`
Executa todos os testes em sequência.

```bash
# Usando dotenv-cli (recomendado)
npx dotenv -c -- tsx scripts/test-storage-all.ts

# Ou se as variáveis já estão no ambiente
npx tsx scripts/test-storage-all.ts
```

**O que faz:**
- Executa todos os scripts de teste
- Mostra um resumo final
- Útil para verificação completa

## Configuração Necessária

Antes de executar os testes, configure as seguintes variáveis de ambiente:

```bash
# Cloudflare R2 Configuration
S3_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
S3_REGION=auto
S3_ACCESS_KEY_ID=your-access-key-id
S3_SECRET_ACCESS_KEY=your-secret-access-key
```

## Ordem Recomendada de Execução

1. **Primeiro**: `check-storage-env.ts` - Verificar configuração
   ```bash
   npx dotenv -c -- tsx scripts/check-storage-env.ts
   ```

2. **Segundo**: `test-storage-simple.ts` - Teste básico
   ```bash
   npx dotenv -c -- tsx scripts/test-storage-simple.ts
   ```

3. **Terceiro**: `test-storage-upload.ts` - Teste completo
   ```bash
   npx dotenv -c -- tsx scripts/test-storage-upload.ts
   ```

4. **Opcional**: `test-upload-hook.ts` - Teste do hook
   ```bash
   npx dotenv -c -- tsx scripts/test-upload-hook.ts
   ```

Ou simplesmente execute `test-storage-all.ts` para rodar tudo:
```bash
npx dotenv -c -- tsx scripts/test-storage-all.ts
```

## Troubleshooting

### Erro: "Missing env variable"
- Verifique se todas as variáveis de ambiente estão configuradas
- Reinicie o terminal/servidor após configurar as variáveis

### Erro: "Could not get signed upload url"
- Verifique se as credenciais do Cloudflare R2 estão corretas
- Confirme se o bucket 'checkout-banners' existe
- Verifique se as credenciais têm permissão para acessar o bucket

### Erro: "403 Forbidden"
- Verifique se as credenciais têm permissão para escrever no bucket
- Confirme se o token de API tem as permissões corretas

### Erro: "404 Not Found"
- Verifique se o bucket 'checkout-banners' existe no Cloudflare R2
- Confirme se o nome do bucket está correto

## Estrutura dos Buckets

O sistema usa os seguintes buckets:

- `checkout-banners`: Para banners de checkout (novo)
- `onboarding-documents`: Para documentos de onboarding
- `user-documents`: Para documentos de usuários
- `company-documents`: Para documentos da empresa

## Limites e Configurações

- **Tamanho máximo**: 10MB por arquivo
- **Tipos permitidos**: PNG, JPEG, JPG, WebP, PDF
- **Timeout de upload**: 30 segundos
- **Expiração de URL**: 1 ano para acesso, 1 minuto para upload
