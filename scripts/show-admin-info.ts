import { PrismaClient, UserRole } from "@prisma/client";
import { readFileSync } from "fs";
import { join } from "path";

// Carregar variáveis de ambiente do arquivo .env
const envFile = readFileSync(join(process.cwd(), '.env'), 'utf8');
const envVars = envFile.split('\n').reduce((acc, line) => {
  const equalIndex = line.indexOf('=');
  if (equalIndex > 0) {
    const key = line.substring(0, equalIndex).trim();
    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
    if (key && value) {
      acc[key] = value;
    }
  }
  return acc;
}, {} as Record<string, string>);

// Definir variáveis de ambiente
Object.entries(envVars).forEach(([key, value]) => {
  process.env[key] = value;
});

const db = new PrismaClient();

async function showAdminInfo() {
  try {
    console.log("🔍 Buscando informações do usuário admin...\n");

    // Buscar o usuário admin com todas as informações
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
      include: {
        organizationMembers: {
          include: {
            organization: true,
          },
        },
        teacherProfile: true,
        affiliateProfile: true,
      },
    });

    if (!adminUser) {
      console.log("❌ Usuário admin não encontrado!");
      return;
    }

    console.log("👤 INFORMAÇÕES DO USUÁRIO ADMIN");
    console.log("=" .repeat(50));
    console.log(`🆔 ID: ${adminUser.id}`);
    console.log(`📧 Email: ${adminUser.email}`);
    console.log(`👤 Username: ${adminUser.username}`);
    console.log(`🔑 Role: ${adminUser.role}`);
    console.log(`✅ Email Verificado: ${adminUser.emailVerified ? 'Sim' : 'Não'}`);
    console.log(`🎯 Onboarding Completo: ${adminUser.onboardingComplete ? 'Sim' : 'Não'}`);
    console.log(`📅 Criado em: ${adminUser.createdAt.toLocaleString('pt-BR')}`);

    if (adminUser.organizationMembers.length > 0) {
      console.log("\n🏢 ORGANIZAÇÕES");
      console.log("=" .repeat(50));
      adminUser.organizationMembers.forEach((membership, index) => {
        console.log(`${index + 1}. ${membership.organization.name}`);
        console.log(`   🆔 ID: ${membership.organization.id}`);
        console.log(`   🔗 Slug: ${membership.organization.slug}`);
        console.log(`   👑 Role: ${membership.role}`);
        console.log(`   📊 Plano: ${membership.organization.subscriptionPlan}`);
        console.log(`   📈 Status: ${membership.organization.subscriptionStatus}`);
        console.log(`   🎨 Branding: ${membership.organization.enableCustomBranding ? 'Habilitado' : 'Desabilitado'}`);
        console.log(`   🌐 Domínio: ${membership.organization.enableCustomDomain ? 'Habilitado' : 'Desabilitado'}`);
        console.log("");
      });
    }

    if (adminUser.teacherProfile) {
      console.log("👨‍🏫 PERFIL DE PROFESSOR");
      console.log("=" .repeat(50));
      console.log(`📝 Bio: ${adminUser.teacherProfile.bio}`);
      console.log(`🎯 Expertise: ${adminUser.teacherProfile.expertise.join(', ')}`);
      console.log(`💼 Experiência: ${adminUser.teacherProfile.experience}`);
      console.log(`✅ Verificado: ${adminUser.teacherProfile.isVerified ? 'Sim' : 'Não'}`);
      console.log("");
    }

    console.log("🔐 COMO FAZER LOGIN");
    console.log("=" .repeat(50));
    console.log("1. Acesse: http://localhost:3000/auth/login");
    console.log("2. Email: <EMAIL>");
    console.log("3. Use 'Esqueci minha senha' para definir uma senha");
    console.log("4. Ou configure OAuth (Google, GitHub, etc.)");
    console.log("");
    console.log("🚀 FUNCIONALIDADES DISPONÍVEIS");
    console.log("=" .repeat(50));
    console.log("✅ Criar e gerenciar produtos");
    console.log("✅ Configurar cupons de desconto");
    console.log("✅ Acessar dashboard de vendas");
    console.log("✅ Gerenciar organizações");
    console.log("✅ Configurar pagamentos");
    console.log("✅ Acessar todas as funcionalidades da plataforma");
    console.log("");
    console.log("🎉 Usuário admin configurado com sucesso!");

  } catch (error) {
    console.error("❌ Erro ao buscar informações:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
showAdminInfo();
