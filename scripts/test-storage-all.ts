#!/usr/bin/env tsx

/**
 * Script principal para testar toda a funcionalidade do storage
 *
 * Uso:
 * npx tsx scripts/test-storage-all.ts
 */

import { execSync } from "child_process";
import { existsSync } from "fs";
import { join } from "path";

async function runAllStorageTests() {
  console.log("🧪 Executando todos os testes de storage...\n");

  const scripts = [
    {
      name: "Verificar variáveis de ambiente",
      file: "check-storage-env.ts",
      description: "Verifica se todas as variáveis de ambiente estão configuradas"
    },
    {
      name: "Teste de configuração básica",
      file: "test-storage-simple.ts",
      description: "Testa a conexão básica com Cloudflare R2"
    },
    {
      name: "Teste completo de upload",
      file: "test-storage-upload.ts",
      description: "Testa upload e download de arquivos"
    }
  ];

  let allPassed = true;

  for (const script of scripts) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🔄 ${script.name}`);
    console.log(`📝 ${script.description}`);
    console.log(`${'='.repeat(60)}`);

    const scriptPath = join(__dirname, script.file);

    if (!existsSync(scriptPath)) {
      console.log(`❌ Script não encontrado: ${script.file}`);
      allPassed = false;
      continue;
    }

    try {
      execSync(`npx tsx ${scriptPath}`, {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log(`✅ ${script.name} - PASSOU`);
    } catch (error) {
      console.log(`❌ ${script.name} - FALHOU`);
      allPassed = false;
    }
  }

  console.log(`\n${'='.repeat(60)}`);
  if (allPassed) {
    console.log("🎉 TODOS OS TESTES PASSARAM!");
    console.log("✅ Storage está configurado e funcionando corretamente");
    console.log("🚀 Pronto para usar a funcionalidade de banner no checkout");
  } else {
    console.log("❌ ALGUNS TESTES FALHARAM");
    console.log("💡 Verifique os erros acima e configure corretamente");
  }
  console.log(`${'='.repeat(60)}`);
}

runAllStorageTests().catch(console.error);
