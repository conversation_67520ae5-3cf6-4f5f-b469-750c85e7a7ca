#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";

const db = new PrismaClient();

async function migrateRolesSimplified() {
  console.log("🔄 Iniciando migração simplificada das roles...\n");

  try {
    console.log("🔄 Iniciando migração usando SQL direto...");

    // Migrar USER -> CUSTOMER
    const userResult = await db.$executeRaw`
      UPDATE "user"
      SET role = 'CUSTOMER'
      WHERE role = 'USER'
    `;
    console.log(`✅ Migrados ${userResult} usuários de USER para CUSTOMER`);

    // Migrar TEACHER -> SELLER
    const teacherResult = await db.$executeRaw`
      UPDATE "user"
      SET role = 'SELLER'
      WHERE role = 'TEACHER'
    `;
    console.log(`✅ Migrados ${teacherResult} usuários de TEACHER para SELLER`);

    // Migrar AFFILIATE -> SELLER
    const affiliateResult = await db.$executeRaw`
      UPDATE "user"
      SET role = 'SELLER'
      WHERE role = 'AFFILIATE'
    `;
    console.log(`✅ Migrados ${affiliateResult} usuários de AFFILIATE para SELLER`);

    // Verificar roles atuais após migração usando SQL
    const roleCounts = await db.$queryRaw<Array<{ role: string; count: bigint }>>`
      SELECT role, COUNT(*) as count
      FROM "user"
      GROUP BY role
      ORDER BY count DESC
    `;

    console.log("\n📊 Roles atuais após migração:");
    console.log("=====================================");
    roleCounts.forEach(({ role, count }) => {
      console.log(`${role}: ${count} usuários`);
    });

    console.log("\n🎉 Migração concluída com sucesso!");
    console.log("\n📝 Resumo das mudanças:");
    console.log("- USER → CUSTOMER (clientes finais)");
    console.log("- TEACHER → SELLER (vendedores/criadores)");
    console.log("- AFFILIATE → SELLER (afiliados agora são vendedores)");
    console.log("- ADMIN (mantido)");
    console.log("- SUPER_ADMIN (mantido)");
    console.log("\n💡 Agora qualquer SELLER pode vender produtos de outros SELLERs");
    console.log("   e ganhar comissões através do sistema de afiliação existente.");

  } catch (error) {
    console.error("❌ Erro durante a migração:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar migração
migrateRolesSimplified();
