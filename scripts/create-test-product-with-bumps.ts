import { db } from '../packages/database';

async function createTestProductWithBumps() {
  try {
    // Verificar se já existe um produto de teste
    const existingProduct = await db.product.findFirst({
      where: {
        name: 'Curso Completo de Marketing Digital'
      }
    });

    if (existingProduct) {
      console.log('Produto de teste já existe:', existingProduct.id);
      console.log('URL do checkout:', `http://localhost:3000/checkout/${existingProduct.id}`);
      return existingProduct;
    }

    // Criar um produto de teste com order bumps
    const product = await db.product.create({
      data: {
        organizationId: 'test-org-1', // ID de organização de teste
        creatorId: 'test-user-1', // ID de usuário de teste
        name: 'Curso Completo de Marketing Digital',
        slug: 'curso-marketing-digital',
        description: 'Aprenda marketing digital do zero ao avançado com este curso completo que inclui estratégias de redes sociais, SEO, Google Ads, Facebook Ads e muito mais!',
        shortDescription: 'Curso completo de marketing digital com certificado',
        type: 'COURSE',
        priceCents: 19700, // R$ 197,00
        comparePriceCents: 39700, // R$ 397,00 (preço original)
        currency: 'BRL',
        status: 'PUBLISHED',
        visibility: 'PUBLIC',
        thumbnail: '/images/checkout/banner.png',
        gallery: [
          '/images/checkout/banner.png',
          '/images/checkout/curso-preview-1.jpg',
          '/images/checkout/curso-preview-2.jpg'
        ],
        tags: ['marketing', 'digital', 'curso', 'certificado'],
        features: [
          'Mais de 50 horas de conteúdo',
          'Certificado de conclusão',
          'Suporte por 1 ano',
          'Acesso vitalício',
          'Bônus exclusivos'
        ],
        requirements: [
          'Conhecimento básico de internet',
          'Computador ou smartphone',
          'Desejo de aprender'
        ],
        duration: 3000, // 50 horas em minutos
        level: 'BEGINNER',
        language: 'pt-BR',
        certificate: true,
        downloadable: true,
        checkoutType: 'DEFAULT',
        settings: {
          installmentsLimit: 12,
          enableInstallments: true,
          acceptedPayments: ['CREDIT_CARD', 'PIX', 'BOLETO'],
          customCheckoutUrl: null,
          successUrl: null,
          cancelUrl: null,
          termsUrl: null,
          showUrgency: true,
          showTestimonials: true,
          showTrustBadges: true,
          showScarcity: true
        }
      }
    });

    // Criar order bumps para o produto
    const orderBumps = [
      {
        productId: product.id,
        name: 'Mentoria Individual (1 hora)',
        description: 'Sessão de mentoria individual de 1 hora para tirar dúvidas e acelerar seus resultados',
        valueCents: 9700, // R$ 97,00
        type: 'ORDER_BUMP',
        isActive: true,
        sortOrder: 1
      },
      {
        productId: product.id,
        name: 'E-book: 100 Estratégias de Vendas',
        description: 'E-book exclusivo com 100 estratégias comprovadas para aumentar suas vendas online',
        valueCents: 4700, // R$ 47,00
        type: 'ORDER_BUMP',
        isActive: true,
        sortOrder: 2
      },
      {
        productId: product.id,
        name: 'Templates de Campanhas',
        description: 'Pacote com 50 templates prontos para campanhas no Facebook, Instagram e Google Ads',
        valueCents: 6700, // R$ 67,00
        type: 'ORDER_BUMP',
        isActive: true,
        sortOrder: 3
      }
    ];

    // Criar os order bumps
    for (const bump of orderBumps) {
      await db.offer.create({
        data: bump
      });
    }

    console.log('✅ Produto de teste criado com sucesso!');
    console.log('📦 Produto ID:', product.id);
    console.log('🎯 Nome:', product.name);
    console.log('💰 Preço:', `R$ ${(product.priceCents / 100).toFixed(2)}`);
    console.log('🔗 URL do checkout:', `http://localhost:3000/checkout/${product.id}`);
    console.log('🎁 Order bumps criados:', orderBumps.length);

    return product;
  } catch (error) {
    console.error('❌ Erro ao criar produto de teste:', error);
    throw error;
  }
}

createTestProductWithBumps()
  .then(() => {
    console.log('🎉 Script executado com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Erro ao executar script:', error);
    process.exit(1);
  });
