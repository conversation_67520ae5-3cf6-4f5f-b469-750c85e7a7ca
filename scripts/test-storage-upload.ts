#!/usr/bin/env tsx

/**
 * Script para testar o upload de arquivos usando @repo/storage
 *
 * Uso:
 * npx tsx scripts/test-storage-upload.ts
 *
 * Certifique-se de ter as seguintes variáveis de ambiente configuradas:
 * - S3_ENDPOINT
 * - S3_REGION
 * - S3_ACCESS_KEY_ID
 * - S3_SECRET_ACCESS_KEY
 */

import { getSignedUploadUrl, getSignedUrl } from "@repo/storage";
import { STORAGE_CONFIG } from "@repo/storage";

async function testStorageConnection() {
  console.log("🧪 Testando conexão com storage...");
  console.log("📋 Configuração:");
  console.log(`   - Buckets: ${Object.keys(STORAGE_CONFIG.buckets).join(", ")}`);
  console.log(`   - Tamanho máximo: ${STORAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`);
  console.log(`   - Tipos permitidos: ${STORAGE_CONFIG.allowedTypes.join(", ")}`);
  console.log("");

  // Verificar variáveis de ambiente
  const requiredEnvVars = [
    "S3_ENDPOINT",
    "S3_ACCESS_KEY_ID",
    "S3_SECRET_ACCESS_KEY"
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error("❌ Variáveis de ambiente ausentes:");
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.log("\n💡 Configure as variáveis de ambiente antes de executar o teste.");
    process.exit(1);
  }

  console.log("✅ Variáveis de ambiente configuradas");
  console.log(`   - S3_ENDPOINT: ${process.env.S3_ENDPOINT}`);
  console.log(`   - S3_REGION: ${process.env.S3_REGION || "auto"}`);
  console.log(`   - S3_ACCESS_KEY_ID: ${process.env.S3_ACCESS_KEY_ID?.substring(0, 8)}...`);
  console.log("");

  try {
    // Testar conexão com o bucket de banners de checkout
    const testPath = `test-${Date.now()}.txt`;
    const bucketName = STORAGE_CONFIG.buckets.checkoutBanners;

    console.log(`🔄 Testando upload para bucket: ${bucketName}`);
    console.log(`📁 Caminho de teste: ${testPath}`);

    // Obter URL de upload assinada
    const uploadUrl = await getSignedUploadUrl(testPath, { bucket: bucketName });
    console.log("✅ URL de upload obtida com sucesso");
    console.log(`   - URL: ${uploadUrl.substring(0, 100)}...`);

    // Simular upload de arquivo de teste
    const testContent = `Teste de upload - ${new Date().toISOString()}`;
    const testFile = new Blob([testContent], { type: "text/plain" });

    console.log("🔄 Fazendo upload do arquivo de teste...");
    const uploadResponse = await fetch(uploadUrl, {
      method: "PUT",
      body: testFile,
      headers: {
        "Content-Type": "text/plain",
      },
    });

    if (!uploadResponse.ok) {
      throw new Error(`Upload falhou: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    console.log("✅ Upload realizado com sucesso");

    // Obter URL de acesso ao arquivo
    console.log("🔄 Obtendo URL de acesso ao arquivo...");
    const accessUrl = await getSignedUrl(testPath, {
      bucket: bucketName,
      expiresIn: 60 * 60 // 1 hora
    });

    console.log("✅ URL de acesso obtida com sucesso");
    console.log(`   - URL: ${accessUrl.substring(0, 100)}...`);

    // Testar acesso ao arquivo
    console.log("🔄 Testando acesso ao arquivo...");
    const accessResponse = await fetch(accessUrl);

    if (!accessResponse.ok) {
      throw new Error(`Acesso ao arquivo falhou: ${accessResponse.status} ${accessResponse.statusText}`);
    }

    const downloadedContent = await accessResponse.text();

    if (downloadedContent === testContent) {
      console.log("✅ Arquivo acessado e conteúdo verificado com sucesso");
    } else {
      console.log("⚠️  Conteúdo do arquivo não confere");
    }

    console.log("\n🎉 Teste de storage concluído com sucesso!");
    console.log("✅ Conexão com Cloudflare R2 funcionando corretamente");
    console.log("✅ Upload de arquivos funcionando");
    console.log("✅ Acesso a arquivos funcionando");

  } catch (error) {
    console.error("\n❌ Erro durante o teste de storage:");
    console.error(error);

    if (error instanceof Error) {
      if (error.message.includes("Missing env variable")) {
        console.log("\n💡 Verifique se todas as variáveis de ambiente estão configuradas corretamente.");
      } else if (error.message.includes("Could not get signed")) {
        console.log("\n💡 Verifique se as credenciais do Cloudflare R2 estão corretas.");
        console.log("💡 Verifique se o bucket 'checkout-banners' existe no Cloudflare R2.");
      } else if (error.message.includes("403") || error.message.includes("Forbidden")) {
        console.log("\n💡 Verifique se as credenciais têm permissão para acessar o bucket.");
      } else if (error.message.includes("404") || error.message.includes("Not Found")) {
        console.log("\n💡 Verifique se o bucket 'checkout-banners' existe no Cloudflare R2.");
      }
    }

    process.exit(1);
  }
}

// Executar teste
testStorageConnection().catch(console.error);
