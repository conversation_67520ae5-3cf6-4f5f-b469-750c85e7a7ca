#!/usr/bin/env tsx

/**
 * Script para testar o hook useFileUpload
 *
 * Uso:
 * npx tsx scripts/test-upload-hook.ts
 */

import { useFileUpload } from "@repo/storage";

// Simular o ambiente React para testar o hook
function createMockFile(content: string, filename: string, type: string): File {
  const blob = new Blob([content], { type });
  return new File([blob], filename, { type });
}

async function testUploadHook() {
  console.log("🧪 Testando hook useFileUpload...\n");

  // Verificar variáveis de ambiente primeiro
  const requiredEnvVars = [
    "S3_ENDPOINT",
    "S3_ACCESS_KEY_ID",
    "S3_SECRET_ACCESS_KEY"
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error("❌ Variáveis de ambiente ausentes:");
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.log("\n💡 Configure as variáveis de ambiente antes de executar o teste.");
    process.exit(1);
  }

  console.log("✅ Variáveis de ambiente configuradas");

  // Criar arquivo de teste
  const testContent = `Teste do hook useFileUpload - ${new Date().toISOString()}`;
  const testFile = createMockFile(testContent, "test-hook.txt", "text/plain");

  console.log(`📁 Arquivo de teste criado: ${testFile.name} (${testFile.size} bytes)`);

  // Simular o uso do hook
  let uploadSuccess = false;
  let uploadError: string | null = null;

  const { uploadFile, isUploading, uploadProgress } = useFileUpload({
    bucket: "checkoutBanners",
    onSuccess: (url) => {
      console.log("✅ Upload realizado com sucesso!");
      console.log(`   URL: ${url.substring(0, 100)}...`);
      uploadSuccess = true;
    },
    onError: (error) => {
      console.error("❌ Erro no upload:", error);
      uploadError = error;
    },
  });

  try {
    console.log("\n🔄 Iniciando upload...");
    console.log(`   Status: ${isUploading ? "Fazendo upload..." : "Aguardando"}`);
    console.log(`   Progresso: ${uploadProgress}%`);

    const result = await uploadFile(testFile, `hook-test-${Date.now()}.txt`);

    if (uploadSuccess) {
      console.log("\n🎉 Hook useFileUpload funcionando corretamente!");
      console.log("✅ Upload de arquivos via hook está operacional");
    } else if (uploadError) {
      console.log("\n❌ Hook useFileUpload falhou:");
      console.log(`   Erro: ${uploadError}`);
    } else {
      console.log("\n⚠️  Upload concluído mas sem callback de sucesso");
    }

  } catch (error) {
    console.error("\n❌ Erro durante o teste do hook:");
    console.error(error);
  }
}

testUploadHook().catch(console.error);
