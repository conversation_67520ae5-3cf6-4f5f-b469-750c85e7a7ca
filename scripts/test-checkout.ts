#!/usr/bin/env tsx

import { db } from '../packages/database/prisma/client';
import { nanoid } from 'nanoid';

async function createTestData() {
	console.log('🚀 Criando dados de teste para o checkout...');

	try {
		// 1. Criar organização de teste
		const testOrg = await db.organization.upsert({
			where: { slug: 'test-org' },
			update: {},
			create: {
				name: 'Organização de Teste',
				slug: 'test-org',
				description: 'Organização para testes do checkout',
			},
		});
		console.log('✅ Organização criada:', testOrg.name);

		// 2. Criar usu<PERSON><PERSON> professor
		const teacher = await db.user.upsert({
			where: { email: '<EMAIL>' },
			update: {},
			create: {
				email: '<EMAIL>',
				name: 'Professor <PERSON>',
				role: 'TEACHER',
				emailVerified: true,
				onboardingComplete: true,
			},
		});
		console.log('✅ <PERSON> criad<PERSON>:', teacher.name);

		// 3. Criar membro da organização
		await db.member.upsert({
			where: {
				organizationId_userId: {
					organizationId: testOrg.id,
					userId: teacher.id,
				},
			},
			update: {},
			create: {
				organizationId: testOrg.id,
				userId: teacher.id,
				role: 'ADMIN',
			},
		});

		// 4. Criar categoria
		const category = await db.category.upsert({
			where: {
				organizationId_slug: {
					organizationId: testOrg.id,
					slug: 'cursos',
				},
			},
			update: {},
			create: {
				organizationId: testOrg.id,
				name: 'Cursos',
				slug: 'cursos',
				description: 'Categoria para cursos',
			},
		});
		console.log('✅ Categoria criada:', category.name);

		// 5. Criar produto de teste
		const product = await db.product.upsert({
			where: {
				organizationId_slug: {
					organizationId: testOrg.id,
					slug: 'curso-marketing-digital',
				},
			},
			update: {},
			create: {
				organizationId: testOrg.id,
				creatorId: teacher.id,
				name: 'Curso de Marketing Digital Completo',
				slug: 'curso-marketing-digital',
				description: 'Aprenda marketing digital do zero ao avançado com este curso completo.',
				shortDescription: 'Curso completo de marketing digital',
				priceCents: 29700, // R$ 297,00
				comparePriceCents: 49700, // R$ 497,00
				currency: 'BRL',
				type: 'COURSE',
				status: 'PUBLISHED',
				visibility: 'PUBLIC',
				categoryId: category.id,
				thumbnail: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
				checkoutType: 'DEFAULT',
				settings: {
					banner: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=200&fit=crop',
					styles: {
						formContainer: {
							backgroundColor: '#f8fafc',
						},
					},
				},
				certificate: true,
				downloadable: true,
				duration: 1200, // 20 horas
				level: 'BEGINNER',
				language: 'pt-BR',
			},
		});
		console.log('✅ Produto criado:', product.name);

		// 6. Criar módulos do curso
		const module1 = await db.productModule.create({
			data: {
				productId: product.id,
				title: 'Introdução ao Marketing Digital',
				description: 'Fundamentos do marketing digital',
				order: 1,
				duration: 120,
				isPublished: true,
			},
		});

		const module2 = await db.productModule.create({
			data: {
				productId: product.id,
				title: 'Redes Sociais',
				description: 'Como usar redes sociais para marketing',
				order: 2,
				duration: 180,
				isPublished: true,
			},
		});

		// 7. Criar aulas
		await db.productLesson.createMany({
			data: [
				{
					moduleId: module1.id,
					title: 'O que é Marketing Digital',
					description: 'Conceitos básicos',
					order: 1,
					duration: 30,
					isPublished: true,
					isFree: true,
				},
				{
					moduleId: module1.id,
					title: 'Evolução do Marketing',
					description: 'Como o marketing evoluiu',
					order: 2,
					duration: 45,
					isPublished: true,
					isFree: false,
				},
				{
					moduleId: module2.id,
					title: 'Facebook e Instagram',
					description: 'Marketing no Facebook e Instagram',
					order: 1,
					duration: 60,
					isPublished: true,
					isFree: false,
				},
			],
		});

		// 8. Criar ofertas (order bumps)
		const offer1 = await db.offer.create({
			data: {
				productId: product.id,
				name: 'E-book: Guia de Redes Sociais',
				type: 'ORDER_BUMP',
				valueCents: 4900, // R$ 49,00
				currency: 'BRL',
				isActive: true,
			},
		});

		const offer2 = await db.offer.create({
			data: {
				productId: product.id,
				name: 'Consultoria 1h',
				type: 'ORDER_BUMP',
				valueCents: 19700, // R$ 197,00
				currency: 'BRL',
				isActive: true,
			},
		});

		console.log('✅ Ofertas criadas:', offer1.name, 'e', offer2.name);

		// 9. Criar usuário cliente de teste
		const customer = await db.user.upsert({
			where: { email: '<EMAIL>' },
			update: {},
			create: {
				email: '<EMAIL>',
				name: 'Cliente Teste',
				role: 'USER',
				emailVerified: true,
				onboardingComplete: true,
			},
		});
		console.log('✅ Cliente criado:', customer.name);

		console.log('\n🎉 Dados de teste criados com sucesso!');
		console.log('\n📋 Informações para teste:');
		console.log(`- Produto ID: ${product.id}`);
		console.log(`- URL do Checkout: /checkout/${product.id}`);
		console.log(`- URL de Teste: /checkout/test`);
		console.log(`- Email do Cliente: ${customer.email}`);
		console.log(`- Email do Professor: ${teacher.email}`);

	} catch (error) {
		console.error('❌ Erro ao criar dados de teste:', error);
		process.exit(1);
	}
}

async function cleanupTestData() {
	console.log('🧹 Limpando dados de teste...');

	try {
		// Deletar em ordem para respeitar as foreign keys
		await db.offerInteraction.deleteMany({
			where: {
				offer: {
					product: {
						organization: {
							slug: 'test-org',
						},
					},
				},
			},
		});

		await db.offer.deleteMany({
			where: {
				product: {
					organization: {
						slug: 'test-org',
					},
				},
			},
		});

		await db.productLesson.deleteMany({
			where: {
				module: {
					product: {
						organization: {
							slug: 'test-org',
						},
					},
				},
			},
		});

		await db.productModule.deleteMany({
			where: {
				product: {
					organization: {
						slug: 'test-org',
					},
				},
			},
		});

		await db.product.deleteMany({
			where: {
				organization: {
					slug: 'test-org',
				},
			},
		});

		await db.category.deleteMany({
			where: {
				organization: {
					slug: 'test-org',
				},
			},
		});

		await db.member.deleteMany({
			where: {
				organization: {
					slug: 'test-org',
				},
			},
		});

		await db.organization.deleteMany({
			where: {
				slug: 'test-org',
			},
		});

		await db.user.deleteMany({
			where: {
				email: {
					in: ['<EMAIL>', '<EMAIL>'],
				},
			},
		});

		console.log('✅ Dados de teste removidos com sucesso!');
	} catch (error) {
		console.error('❌ Erro ao limpar dados de teste:', error);
		process.exit(1);
	}
}

async function main() {
	const command = process.argv[2];

	switch (command) {
		case 'create':
			await createTestData();
			break;
		case 'cleanup':
			await cleanupTestData();
			break;
		default:
			console.log('Uso: tsx scripts/test-checkout.ts [create|cleanup]');
			console.log('  create  - Cria dados de teste para o checkout');
			console.log('  cleanup - Remove dados de teste');
			break;
	}

	await db.$disconnect();
}

main().catch((error) => {
	console.error('Erro:', error);
	process.exit(1);
});
