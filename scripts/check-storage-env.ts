#!/usr/bin/env tsx

/**
 * Script para verificar se as variáveis de ambiente do storage estão configuradas
 *
 * Uso:
 * npx tsx scripts/check-storage-env.ts
 */

import { STORAGE_CONFIG } from "@repo/storage";

function checkStorageEnvironment() {
  console.log("🔍 Verificando configuração do storage...\n");

  // Verificar variáveis de ambiente
  const envVars = {
    S3_ENDPOINT: {
      value: process.env.S3_ENDPOINT,
      required: true,
      description: "Endpoint do Cloudflare R2 (ex: https://account-id.r2.cloudflarestorage.com)"
    },
    S3_REGION: {
      value: process.env.S3_REGION || "auto",
      required: false,
      description: "Região do Cloudflare R2 (padrão: auto)"
    },
    S3_ACCESS_KEY_ID: {
      value: process.env.S3_ACCESS_KEY_ID,
      required: true,
      description: "Access Key ID do Cloudflare R2"
    },
    S3_SECRET_ACCESS_KEY: {
      value: process.env.S3_SECRET_ACCESS_KEY,
      required: true,
      description: "Secret Access Key do Cloudflare R2"
    }
  };

  console.log("📋 Variáveis de ambiente:");
  let allConfigured = true;

  Object.entries(envVars).forEach(([key, config]) => {
    const status = config.value ? "✅" : (config.required ? "❌" : "⚠️");
    const value = config.value ?
      (key.includes("SECRET") ? "***" + config.value.slice(-4) : config.value) :
      "Não configurado";

    console.log(`   ${status} ${key}: ${value}`);
    console.log(`      ${config.description}`);

    if (config.required && !config.value) {
      allConfigured = false;
    }
  });

  console.log("\n📦 Configuração do storage:");
  console.log(`   Buckets disponíveis: ${Object.keys(STORAGE_CONFIG.buckets).join(", ")}`);
  console.log(`   Tamanho máximo de arquivo: ${STORAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`);
  console.log(`   Tipos de arquivo permitidos: ${STORAGE_CONFIG.allowedTypes.join(", ")}`);

  if (allConfigured) {
    console.log("\n✅ Todas as variáveis de ambiente necessárias estão configuradas!");
    console.log("🚀 Pronto para testar o upload com: npx tsx scripts/test-storage-simple.ts");
  } else {
    console.log("\n❌ Algumas variáveis de ambiente estão ausentes.");
    console.log("\n💡 Para configurar o Cloudflare R2:");
    console.log("1. Acesse o Cloudflare Dashboard");
    console.log("2. Vá para R2 Object Storage");
    console.log("3. Crie um bucket chamado 'checkout-banners'");
    console.log("4. Vá para 'Manage R2 API tokens'");
    console.log("5. Crie um token com permissões de Object Read & Write");
    console.log("6. Configure as variáveis de ambiente no seu .env");
    console.log("\n📝 Exemplo de configuração no .env:");
    console.log("S3_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com");
    console.log("S3_REGION=auto");
    console.log("S3_ACCESS_KEY_ID=your-access-key-id");
    console.log("S3_SECRET_ACCESS_KEY=your-secret-access-key");
  }

  return allConfigured;
}

checkStorageEnvironment();
