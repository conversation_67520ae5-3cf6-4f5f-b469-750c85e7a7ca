#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";
import { createHash } from "crypto";

const db = new PrismaClient();

async function createAdminPassword() {
  console.log("🔐 Criando senha para o admin...\n");

  try {
    // Buscar o usuário admin
    const adminUser = await db.user.findFirst({
      where: { email: "<EMAIL>" }
    });

    if (!adminUser) {
      console.error("❌ Usuário admin não encontrado!");
      process.exit(1);
    }

    // Senha simples para o admin
    const adminPassword = "admin123456";

    // Hash simples usando SHA-256 (para desenvolvimento)
    const hashedPassword = createHash('sha256').update(adminPassword).digest('hex');

    // Verificar se já existe uma conta
    const existingAccount = await db.account.findFirst({
      where: {
        userId: adminUser.id,
        providerId: "credential",
      },
    });

    if (existingAccount) {
      // Atualizar senha existente
      await db.account.update({
        where: { id: existingAccount.id },
        data: {
          password: hashedPassword,
          updatedAt: new Date(),
        },
      });
    } else {
      // Criar nova conta
      await db.account.create({
        data: {
          userId: adminUser.id,
          providerId: "credential",
          accountId: adminUser.id,
          password: hashedPassword,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }

    console.log("✅ Senha criada com sucesso!");
    console.log("\n🔐 INFORMAÇÕES DE LOGIN:");
    console.log("==========================");
    console.log(`📧 Email: <EMAIL>`);
    console.log(`🔑 Senha: ${adminPassword}`);
    console.log(`🌐 URL: http://localhost:3001/auth/login`);
    console.log(`👤 Role: SUPER_ADMIN`);
    console.log(`🏢 Organização: SupGateway Admin (OWNER)`);

    console.log("\n🚀 FUNCIONALIDADES DISPONÍVEIS:");
    console.log("=================================");
    console.log("✅ Acesso completo ao sistema");
    console.log("✅ Gestão de organizações");
    console.log("✅ Criação e gestão de produtos");
    console.log("✅ Sistema de vendas e pagamentos");
    console.log("✅ Dashboard administrativo");
    console.log("✅ Configurações avançadas");

  } catch (error) {
    console.error("❌ Erro ao criar senha:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar
createAdminPassword();
