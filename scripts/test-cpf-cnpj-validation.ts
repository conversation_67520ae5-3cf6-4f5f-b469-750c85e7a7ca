// scripts/test-cpf-cnpj-validation.ts
// Script para testar a validação de CPF/CNPJ

// Função para validar CPF
function validateCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, '');

  if (cleanCPF.length !== 11) return false;
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false; // Todos os dígitos iguais

  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

  return true;
}

// Função para validar CNPJ
function validateCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, '');

  if (cleanCNPJ.length !== 14) return false;
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false; // Todos os dígitos iguais

  let sum = 0;
  let weight = 2;
  for (let i = 11; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  let remainder = sum % 11;
  const firstDigit = remainder < 2 ? 0 : 11 - remainder;
  if (firstDigit !== parseInt(cleanCNPJ.charAt(12))) return false;

  sum = 0;
  weight = 2;
  for (let i = 12; i >= 0; i--) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weight;
    weight = weight === 9 ? 2 : weight + 1;
  }
  remainder = sum % 11;
  const secondDigit = remainder < 2 ? 0 : 11 - remainder;
  if (secondDigit !== parseInt(cleanCNPJ.charAt(13))) return false;

  return true;
}

// Função para formatar CPF
function formatCPF(value: string): string {
  const numbers = value.replace(/\D/g, '');
  if (numbers.length <= 3) return numbers;
  if (numbers.length <= 6) return `${numbers.slice(0, 3)}.${numbers.slice(3)}`;
  if (numbers.length <= 9) return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6)}`;
  return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9, 11)}`;
}

// Função para formatar CNPJ
function formatCNPJ(value: string): string {
  const numbers = value.replace(/\D/g, '');
  if (numbers.length <= 2) return numbers;
  if (numbers.length <= 5) return `${numbers.slice(0, 2)}.${numbers.slice(2)}`;
  if (numbers.length <= 8) return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5)}`;
  if (numbers.length <= 12) return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5, 8)}/${numbers.slice(8)}`;
  return `${numbers.slice(0, 2)}.${numbers.slice(2, 5)}.${numbers.slice(5, 8)}/${numbers.slice(8, 12)}-${numbers.slice(12, 14)}`;
}

// Testes de CPF
console.log('=== TESTES DE CPF ===');
const cpfTests = [
  { input: '11144477735', expected: true, description: 'CPF válido' },
  { input: '111.444.777-35', expected: true, description: 'CPF válido com formatação' },
  { input: '11111111111', expected: false, description: 'CPF inválido (todos iguais)' },
  { input: '12345678901', expected: false, description: 'CPF inválido (sequência)' },
  { input: '1114447773', expected: false, description: 'CPF inválido (10 dígitos)' },
  { input: '111444777356', expected: false, description: 'CPF inválido (12 dígitos)' },
];

cpfTests.forEach(test => {
  const result = validateCPF(test.input);
  const status = result === test.expected ? '✅' : '❌';
  console.log(`${status} ${test.description}: ${test.input} -> ${result} (esperado: ${test.expected})`);
});

// Testes de CNPJ
console.log('\n=== TESTES DE CNPJ ===');
const cnpjTests = [
  { input: '11222333000181', expected: true, description: 'CNPJ válido' },
  { input: '11.222.333/0001-81', expected: true, description: 'CNPJ válido com formatação' },
  { input: '11111111111111', expected: false, description: 'CNPJ inválido (todos iguais)' },
  { input: '12345678901234', expected: false, description: 'CNPJ inválido (sequência)' },
  { input: '1122233300018', expected: false, description: 'CNPJ inválido (13 dígitos)' },
  { input: '112223330001812', expected: false, description: 'CNPJ inválido (15 dígitos)' },
];

cnpjTests.forEach(test => {
  const result = validateCNPJ(test.input);
  const status = result === test.expected ? '✅' : '❌';
  console.log(`${status} ${test.description}: ${test.input} -> ${result} (esperado: ${test.expected})`);
});

// Testes de formatação
console.log('\n=== TESTES DE FORMATAÇÃO ===');
const formatTests = [
  { input: '11144477735', expected: '111.444.777-35', type: 'CPF' },
  { input: '11222333000181', expected: '11.222.333/0001-81', type: 'CNPJ' },
  { input: '111444777', expected: '111.444.777', type: 'CPF (parcial)' },
  { input: '112223330001', expected: '11.222.333/0001', type: 'CNPJ (parcial)' },
];

formatTests.forEach(test => {
  const result = test.type.includes('CPF') ? formatCPF(test.input) : formatCNPJ(test.input);
  const status = result === test.expected ? '✅' : '❌';
  console.log(`${status} ${test.type}: ${test.input} -> ${result} (esperado: ${test.expected})`);
});

// Teste de detecção automática
console.log('\n=== TESTE DE DETECÇÃO AUTOMÁTICA ===');
const autoDetectionTests = [
  { input: '11144477735', expectedType: 'CPF' },
  { input: '11222333000181', expectedType: 'CNPJ' },
  { input: '111444777', expectedType: 'CPF (parcial)' },
  { input: '112223330001', expectedType: 'CNPJ (parcial)' },
];

autoDetectionTests.forEach(test => {
  const cleanInput = test.input.replace(/\D/g, '');
  let detectedType = '';
  if (cleanInput.length <= 11) {
    detectedType = cleanInput.length === 11 ? 'CPF' : 'CPF (parcial)';
  } else {
    detectedType = cleanInput.length === 14 ? 'CNPJ' : 'CNPJ (parcial)';
  }

  const status = detectedType === test.expectedType ? '✅' : '❌';
  console.log(`${status} Detecção: ${test.input} -> ${detectedType} (esperado: ${test.expectedType})`);
});

console.log('\n=== RESUMO ===');
console.log('✅ Validação de CPF implementada');
console.log('✅ Validação de CNPJ implementada');
console.log('✅ Formatação automática implementada');
console.log('✅ Detecção automática de tipo implementada');
console.log('✅ Tooltip explicativo implementado');
console.log('✅ Experiência otimizada para não quebrar vendas');
