import { PrismaClient } from "@prisma/client";
import { readFileSync } from "fs";
import { join } from "path";

// Carregar variáveis de ambiente do arquivo .env
const envFile = readFileSync(join(process.cwd(), '.env'), 'utf8');
const envVars = envFile.split('\n').reduce((acc, line) => {
  const equalIndex = line.indexOf('=');
  if (equalIndex > 0) {
    const key = line.substring(0, equalIndex).trim();
    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
    acc[key] = value;
  }
  return acc;
}, {} as Record<string, string>);

// Definir variáveis de ambiente
Object.assign(process.env, envVars);

const prisma = new PrismaClient();

const COMMUNITIES_DATA = [
  {
    name: "That Pickleball School",
    slug: "that-pickleball-school",
    description: "Learn strategies to play better pickleball right away! With top pro <PERSON> & his team of coaches.",
    shortDescription: "Learn strategies to play better pickleball right away!",
    thumbnail: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop",
    memberCount: 1100,
    maxMembers: 2000,
    priceCents: 3900, // $39/month
    currency: "USD",
    billingType: "MONTHLY",
    accessType: "PAID",
    isPublic: true,
    isActive: true,
    tags: ["sports", "pickleball", "coaching", "fitness"],
    features: [
      "Video lessons from pro coaches",
      "Live training sessions",
      "Community challenges",
      "Equipment recommendations"
    ],
    platformType: "DISCORD",
    platformUrl: "https://discord.gg/pickleball-school",
  },
  {
    name: "AI Automation Society (AIS)",
    slug: "ai-automation-society",
    description: "A community for mastering AI-driven automation and AI agents. Learn, collaborate, and optimize your workflows!",
    shortDescription: "Master AI-driven automation and AI agents",
    thumbnail: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop",
    memberCount: 143800,
    maxMembers: 200000,
    priceCents: 0, // Free
    currency: "USD",
    billingType: "ONE_TIME",
    accessType: "FREE",
    isPublic: true,
    isActive: true,
    tags: ["ai", "automation", "technology", "productivity"],
    features: [
      "AI tool tutorials",
      "Automation templates",
      "Expert interviews",
      "Project showcases"
    ],
    platformType: "DISCORD",
    platformUrl: "https://discord.gg/ai-automation",
  },
  {
    name: "Calligraphy Skool",
    slug: "calligraphy-skool",
    description: "Learn modern calligraphy the fun, easy way! With sisters Jordan & Jillian",
    shortDescription: "Learn modern calligraphy the fun, easy way!",
    thumbnail: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop",
    memberCount: 1400,
    maxMembers: 3000,
    priceCents: 900, // $9/month
    currency: "USD",
    billingType: "MONTHLY",
    accessType: "PAID",
    isPublic: true,
    isActive: true,
    tags: ["art", "calligraphy", "handwriting", "creative"],
    features: [
      "Step-by-step tutorials",
      "Practice worksheets",
      "Live workshops",
      "Feedback from instructors"
    ],
    platformType: "DISCORD",
    platformUrl: "https://discord.gg/calligraphy-skool",
  },
  {
    name: "The Lady Change",
    slug: "the-lady-change",
    description: "THE #1 community for menopausal (peri & post) women to lose weight, get healthier and regain their confidence!",
    shortDescription: "Community for menopausal women to lose weight and regain confidence",
    thumbnail: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
    memberCount: 1500,
    maxMembers: 5000,
    priceCents: 4900, // $49/month
    currency: "USD",
    billingType: "MONTHLY",
    accessType: "PAID",
    isPublic: true,
    isActive: true,
    tags: ["health", "menopause", "weight-loss", "women"],
    features: [
      "Nutrition guidance",
      "Exercise routines",
      "Hormone support",
      "Peer support groups"
    ],
    platformType: "DISCORD",
    platformUrl: "https://discord.gg/lady-change",
  },
  {
    name: "Brotherhood Of Scent",
    slug: "brotherhood-of-scent",
    description: "#1 Fragrance Community Our mission is to help YOU leverage the power of scent to become the man you know yourself to...",
    shortDescription: "#1 Fragrance Community for men",
    thumbnail: "https://images.unsplash.com/photo-1541643600914-78b084683601?w=400&h=300&fit=crop",
    memberCount: 9100,
    maxMembers: 15000,
    priceCents: 0, // Free
    currency: "USD",
    billingType: "ONE_TIME",
    accessType: "FREE",
    isPublic: true,
    isActive: true,
    tags: ["fragrance", "men", "lifestyle", "grooming"],
    features: [
      "Fragrance reviews",
      "Scent matching",
      "Expert recommendations",
      "Community reviews"
    ],
    platformType: "DISCORD",
    platformUrl: "https://discord.gg/brotherhood-scent",
  },
  {
    name: "Abbew Crew",
    slug: "abbew-crew",
    description: "My mission is to help people reclaim their health, body and energy. Achieving fat loss or muscle building is not complicated. Tr...",
    shortDescription: "Reclaim your health, body and energy",
    thumbnail: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
    memberCount: 18900,
    maxMembers: 25000,
    priceCents: 39700, // $397 one-time
    currency: "USD",
    billingType: "ONE_TIME",
    accessType: "PAID",
    isPublic: true,
    isActive: true,
    tags: ["fitness", "health", "muscle-building", "fat-loss"],
    features: [
      "Custom workout plans",
      "Nutrition guidance",
      "Progress tracking",
      "1-on-1 coaching"
    ],
    platformType: "DISCORD",
    platformUrl: "https://discord.gg/abbew-crew",
  },
];

async function main() {
  console.log("🌱 Seeding communities...");

  // Get the first organization
  const organization = await prisma.organization.findFirst();
  if (!organization) {
    console.error("❌ No organization found. Please create an organization first.");
    return;
  }

  console.log(`📊 Using organization: ${organization.name}`);

  // Create communities
  const communities = [];
  for (const communityData of COMMUNITIES_DATA) {
    const community = await prisma.community.upsert({
      where: {
        organizationId_slug: {
          organizationId: organization.id,
          slug: communityData.slug,
        },
      },
      update: {},
      create: {
        organizationId: organization.id,
        ...communityData,
      },
    });
    communities.push(community);
    console.log(`✅ Created community: ${community.name}`);
  }

  console.log(`🎉 Successfully created ${communities.length} communities!`);
}

main()
  .catch((e) => {
    console.error("❌ Error seeding communities:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
