#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";
import { auth } from "@repo/auth";

const db = new PrismaClient();

async function seedAdmin() {
  console.log("🌱 Iniciando seed do banco de dados...\n");

  try {
    // 1. Criar usuário admin
    console.log("👤 Criando usuário admin...");

    const adminPassword = "admin123456";
    const authContext = await auth.$context;
    const hashedPassword = await authContext.password.hash(adminPassword);

    const adminUser = await db.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Admin SupGateway",
        email: "<EMAIL>",
        username: "admin",
        role: "SUPER_ADMIN",
        emailVerified: true,
        onboardingComplete: true,
      },
    });

    // Criar conta de credenciais para o admin
    await db.account.upsert({
      where: {
        providerId_accountId: {
          providerId: "credential",
          accountId: adminUser.id,
        },
      },
      update: {},
      create: {
        userId: adminUser.id,
        providerId: "credential",
        accountId: adminUser.id,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    console.log(`✅ Admin criado: ${adminUser.email}`);

    // 2. Criar organização admin
    console.log("🏢 Criando organização admin...");

    const adminOrganization = await db.organization.upsert({
      where: { slug: "supgateway-admin" },
      update: {},
      create: {
        name: "SupGateway Admin",
        slug: "supgateway-admin",
        subscriptionPlan: "enterprise",
        subscriptionStatus: "active",
        enableCustomBranding: true,
        enableCustomDomain: true,
        enableAdvancedPayments: true,
        maxProducts: 1000,
        maxUsers: 10000,
        maxStorageGB: 1000,
        maxTransactions: 100000,
        currency: "BRL",
        language: "pt",
        timezone: "America/Sao_Paulo",
      },
    });

    console.log(`✅ Organização criada: ${adminOrganization.name}`);

    // 3. Adicionar admin como owner da organização
    console.log("👑 Adicionando admin como owner da organização...");

    await db.member.upsert({
      where: {
        organizationId_userId: {
          organizationId: adminOrganization.id,
          userId: adminUser.id,
        },
      },
      update: {},
      create: {
        organizationId: adminOrganization.id,
        userId: adminUser.id,
        role: "owner",
        createdAt: new Date(),
      },
    });

    console.log("✅ Admin adicionado como owner da organização");

    // 4. Criar perfil de professor para o admin
    console.log("👨‍🏫 Criando perfil de professor...");

    await db.teacherProfile.upsert({
      where: { userId: adminUser.id },
      update: {},
      create: {
        userId: adminUser.id,
        bio: "Administrador do SupGateway - Plataforma completa para criação e venda de produtos digitais",
        expertise: ["Plataforma", "Gestão", "Desenvolvimento", "Vendas Online"],
        experience: "Experiência completa na plataforma SupGateway",
        education: "Especialista em plataformas de e-learning e vendas digitais",
        website: "https://supgateway.com",
        isVerified: true,
      },
    });

    console.log("✅ Perfil de professor criado");

    // 5. Criar perfil de afiliado para o admin
    console.log("🤝 Criando perfil de afiliado...");

    await db.affiliateProfile.upsert({
      where: { userId: adminUser.id },
      update: {},
      create: {
        userId: adminUser.id,
        commissionCents: 0,
        totalEarningsCents: 0,
        currency: "BRL",
        isActive: true,
      },
    });

    console.log("✅ Perfil de afiliado criado");

    // 6. Criar algumas categorias de exemplo
    console.log("📂 Criando categorias de exemplo...");

    const categories = [
      { name: "Tecnologia", slug: "tecnologia", description: "Cursos e produtos de tecnologia" },
      { name: "Marketing", slug: "marketing", description: "Estratégias e ferramentas de marketing" },
      { name: "Negócios", slug: "negocios", description: "Empreendedorismo e gestão de negócios" },
      { name: "Design", slug: "design", description: "Design gráfico e UX/UI" },
    ];

    for (const category of categories) {
      await db.category.upsert({
        where: {
          organizationId_slug: {
            organizationId: adminOrganization.id,
            slug: category.slug,
          },
        },
        update: {},
        create: {
          organizationId: adminOrganization.id,
          name: category.name,
          slug: category.slug,
          description: category.description,
        },
      });
    }

    console.log("✅ Categorias criadas");

    // 7. Verificar dados criados
    console.log("\n📊 Dados criados:");
    console.log("==================");

    const userCount = await db.user.count();
    const orgCount = await db.organization.count();
    const memberCount = await db.member.count();
    const categoryCount = await db.category.count();

    console.log(`👥 Usuários: ${userCount}`);
    console.log(`🏢 Organizações: ${orgCount}`);
    console.log(`👑 Membros: ${memberCount}`);
    console.log(`📂 Categorias: ${categoryCount}`);

    console.log("\n🎉 Seed concluído com sucesso!");
    console.log("\n🔐 INFORMAÇÕES DE LOGIN:");
    console.log("==========================");
    console.log(`📧 Email: <EMAIL>`);
    console.log(`🔑 Senha: ${adminPassword}`);
    console.log(`🌐 URL: http://localhost:3001/auth/login`);
    console.log(`👤 Role: SUPER_ADMIN`);
    console.log(`🏢 Organização: ${adminOrganization.name}`);
    console.log(`👑 Role na Org: OWNER`);

    console.log("\n🚀 FUNCIONALIDADES DISPONÍVEIS:");
    console.log("=================================");
    console.log("✅ Acesso completo ao sistema");
    console.log("✅ Gestão de organizações");
    console.log("✅ Criação e gestão de produtos");
    console.log("✅ Sistema de vendas e pagamentos");
    console.log("✅ Dashboard administrativo");
    console.log("✅ Configurações avançadas");

  } catch (error) {
    console.error("❌ Erro durante o seed:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar seed
seedAdmin();
