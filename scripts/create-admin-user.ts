import { PrismaClient, UserRole } from "@prisma/client";
import { readFileSync } from "fs";
import { join } from "path";

// Carregar variáveis de ambiente do arquivo .env
const envFile = readFileSync(join(process.cwd(), '.env'), 'utf8');
const envVars = envFile.split('\n').reduce((acc, line) => {
  const equalIndex = line.indexOf('=');
  if (equalIndex > 0) {
    const key = line.substring(0, equalIndex).trim();
    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
    if (key && value) {
      acc[key] = value;
    }
  }
  return acc;
}, {} as Record<string, string>);

// Definir variáveis de ambiente
Object.entries(envVars).forEach(([key, value]) => {
  process.env[key] = value;
});

console.log('DATABASE_URL configurada:', process.env.DATABASE_URL ? 'Sim' : 'Não');

const db = new PrismaClient();

async function createAdminUser() {
  try {
    console.log("🚀 Criando usuário admin...");

    // Dados do usuário admin
    const adminData = {
      name: "Admin SupGateway",
      email: "<EMAIL>",
      username: "admin",
      role: "SUPER_ADMIN" as any,
      emailVerified: true,
      onboardingComplete: true,
    };

    // Verificar se o usuário já existe
    const existingUser = await db.user.findFirst({
      where: {
        OR: [
          { email: adminData.email },
          { username: adminData.username },
        ],
      },
    });

    if (existingUser) {
      console.log("⚠️  Usuário admin já existe!");
      console.log(`📧 Email: ${existingUser.email}`);
      console.log(`👤 Username: ${existingUser.username}`);
      console.log(`🔑 Role: ${existingUser.role}`);
      return;
    }

    // Criar o usuário admin
    const adminUser = await db.user.create({
      data: adminData,
    });

    console.log("✅ Usuário admin criado com sucesso!");
    console.log(`🆔 ID: ${adminUser.id}`);
    console.log(`📧 Email: ${adminUser.email}`);
    console.log(`👤 Username: ${adminUser.username}`);
    console.log(`🔑 Role: ${adminUser.role}`);

    // Criar uma organização padrão para o admin
    const organization = await db.organization.create({
      data: {
        name: "SupGateway Admin",
        slug: "supgateway-admin",
        subscriptionPlan: "enterprise",
        subscriptionStatus: "active",
        enableAffiliatePogram: true,
        enableDigitalProducts: true,
        enableCertificates: true,
        enableCustomBranding: true,
        enableCustomDomain: true,
        enableAdvancedPayments: true,
        maxProducts: 999999,
        maxUsers: 999999,
        maxStorageGB: 999999,
        maxTransactions: 999999,
        members: {
          create: {
            userId: adminUser.id,
            role: "OWNER",
            createdAt: new Date(),
          },
        },
      },
    });

    console.log("🏢 Organização admin criada!");
    console.log(`🏢 ID: ${organization.id}`);
    console.log(`🏢 Nome: ${organization.name}`);
    console.log(`🏢 Slug: ${organization.slug}`);

    // Criar perfil de professor para o admin
    await db.teacherProfile.create({
      data: {
        userId: adminUser.id,
        bio: "Administrador do SupGateway",
        expertise: ["Plataforma", "Gestão", "Desenvolvimento"],
        experience: "Experiência completa na plataforma",
        isVerified: true,
      },
    });

    console.log("👨‍🏫 Perfil de professor criado!");

    console.log("\n🎉 Setup completo!");
    console.log("📝 Próximos passos:");
    console.log("1. Acesse o sistema com o email: <EMAIL>");
    console.log("2. Use a funcionalidade de 'Esqueci minha senha' para definir uma senha");
    console.log("3. Ou configure autenticação via OAuth (Google, GitHub, etc.)");

  } catch (error) {
    console.error("❌ Erro ao criar usuário admin:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
createAdminUser();
