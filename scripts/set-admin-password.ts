#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";
import { auth } from "@repo/auth";

const db = new PrismaClient();

async function setAdminPassword() {
  console.log("🔐 Definindo senha para o admin...\n");

  try {
    // Buscar o usuário admin
    const adminUser = await db.user.findFirst({
      where: { email: "<EMAIL>" }
    });

    if (!adminUser) {
      console.error("❌ Usuário admin não encontrado!");
      process.exit(1);
    }

    // Definir senha
    const adminPassword = "admin123456";
    const authContext = await auth.$context;
    const hashedPassword = await authContext.password.hash(adminPassword);

    // Criar ou atualizar conta de credenciais
    await db.account.upsert({
      where: {
        providerId_accountId: {
          providerId: "credential",
          accountId: adminUser.id,
        },
      },
      update: {
        password: hashedPassword,
        updatedAt: new Date(),
      },
      create: {
        userId: adminUser.id,
        providerId: "credential",
        accountId: adminUser.id,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    console.log("✅ Senha definida com sucesso!");
    console.log("\n🔐 INFORMAÇÕES DE LOGIN:");
    console.log("==========================");
    console.log(`📧 Email: <EMAIL>`);
    console.log(`🔑 Senha: ${adminPassword}`);
    console.log(`🌐 URL: http://localhost:3001/auth/login`);
    console.log(`👤 Role: SUPER_ADMIN`);

  } catch (error) {
    console.error("❌ Erro ao definir senha:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Executar
setAdminPassword();
